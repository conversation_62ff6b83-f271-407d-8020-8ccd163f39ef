<template>
  <div class="school-assistant-page max-w-7xl mx-auto">
    <!-- 面包屑导航占位 -->
    <div class="mb-6">
      <!-- <Breadcrumb /> -->
    </div>

    <!-- 页面标题区域 -->
    <div class="mb-6">
      <h2 class="text-xl font-medium">智能选校</h2>
      <p class="mt-1 text-sm text-gray-500">通过填写学生信息，获取个性化院校推荐</p>
    </div>

    <!-- 主要内容区域 -->
    <div class="flex flex-col lg:flex-row gap-6">
      <!-- 左侧：信息输入表单 -->
      <div class="lg:w-88 flex-shrink-0">
        <div class="pro-card">
          <!-- 表单标题 -->
          <div class="pro-card-header">
            <h3 class="pro-card-title">填写申请信息</h3>
          </div>

          <!-- 表单内容 -->
          <form @submit.prevent="handleSubmit" class="divide-y divide-gray-100">
            <!-- 学术背景 -->
            <div class="px-4 py-3">
              <div 
                class="flex items-center space-x-2 mb-2 cursor-pointer"
                @click="toggleSection('academic')"
              >
                <span class="material-icons-outlined text-[#4F46E5] text-lg">school</span>
                <h4 class="text-sm font-semibold text-gray-600">学术背景</h4>
                <span class="material-icons-outlined text-gray-400 text-sm transition-transform duration-300" 
                  :class="sectionStates.academic ? 'rotate-180' : ''">
                  expand_more
                </span>
              </div>
              
              <div 
                class="space-y-2 overflow-hidden form-section-content" 
                :style="{ 
                  maxHeight: sectionStates.academic ? '1000px' : '0px', 
                  opacity: sectionStates.academic ? 1 : 0,
                  visibility: sectionStates.academic ? 'visible' : 'hidden'
                }"
              >
                <!-- 使用动画输入组件的表单项 -->
                <AnimatedInput
                  v-model="profileForm.academic.education"
                  label="当前/最高学历"
                  placeholder="请选择学历状态"
                  type="select"
                  :options="[
                    { label: '高中', value: 'high_school' },
                    { label: '本科', value: 'undergraduate' },
                    { label: '硕士', value: 'master' },
                    { label: '博士', value: 'phd' }
                  ]"
                  required
                  @change="checkSectionCompletion('academic')"
                />
              
                <AnimatedInput
                  v-model="profileForm.academic.school"
                  label="就读/毕业院校"
                  placeholder="请输入院校名称"
                  type="autocomplete"
                  required
                  :fetchSuggestions="querySchools"
                  @input="checkSectionCompletion('academic')"
                />
              
                <AnimatedInput
                  v-model="profileForm.academic.major"
                  label="专业"
                  placeholder="请输入专业名称"
                  type="autocomplete"
                  required
                  :fetchSuggestions="queryMajors"
                  @input="checkSectionCompletion('academic')"
                />
              
                <div class="grid grid-cols-2 gap-x-4 mb-0">
                  <AnimatedInput
                    v-model="profileForm.academic.gpa"
                    label="GPA/平均分"
                    placeholder="请输入GPA或平均分"
                    type="input"
                    required
                    @input="checkSectionCompletion('academic')"
                  />
                  
                  <AnimatedInput
                    v-model="profileForm.academic.gpaScale"
                    label="GPA制式/总分"
                    placeholder="请选择GPA计算制度"
                    type="select"
                    :options="[
                      { label: '4.0制', value: '4.0' },
                      { label: '5.0制', value: '5.0' },
                      { label: '百分制', value: '100' }
                    ]"
                    required
                    @change="checkSectionCompletion('academic')"
                  />
                </div>
              
                <AnimatedInput
                  v-model="profileForm.academic.ranking"
                  label="班级排名"
                  placeholder="请选择班级排名"
                  type="select"
                  :options="[
                    { label: '前5%', value: '5%' },
                    { label: '前10%', value: '10%' },
                    { label: '前20%', value: '20%' },
                    { label: '前50%', value: '50%' }
                  ]"
                  @change="checkSectionCompletion('academic')"
                />
              </div>
            </div>

            <!-- 留学意向 - 优化布局 -->
            <div class="px-4 py-3">
              <div 
                class="flex items-center space-x-2 mb-2 cursor-pointer"
                @click="toggleSection('intention')"
              >
                <span class="material-icons-outlined text-[#4F46E5] text-lg">flight_takeoff</span>
                <h4 class="text-sm font-semibold text-gray-600">留学意向</h4>
                <span class="material-icons-outlined text-gray-400 text-sm transition-transform duration-300" 
                  :class="sectionStates.intention ? 'rotate-180' : ''">
                  expand_more
                </span>
              </div>
              
              <div 
                class="space-y-2 overflow-hidden form-section-content" 
                :style="{ 
                  maxHeight: sectionStates.intention ? '1000px' : '0px', 
                  opacity: sectionStates.intention ? 1 : 0,
                  visibility: sectionStates.intention ? 'visible' : 'hidden'
                }"
              >
                <AnimatedInput
                  v-model="profileForm.intention.countries"
                  label="意向国家/地区"
                  placeholder="请选择意向的国家或地区（可多选）"
                  type="select"
                  :options="[
                    { label: '中国香港', value: 'Hong Kong' },
                    { label: '新加坡', value: 'Singapore' },
                    { label: '英国', value: 'UK' },
                    { label: '美国', value: 'USA' },
                    { label: '澳大利亚', value: 'Australia' },
                    { label: '中国澳门', value: 'Macau' },
                    { label: '马来西亚', value: 'Malaysia' }
                  ]"
                  multiple
                  required
                  @change="checkSectionCompletion('intention')"
                />
              
                <AnimatedInput
                  v-model="profileForm.intention.majors"
                  label="意向专业/领域"
                  placeholder="请选择意向专业领域（可多选）"
                  type="select"
                  :options="[
                    { label: '商科领域', value: 'business_field', secondaryText: '领域' },
                    { label: '金工金数', value: 'financial_engineering', secondaryText: '商科领域' },
                    { label: '金融', value: 'finance', secondaryText: '商科领域' },
                    { label: '商业分析', value: 'business_analytics', secondaryText: '商科领域' },
                    { label: '经济', value: 'economics', secondaryText: '商科领域' },
                    { label: '会计', value: 'accounting', secondaryText: '商科领域' },
                    { label: '市场营销', value: 'marketing', secondaryText: '商科领域' },
                    { label: '信息系统', value: 'information_systems', secondaryText: '商科领域' },
                    { label: '管理', value: 'management', secondaryText: '商科领域' },
                    { label: '人力资源管理', value: 'hrm', secondaryText: '商科领域' },
                    { label: '供应链管理', value: 'scm', secondaryText: '商科领域' },
                    { label: '创业与创新', value: 'entrepreneurship', secondaryText: '商科领域' },
                    { label: '房地产', value: 'real_estate', secondaryText: '商科领域' },
                    { label: '旅游酒店管理', value: 'hospitality', secondaryText: '商科领域' },
                    { label: '工商管理', value: 'business_admin', secondaryText: '商科领域' },
                    { label: '其他商科', value: 'other_business', secondaryText: '商科领域' },
                    { label: '社科领域', value: 'social_science_field', secondaryText: '领域' },
                    { label: '教育', value: 'education', secondaryText: '社科领域' },
                    { label: '建筑', value: 'architecture', secondaryText: '社科领域' },
                    { label: '法律', value: 'law', secondaryText: '社科领域' },
                    { label: '社会学与社工', value: 'sociology', secondaryText: '社科领域' },
                    { label: '国际关系', value: 'international_relations', secondaryText: '社科领域' },
                    { label: '哲学', value: 'philosophy', secondaryText: '社科领域' },
                    { label: '历史', value: 'history', secondaryText: '社科领域' },
                    { label: '公共政策与事务', value: 'public_policy', secondaryText: '社科领域' },
                    { label: '艺术', value: 'arts', secondaryText: '社科领域' },
                    { label: '公共卫生', value: 'public_health', secondaryText: '社科领域' },
                    { label: '心理学', value: 'psychology', secondaryText: '社科领域' },
                    { label: '体育', value: 'sports', secondaryText: '社科领域' },
                    { label: '药学', value: 'pharmacy', secondaryText: '社科领域' },
                    { label: '医学', value: 'medicine', secondaryText: '社科领域' },
                    { label: '新闻', value: 'journalism', secondaryText: '社科领域' },
                    { label: '影视', value: 'film', secondaryText: '社科领域' },
                    { label: '文化', value: 'culture', secondaryText: '社科领域' },
                    { label: '媒体与传播', value: 'media_communication', secondaryText: '社科领域' },
                    { label: '新媒体', value: 'new_media', secondaryText: '社科领域' },
                    { label: '媒介与社会', value: 'media_society', secondaryText: '社科领域' },
                    { label: '科学传播', value: 'science_communication', secondaryText: '社科领域' },
                    { label: '策略传播', value: 'strategic_communication', secondaryText: '社科领域' },
                    { label: '媒体产业', value: 'media_industry', secondaryText: '社科领域' },
                    { label: '语言', value: 'language', secondaryText: '社科领域' },
                    { label: '其他社科', value: 'other_social_science', secondaryText: '社科领域' },
                    { label: '工科领域', value: 'engineering_field', secondaryText: '领域' },
                    { label: '计算机', value: 'computer_science', secondaryText: '工科领域' },
                    { label: '电气电子', value: 'electrical_engineering', secondaryText: '工科领域' },
                    { label: '机械工程', value: 'mechanical_engineering', secondaryText: '工科领域' },
                    { label: '材料', value: 'materials', secondaryText: '工科领域' },
                    { label: '化工', value: 'chemical_engineering', secondaryText: '工科领域' },
                    { label: '生物工程', value: 'bioengineering', secondaryText: '工科领域' },
                    { label: '土木工程', value: 'civil_engineering', secondaryText: '工科领域' },
                    { label: '工程管理', value: 'engineering_management', secondaryText: '工科领域' },
                    { label: '环境工程', value: 'environmental_engineering', secondaryText: '工科领域' },
                    { label: '工业工程', value: 'industrial_engineering', secondaryText: '工科领域' },
                    { label: '能源', value: 'energy', secondaryText: '工科领域' },
                    { label: '航空工程', value: 'aerospace', secondaryText: '工科领域' },
                    { label: '地球科学', value: 'earth_science', secondaryText: '工科领域' },
                    { label: '交通运输', value: 'transportation', secondaryText: '工科领域' },
                    { label: '海洋技术', value: 'marine_technology', secondaryText: '工科领域' },
                    { label: '食品科学', value: 'food_science', secondaryText: '工科领域' },
                    { label: '其他工科', value: 'other_engineering', secondaryText: '工科领域' },
                    { label: '理科领域', value: 'science_field', secondaryText: '领域' },
                    { label: '物理', value: 'physics', secondaryText: '理科领域' },
                    { label: '化学', value: 'chemistry', secondaryText: '理科领域' },
                    { label: '数学', value: 'mathematics', secondaryText: '理科领域' },
                    { label: '生物', value: 'biology', secondaryText: '理科领域' },
                    { label: '数据科学', value: 'data_science', secondaryText: '理科领域' }
                  ]"
                  multiple
                  required
                  @change="checkSectionCompletion('intention')"
                />
                <!-- End of Selection -->
              
                <div class="grid grid-cols-2 gap-x-4 mb-0">
                  <AnimatedInput
                    v-model="profileForm.intention.startYear"
                    label="期望入学年份"
                    placeholder="请选择入学的学期"
                    type="select"
                    :options="[
                      { label: '2025Fall', value: '2025-fall' },
                      { label: '2026Fall', value: '2026-fall' },
                      { label: '2027Fall', value: '2027-fall' }
                    ]"
                    required
                    @change="checkSectionCompletion('intention')"
                  />
                  
                  <AnimatedInput
                    v-model="profileForm.intention.duration"
                    label="期望项目时长"
                    placeholder="请选择学制长度"
                    type="select"
                    :options="[
                      { label: '1年', value: '1' },
                      { label: '2年', value: '2' },
                      { label: '3年', value: '3' },
                      { label: '4年', value: '4' }
                    ]"
                    @change="checkSectionCompletion('intention')"
                  />
                </div>
              </div>
            </div>

            <!-- 软实力与偏好 -->
            <div class="px-4 py-3">
              <div 
                class="flex items-center space-x-2 mb-2 cursor-pointer"
                @click="toggleSection('strength')"
              >
                <span class="material-icons-outlined text-[#4F46E5] text-lg">stars</span>
                <h4 class="text-sm font-semibold text-gray-600">软实力与偏好</h4>
                <span class="text-xs text-gray-500">(可选)</span>
                <span class="material-icons-outlined text-gray-400 text-sm transition-transform duration-300" 
                  :class="sectionStates.strength ? 'rotate-180' : ''">
                  expand_more
                </span>
              </div>
              
              <div 
                class="space-y-2 overflow-hidden form-section-content" 
                :style="{ 
                  maxHeight: sectionStates.strength ? '1000px' : '0px', 
                  opacity: sectionStates.strength ? 1 : 0,
                  visibility: sectionStates.strength ? 'visible' : 'hidden' 
                }"
              >
                <AnimatedInput
                  v-model="profileForm.strength.competition"
                  label="竞赛经历"
                  placeholder="请选择获得的竞赛奖项级别"
                  type="select"
                  :options="[
                    { label: '省级', value: 'provincial' },
                    { label: '国家级', value: 'national' },
                    { label: '国际级', value: 'international' }
                  ]"
                  multiple
                />
              
                <AnimatedInput
                  v-model="profileForm.strength.internship"
                  label="实习经历"
                  placeholder="请选择实习经历类型"
                  type="select"
                  :options="[
                    { label: '互联网大厂', value: 'internet' },
                    { label: '头部金融机构', value: 'finance' },
                    { label: '4A', value: '4a' },
                    { label: '四大会计事务所', value: 'big4' },
                    { label: '世界500强', value: 'fortune500' },
                    { label: '上市公司', value: 'listed' },
                    { label: '其他', value: 'other' }
                  ]"
                  multiple
                />
              
                <AnimatedInput
                  v-model="profileForm.strength.research"
                  label="科研经历"
                  placeholder="请选择科研成果类型"
                  type="select"
                  :options="[
                    { label: '普通刊物发表论文', value: 'normal-paper' },
                    { label: '核心刊物发表论文', value: 'core-paper' },
                    { label: 'SCI刊物发表论文', value: 'sci-paper' },
                    { label: '实用新型专利', value: 'utility-patent' },
                    { label: '发明专利', value: 'invention-patent' }
                  ]"
                  multiple
                />
              
                <AnimatedInput
                  v-model="profileForm.strength.rankingPreference"
                  label="院校排名偏好"
                  placeholder="请选择排名范围"
                  type="select"
                  :options="[
                    { label: 'QS Top 10', value: 'QS-10' },
                    { label: 'QS Top 50', value: 'QS-50' },
                    { label: 'QS Top 100', value: 'QS-100' }
                  ]"
                  multiple
                />
              </div>
            </div>

            <!-- 提交按钮 -->
            <div class="pro-card-footer bg-gray-50">
              <el-button 
                type="primary" 
                @click="handleSubmit"
                :loading="isLoading"
                class="!bg-[#4F46E5] !border-[#4F46E5] !h-7 !px-3 !text-xs"
              >
                <span class="material-icons-outlined mr-1 !text-xs">send</span>
                生成推荐
              </el-button>
            </div>
          </form>
        </div>
      </div>
      
      <!-- 右侧：推荐结果展示 -->
      <div class="flex-1">
        <div class="pro-card">
          <div class="pro-card-header flex justify-between items-center">
            <h2 class="pro-card-title">推荐院校</h2>
            
            <!-- 筛选和排序选项 -->
            <div v-if="hasSubmitted && recommendations.length > 0" class="flex space-x-3">
              <!-- 匹配度筛选 -->
              <el-select v-model="filterLevel" placeholder="筛选匹配度" size="small" class="w-28">
                <template #prefix>
                  <span class="material-icons-outlined text-gray-400 text-sm">filter_list</span>
                </template>
                <el-option label="全部" value="" />
                <el-option label="冲刺院校" value="冲" />
                <el-option label="稳妥院校" value="稳" />
                <el-option label="保底院校" value="保" />
              </el-select>
              
              <!-- 排序方式 -->
              <el-select v-model="sortBy" placeholder="排序方式" size="small" class="w-28">
                <template #prefix>
                  <span class="material-icons-outlined text-gray-400 text-sm">sort</span>
                </template>
                <el-option label="匹配度↓" value="matchScore-desc" />
                <el-option label="匹配度↑" value="matchScore-asc" />
                <el-option label="院校排名↓" value="ranking-desc" />
                <el-option label="院校排名↑" value="ranking-asc" />
              </el-select>
            </div>
          </div>
          
          <!-- 推荐结果内容区域 -->
          <div class="pro-card-body">
            <!-- 空状态展示 -->
            <div v-if="!hasSubmitted" class="py-6">
              <!-- 顶部说明区域 -->
              <div class="flex flex-col items-center justify-center py-0">
                <div class="relative">
                  <!-- 背景装饰 -->
                  <div class="absolute inset-0 bg-gradient-to-br from-[#4F46E5]/10 to-[#7C3AED]/10 rounded-full blur-3xl animate-pulse"></div>
                  
                  <!-- 图标容器 -->
                  <div class="relative bg-gradient-to-br from-[#4F46E5]/5 to-[#7C3AED]/5 p-8 rounded-2xl">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-[#4F46E5] mx-auto animate-float" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                  </div>
                </div>
                
                <div class="text-center mb-6">
                  <h3 class="text-base font-semibold text-gray-800 mb-2">智能选校，精准推荐</h3>
                  <p class="text-sm text-gray-500">填写学生的学术背景和留学意向，我们将为您匹配最适合的院校方案</p>
                </div>

                <!-- 底部统计信息 -->
                <div class="pt-6 border-t border-gray-100">
                  <div class="grid grid-cols-3 gap-28 text-center">
                    <div>
                      <div class="text-xl font-bold text-[#4F46E5]">11,000+</div>
                      <div class="text-xs text-gray-500">院校数据</div>
                    </div>
                    <div>
                      <div class="text-xl font-bold text-[#4F46E5]">15,0000+</div>
                      <div class="text-xs text-gray-500">录取案例</div>
                    </div>
                    <div>
                      <div class="text-xl font-bold text-[#4F46E5]">95%</div>
                      <div class="text-xs text-gray-500">匹配准确率</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 推荐结果列表 -->
            <div v-else class="space-y-4">
              <p v-if="filteredRecommendations.length === 0" class="text-gray-500 text-center py-8">
                {{ filterLevel ? '没有符合当前筛选条件的推荐结果' : '暂无推荐结果' }}
              </p>

              <!-- 学校推荐卡片 - 新布局 -->
              <div 
                v-for="(school, index) in filteredRecommendations" 
                :key="index" 
                class="school-card relative border border-gray-200 rounded-xl overflow-hidden bg-white shadow-sm hover:shadow-md transition-all duration-300"
                :data-school-id="school.id"
              >
                <!-- 卡片头部：学校基本信息 -->
                <div class="px-4 py-3 bg-gradient-to-r from-gray-50 via-white to-gray-50 border-b border-gray-100">
                  <div class="flex items-start">
                    <!-- 学校Logo区域 -->
                    <div class="flex-shrink-0 w-14 h-14 bg-white rounded-lg flex items-center justify-center overflow-hidden mr-4 border border-gray-100 shadow-sm">
                      <img 
                        :src="getSchoolLogo(school)"
                        :alt="school.学校中文名"
                        class="h-10 w-10 object-contain"
                        @error="handleLogoError($event, school)"
                      />
                    </div>

                    <!-- 学校名称和基本信息 -->
                    <div class="flex-grow">
                      <h3 class="text-base font-semibold text-gray-800">{{ school.学校中文名 }} - {{ school.专业中文名 }}</h3>
                      <p class="text-xs text-gray-500 mt-0.5">{{ school.学校英文名 }}</p>
                      <p class="text-xs text-gray-600 mt-1 flex items-center">
                        <span class="material-icons-outlined text-gray-400 text-xs mr-1">location_on</span>
                        {{ school.location }}
                      </p>
                    </div>

                    <!-- 评分和匹配等级 -->
                    <div class="flex items-center space-x-3">
                      <!-- 学校排名 -->
                      <div class="bg-white rounded-full h-14 w-14 flex items-center justify-center border border-gray-200 shadow-sm flex-col">
                        <span class="text-lg font-bold" 
                          :class="{
                            'text-red-600': school.matchScore < 70,
                            'text-amber-500': school.matchScore >= 70 && school.matchScore < 85,
                            'text-emerald-600': school.matchScore >= 85,
                          }">{{ school.matchScore }}</span>
                        <span class="text-xs text-gray-500">匹配度</span>
                      </div>
                      
                      <!-- 匹配等级标签 -->
                      <span class="px-2.5 py-0.5 text-xs rounded-full mt-1"
                        :class="{
                          'bg-red-50 text-red-600 border border-red-100': school.matchLevel === '冲',
                          'bg-amber-50 text-amber-600 border border-amber-100': school.matchLevel === '稳',
                          'bg-emerald-50 text-emerald-600 border border-emerald-100': school.matchLevel === '保',
                        }"
                      >
                        {{ school.matchLevel }}
                      </span>
                    </div>
                  </div>
                </div>

                <!-- 卡片主体内容：专业信息 -->
                <div class="px-4 py-3">
                  <!-- 申请信息 -->
                  <div class="grid grid-cols-3 gap-x-3 gap-y-2 text-xs">
                    <div class="flex flex-col">
                      <span class="text-gray-400 text-xs mb-0.5">专业大类</span> 
                      <span class="font-medium text-gray-700">{{ school.专业大类 }}</span>
                    </div>
                    <div class="flex flex-col">
                      <span class="text-gray-400 text-xs mb-0.5">专业方向</span> 
                      <span class="font-medium text-gray-700">{{ school.专业方向 }}</span>
                    </div>
                    <div class="flex flex-col">
                      <span class="text-gray-400 text-xs mb-0.5">所在学院</span> 
                      <span class="font-medium text-gray-700">{{ school.所在学院 }}</span>
                    </div>
                    <div class="flex flex-col">
                      <span class="text-gray-400 text-xs mb-0.5">入学时间</span> 
                      <span class="font-medium text-gray-700">{{ school.入学时间 }}</span>
                    </div>
                    <div class="flex flex-col">
                      <span class="text-gray-400 text-xs mb-0.5">项目时长</span> 
                      <span class="font-medium text-gray-700">{{ school.项目时长 }}</span>
                    </div>
                    <div class="flex flex-col">
                      <span class="text-gray-400 text-xs mb-0.5">学费</span> 
                      <span class="font-medium text-gray-700">{{ school.tuitionRange }}</span>
                    </div>
                    <div class="flex flex-col col-span-2">
                      <span class="text-gray-400 text-xs mb-0.5">申请截止</span> 
                      <span class="font-medium text-gray-700">{{ school.deadline }}</span>
                    </div>
                    <div class="flex flex-col">
                      <span class="text-gray-400 text-xs mb-0.5">排名</span> 
                      <span class="font-medium text-gray-700">{{ school.ranking }}</span>
                    </div>
                  </div>

                  <!-- 亮点标签 -->
                  <div class="mt-3 flex flex-wrap gap-1.5">
                    <span 
                      v-for="(highlight, hIndex) in school.highlights" 
                      :key="hIndex"
                      class="inline-flex items-center px-2 py-0.5 text-xs font-medium rounded-full bg-gray-50 text-gray-600 border border-gray-100"
                    >
                      {{ highlight }}
                    </span>
                  </div>
                </div>

                <!-- 操作按钮区域 -->
                <div class="px-4 py-2.5 bg-gray-50 border-t border-gray-100 flex justify-between items-center">
                  <a 
                    v-if="school.项目官网" 
                    :href="school.项目官网" 
                    target="_blank" 
                    class="text-indigo-600 text-xs flex items-center group"
                  >
                    <span class="material-icons-outlined mr-1 !text-sm">open_in_new</span>
                    <span class="group-hover:underline">查看官网</span>
                  </a>
                  <div class="flex items-center space-x-2">
                    <el-button 
                      type="primary"
                      @click="toggleDetails(school)"
                      class="!bg-indigo-600 !border-indigo-600 hover:!bg-indigo-700 !h-7 !text-xs !rounded-md !px-2.5 !py-1 !transition-colors"
                    >
                      <span class="material-icons-outlined mr-1 !text-xs">{{ school.showDetails ? 'expand_less' : 'expand_more' }}</span>
                      <span class="!text-xs">{{ school.showDetails ? '收起详情' : '查看详情' }}</span>
                    </el-button>
                    <el-button
                      :class="school.isFavorite ? '!text-indigo-600 !h-7 !text-xs !rounded-md !px-2.5' : '!h-7 !text-xs !rounded-md !px-2.5'"
                      @click="toggleFavorite(school)"
                    >
                      <span class="material-icons-outlined mr-1 !text-xs">{{ school.isFavorite ? 'favorite' : 'favorite_border' }}</span>
                      <span class="!text-xs">{{ school.isFavorite ? '已收藏' : '收藏' }}</span>
                    </el-button>
                  </div>
                </div>

                <!-- 详细信息区域 -->
                <div 
                  class="details-container w-full overflow-hidden" 
                  :style="{ 
                    height: school.detailsHeight + 'px', 
                    opacity: school.showDetails ? 1 : 0,
                    transition: 'height 0.4s ease, opacity 0.4s ease',
                    transitionDelay: school.showDetails ? '0s' : '0s'
                  }"
                >
                  <div class="details-content p-4 bg-gray-50 border-t border-gray-100">
                    <!-- 详情信息，分类显示 -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-3">
                      <!-- 申请要求 -->
                      <div class="bg-white p-3 rounded-lg border border-gray-100 shadow-sm">
                        <h4 class="text-xs font-semibold mb-1.5 text-gray-800 flex items-center">
                          <span class="material-icons-outlined text-gray-400 text-sm mr-1">assignment</span>
                          申请要求
                        </h4>
                        <p class="text-xs text-gray-600 leading-relaxed">{{ school.申请要求 }}</p>
                      </div>
                      
                      <!-- 语言要求 -->
                      <div class="bg-white p-3 rounded-lg border border-gray-100 shadow-sm">
                        <h4 class="text-xs font-semibold mb-1.5 text-gray-800 flex items-center">
                          <span class="material-icons-outlined text-gray-400 text-sm mr-1">translate</span>
                          语言要求
                        </h4>
                        <p class="text-xs text-gray-600 leading-relaxed">{{ school.语言要求 }}</p>
                      </div>
                      
                      <!-- 项目培养目标 -->
                      <div class="bg-white p-3 rounded-lg border border-gray-100 shadow-sm lg:col-span-2">
                        <h4 class="text-xs font-semibold mb-1.5 text-gray-800 flex items-center">
                          <span class="material-icons-outlined text-gray-400 text-sm mr-1">lightbulb</span>
                          培养目标
                        </h4>
                        <p class="text-xs text-gray-600 leading-relaxed">{{ school.培养目标 }}</p>
                      </div>
                      
                      <!-- 课程设置 -->
                      <div class="bg-white p-3 rounded-lg border border-gray-100 shadow-sm lg:col-span-2">
                        <h4 class="text-xs font-semibold mb-1.5 text-gray-800 flex items-center">
                          <span class="material-icons-outlined text-gray-400 text-sm mr-1">school</span>
                          课程设置
                        </h4>
                        <p class="text-xs text-gray-600 leading-relaxed">{{ school.课程设置 }}</p>
                      </div>
                      
                      <!-- 推荐理由 -->
                      <div class="bg-white p-3 rounded-lg border border-gray-100 shadow-sm lg:col-span-2">
                        <h4 class="text-xs font-semibold mb-1.5 text-gray-800 flex items-center">
                          <span class="material-icons-outlined text-gray-400 text-sm mr-1">thumb_up</span>
                          推荐理由
                        </h4>
                        <p class="text-xs text-gray-600 leading-relaxed">{{ school.reason }}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage, ElLoading } from 'element-plus';
import AnimatedInput from '@/components/common/AnimatedInput.vue';

/**
 * 智能选校助手组件
 * 
 * 该组件提供了一个交互式界面，让教育工作者输入学生信息，
 * 获取个性化的院校推荐，辅助教育工作者为学生提供精准的留学规划指导。
 * 
 * 主要功能：
 * 1. 多步骤表单收集学生关键信息（基本信息、学术背景、留学意向、软实力）
 * 2. 基于学生数据生成个性化院校推荐
 * 3. 推荐结果可筛选和排序
 * 4. 展示院校匹配度和推荐理由
 */

// 创建响应式状态
const isLoading = ref(false);
const activeTab = ref('basic'); // 控制表单标签页
const filterLevel = ref(''); // 筛选匹配度
const sortBy = ref('matchScore-asc'); // 排序方式，默认为匹配度正序

// 表单折叠状态控制
const sectionStates = reactive({
  academic: true,  // 默认展开学术背景
  intention: true, // 默认展开留学意向
  strength: false  // 默认折叠软实力与偏好
});

// 表单完成状态追踪
const sectionCompletion = reactive({
  academic: false,
  intention: false,
  strength: false  // 软实力部分现在也需要检查，默认未完成
});

const userHasInteractedWithSection = reactive({
  academic: false,
  intention: false,
  strength: false
});

// 切换表单区域展开/折叠状态
const toggleSection = (section) => {
  const originallyCollapsed = !sectionStates[section];
  sectionStates[section] = !sectionStates[section];

  // 如果用户手动展开了一个原本是折叠的区域
  if (sectionStates[section] && originallyCollapsed) {
    userHasInteractedWithSection[section] = true;
  }
  // 如果用户手动折叠，则重置交互状态，允许后续的自动折叠/展开
  if (!sectionStates[section]) {
    userHasInteractedWithSection[section] = false;
  }
};

// 自动检查表单区域完成状态
const checkSectionCompletion = (section) => {
  if (section === 'academic') {
    const { education, school, major, gpa, gpaScale, ranking } = profileForm.academic;
    const isComplete = !!education && !!school && !!major && !!gpa && !!gpaScale && !!ranking;
    
    if (!isComplete && sectionCompletion.academic) {
      // 如果从完成变为未完成，重置用户交互状态，允许将来再次自动折叠
      userHasInteractedWithSection.academic = false;
    }
    sectionCompletion.academic = isComplete;
    
    if (isComplete && sectionStates.academic && !userHasInteractedWithSection.academic) { 
      setTimeout(() => { 
        sectionStates.academic = false; 
        if (!sectionCompletion.intention && !userHasInteractedWithSection.intention) {
            sectionStates.intention = true;
        }
      }, 300); 
    }
  } else if (section === 'intention') {
    const { countries, majors, startYear, duration } = profileForm.intention;
    const isComplete = countries.length > 0 && majors.length > 0 && !!startYear && !!duration;

    if (!isComplete && sectionCompletion.intention) {
      userHasInteractedWithSection.intention = false;
    }
    sectionCompletion.intention = isComplete;
    
    if (isComplete && sectionStates.intention && !userHasInteractedWithSection.intention) { 
      setTimeout(() => { 
        sectionStates.intention = false;
        if (!sectionCompletion.strength && !userHasInteractedWithSection.strength) {
          sectionStates.strength = true;
        } 
      }, 300);
    }
  } else if (section === 'strength') {
    const { competition, internship, research, rankingPreference } = profileForm.strength;
    const isComplete = 
      (Array.isArray(competition) ? competition.length > 0 : !!competition) &&
      (Array.isArray(internship) ? internship.length > 0 : !!internship) &&
      (Array.isArray(research) ? research.length > 0 : !!research) &&
      (Array.isArray(rankingPreference) ? rankingPreference.length > 0 : !!rankingPreference);

    if (!isComplete && sectionCompletion.strength) {
      userHasInteractedWithSection.strength = false;
    }
    sectionCompletion.strength = isComplete;

    if (isComplete && sectionStates.strength && !userHasInteractedWithSection.strength) {
      setTimeout(() => {
        sectionStates.strength = false;
      }, 300);
    }
  }
};

// 表单标签页配置
const tabs = [
  { key: 'basic', name: '基本信息' },
  { key: 'academic', name: '学术背景' },
  { key: 'intention', name: '留学意向' },
  { key: 'strength', name: '软实力与偏好' },
];

// 表单数据
const profileForm = reactive({
  // 学术背景
  academic: {
    education: '',
    school: '',
    major: '',
    gpa: '',
    gpaScale: '',
    ranking: '',
  },
  // 留学意向
  intention: {
    countries: [],
    majors: [],
    startYear: '',
    duration: '',
  },
  // 软实力与偏好
  strength: {
    research: '',
    internship: '',
    papers: '',
    rankingPreference: []
  }
});

// 推荐结果
const recommendations = ref([]);
const hasSubmitted = ref(false);

/**
 * 添加获取logo的方法
 */
const getSchoolLogo = (school) => {
  // 方案1: 使用学校官方域名获取favicon（最可靠）
  const domainMapping = {
    '麻省理工学院': 'mit.edu',
    '新加坡国立大学': 'nus.edu.sg',
    '香港大学': 'hku.hk',
    '帝国理工学院': 'imperial.ac.uk',
    '伦敦大学学院': 'ucl.ac.uk',
    '哈佛大学': 'harvard.edu',
    '斯坦福大学': 'stanford.edu',
    '剑桥大学': 'cam.ac.uk',
    '牛津大学': 'ox.ac.uk',
    '清华大学': 'tsinghua.edu.cn',
    '北京大学': 'pku.edu.cn',
    '复旦大学': 'fudan.edu.cn',
    '上海交通大学': 'sjtu.edu.cn',
    '浙江大学': 'zju.edu.cn',
    '南京大学': 'nju.edu.cn',
    '中国人民大学': 'ruc.edu.cn',
    '北京航空航天大学': 'buaa.edu.cn',
    '同济大学': 'tongji.edu.cn',
    '天津大学': 'tju.edu.cn',
    '华中科技大学': 'hust.edu.cn',
    '西安交通大学': 'xjtu.edu.cn',
    '中山大学': 'sysu.edu.cn',
    '哈尔滨工业大学': 'hit.edu.cn',
    '武汉大学': 'whu.edu.cn',
    '东南大学': 'seu.edu.cn',
    '中南大学': 'csu.edu.cn',
    '大连理工大学': 'dlut.edu.cn',
    '北京理工大学': 'bit.edu.cn',
    '厦门大学': 'xmu.edu.cn'
  };

  // 首先检查是否有预定义的域名映射
  if (domainMapping[school.学校中文名]) {
    const domain = domainMapping[school.学校中文名];
    // 使用Google的favicon服务，更可靠
    return `https://www.google.com/s2/favicons?domain=${domain}&sz=128`;
  }

  // 方案2: 尝试从项目官网获取favicon
  if (school.项目官网) {
    try {
      const url = new URL(school.项目官网);
      const domain = url.hostname.replace('www.', '');
      return `https://www.google.com/s2/favicons?domain=${domain}&sz=128`;
    } catch (e) {
      console.warn(`Invalid URL for ${school.学校中文名}: ${school.项目官网}`);
    }
  }

  // 方案3: 根据学校英文名尝试推断域名
  const englishName = school.学校英文名 || '';
  const englishDomainMapping = {
    'Massachusetts Institute of Technology': 'mit.edu',
    'MIT': 'mit.edu',
    'Stanford University': 'stanford.edu',
    'University of Oxford': 'ox.ac.uk',
    'University of Cambridge': 'cam.ac.uk',
    'Imperial College London': 'imperial.ac.uk',
    'University College London': 'ucl.ac.uk',
    'National University of Singapore': 'nus.edu.sg',
    'The University of Hong Kong': 'hku.hk',
    'Chinese University of Hong Kong': 'cuhk.edu.hk',
    'Nanyang Technological University': 'ntu.edu.sg',
    'University of Toronto': 'utoronto.ca',
    'University of Melbourne': 'unimelb.edu.au',
    'Harvard University': 'harvard.edu',
    'Yale University': 'yale.edu',
    'Princeton University': 'princeton.edu',
    'California Institute of Technology': 'caltech.edu',
    'University of Chicago': 'uchicago.edu',
    'Columbia University': 'columbia.edu',
    'University of Pennsylvania': 'upenn.edu'
  };

  for (const [name, domain] of Object.entries(englishDomainMapping)) {
    if (englishName.toLowerCase().includes(name.toLowerCase()) || 
        englishName.toLowerCase() === name.toLowerCase()) {
      return `https://www.google.com/s2/favicons?domain=${domain}&sz=128`;
    }
  }

  // 方案4: 生成美观的文字Logo（最终fallback）
  const colors = [
    { bg: '4F46E5', color: 'FFFFFF' }, // 紫色
    { bg: '059669', color: 'FFFFFF' }, // 绿色
    { bg: 'DC2626', color: 'FFFFFF' }, // 红色
    { bg: '2563EB', color: 'FFFFFF' }, // 蓝色
    { bg: 'F59E0B', color: 'FFFFFF' }, // 橙色
    { bg: '7C2D12', color: 'FFFFFF' }, // 棕色
    { bg: '581C87', color: 'FFFFFF' }, // 深紫色
    { bg: '0F766E', color: 'FFFFFF' }  // 青色
  ];
  
  // 使用学校名称的第一个字符来选择颜色
  const firstChar = (school.学校中文名 || school.学校英文名 || 'U').charAt(0);
  const colorIndex = firstChar.charCodeAt(0) % colors.length;
  const { bg, color } = colors[colorIndex];
  
  // 使用学校名称的前两个字符（如果是中文）或第一个字符（如果是英文）
  let initials = school.学校中文名 || school.学校英文名|| 'U';
  if (/[\u4e00-\u9fa5]/.test(initials)) {
    // 中文：取前两个字符
    initials = initials.substring(0, 2);
  } else {
    // 英文：取第一个字符
    initials = initials.charAt(0).toUpperCase();
  }
  
  return `https://ui-avatars.com/api/?name=${encodeURIComponent(initials)}&background=${bg}&color=${color}&size=128&font-size=0.6&bold=true&format=svg`;
};

/**
 * 模拟数据
 * 注意: 在实际项目中，这些数据应从API获取
 */

// 修改模拟数据，增加更多院校
const mockRecommendations = [
  {
    id: 1,
    学校中文名: '麻省理工学院',
    学校英文名: 'Massachusetts Institute of Technology',
    专业中文名: '计算机科学与工程硕士',
    专业英文名: 'Master of Science in Computer Science and Engineering',
    专业大类: '工科',
    专业方向: '计算机',
    所在学院: '电气工程与计算机科学学院',
    入学时间: '9月',
    项目时长: '2年',
    项目官网: 'https://www.eecs.mit.edu/academics/graduate-programs/masters-programs',
    培养目标: '该项目旨在培养在计算机科学和工程领域具有深厚理论基础和实践能力的高级专业人才。学生将掌握先进的算法设计、人工智能、机器学习、计算机系统等核心知识，并通过参与前沿研究项目，培养独立解决复杂问题的能力。',
    申请要求: '具有一类大学的计算机、电子等相关专业本科学位，GPA至少3.7/4.0，需要提供GRE成绩（一般要求320+），编程背景强大且有研究经验者优先。',
    语言要求: '托福总分要求100，各单项不低于23；雅思总分要求7.0，各单项不低于6.5。',
    申请时间: '25年秋季入学开放申请2024-09-15；Round1截止2024-12-15；Round2截止2025-03-01；最终截止2025-04-15',
    课程设置: '高级算法、机器学习、人工智能、计算机系统架构、分布式系统、计算机图形学、自然语言处理、数据库系统、网络安全等',
    专业代码: 'EECS-6',
    // 保留原有的功能性字段
    name: '麻省理工学院',
    location: '美国 · 马萨诸塞州',
    domain: 'mit.edu',
    program: '计算机科学与工程硕士',
    matchLevel: '冲',
    matchScore: 65,
    highlights: ['计算机排名世界第一', 'AI研究领先全球', '就业前景极佳'],
    ranking: 'QS排名 #1',
    tuitionRange: '$58,000-$60,000/年',
    deadline: 'Round1: 2024-12-15',
    reason: '麻省理工学院的计算机专业是全美最顶尖的项目之一，竞争非常激烈。根据学生的背景和GPA，属于具有挑战性的申请目标，但如果有突出的研究经历和推荐信，仍有一定机会。',
    showDetails: false,
    isFavorite: false
  },
  {
    id: 2,
    学校中文名: '帝国理工学院',
    学校英文名: 'Imperial College London',
    专业中文名: '地球能源（机器学习与数据科学）理学硕士',
    专业英文名: 'MSc Geo-Energy with Machine Learning and Data Science',
    专业大类: '工科',
    专业方向: '地球科学',
    所在学院: '地球科学与工程学院',
    入学时间: '9月',
    项目时长: '1年',
    项目官网: 'https://www.imperial.ac.uk/study/courses/postgraduate-taught/geo-energy-machine-learning-data-science/',
    培养目标: '数据科学和机器学习正在彻底改变地下地球科学和工程的研究。帝国理工学院地球能源（机器学习与数据科学）理学硕士课程适合想要将自己的技能应用到这个领域的数学家、物理学家或计算机科学家，或者具有地球科学或工程背景，想要学习数据科学和机器学习工具和方法的学生，学生将学习地下地球科学和工程的基本过程和各种应用，同时学习将数据科学、数值方法和机器学习应用于该领域的问题，还将有机会将这些概念应用到现实世界的环境中。',
    申请要求: '具有2:1学位，需要地质、物理科学学科或工程学科背景还需要良好的定量背景证明（A-level数学至少达到Grade A或具备本科学位数学课程证明）和编码经验具有其他资格和相关专业或行业经验的申请者也可能被学校考虑GPA要求该学校针对内地院校有专属的GPA分数要求，帝国理工学院仅接受"211院校"申请，对不同学院及专业设置不同的申请要求。',
    语言要求: '雅思总分要求6.5听力6阅读6写作6口语6托福总分要求92听力20阅读20写作20口语20PTE总分要求62听力56阅读56写作56口语56',
    申请时间: '25年秋季入学开放申请2024-09-27；Round1截止2025-01-16;Round2截止2025-03-27;Round32025-05-15进行中：距Round3申请截止还有36天',
    课程设置: 'Python数控编程NumericalProgramminginPython数据科学与机器学习DataScienceandMachineLearning计算数学ComputationalMathematics应用计算/数据科学ApplyingComputational/DataScience资源地质学与地球物理学概论IntroductiontoResourceGeologyandGeophysics流体与多孔介质中的流体流动FluidsandFluidFlowinPorousMedia地质力学与压力分析GeomechanicsandPressureAnalysis能源地球科学与工程EnergyGeoscienceandEngineering深度学习DeepLearning应用计算/数据科学项目AppliedComputational/DataScienceProject',
    专业代码: '67744',
    // 保留原有的功能性字段
    name: '帝国理工学院',
    location: '英国 · 伦敦',
    domain: 'imperial.ac.uk',
    program: '地球能源（机器学习与数据科学）理学硕士',
    matchLevel: '稳',
    matchScore: 82,
    highlights: ['英国顶尖理工学院', '地球科学与ML交叉领域', '一年制硕士'],
    ranking: 'QS排名 #6',
    tuitionRange: '£36,500/年',
    deadline: 'Round2: 2025-03-27',
    reason: '该项目结合了地球科学与机器学习，对于有计算背景的学生是非常好的选择。学校的录取要求与学生背景匹配度高，且英国一年制硕士可以快速完成学业。',
    showDetails: false,
    isFavorite: false
  },
  {
    id: 3,
    学校中文名: '新加坡国立大学',
    学校英文名: 'National University of Singapore',
    专业中文名: '金融科技与分析理学硕士',
    专业英文名: 'Master of Science in Financial Technology and Analytics',
    专业大类: '商科',
    专业方向: '金融科技',
    所在学院: '计算机学院',
    入学时间: '8月',
    项目时长: '1年',
    项目官网: 'https://www.comp.nus.edu.sg/programmes/pg/msfintech/',
    培养目标: '该项目旨在培养具备金融科技前沿知识和实践能力的专业人才，使学生掌握金融学、计算机科学和数据分析的交叉知识，能够在金融科技领域解决复杂问题。毕业生将能胜任金融科技公司、银行、投资机构的技术开发、数据分析和管理岗位。',
    申请要求: '要求具有计算机、金融、数学、统计等相关背景的学士学位，GPA 3.5/4.0以上优先。有相关工作经验者优先考虑。需要提供GRE或GMAT成绩。',
    语言要求: '托福总分至少85，各单项不低于22；雅思总分至少6.5，各单项不低于6.0。',
    申请时间: '2025年8月入学申请于2024年9月开放，2025年2月最终截止。申请越早录取机会越大。',
    课程设置: '金融科技导论、区块链与加密货币、大数据分析、机器学习在金融中的应用、金融风险管理、算法交易、监管科技、金融安全与隐私、金融科技创业实践、产业实习等',
    专业代码: 'SoC-MSFT',
    // 保留原有的功能性字段
    name: '新加坡国立大学',
    location: '新加坡',
    domain: 'nus.edu.sg',
    program: '金融科技与分析理学硕士',
    matchLevel: '保',
    matchScore: 92,
    highlights: ['亚洲顶尖学府', '金融科技新兴专业', '实习机会丰富'],
    ranking: 'QS排名 #11',
    tuitionRange: 'SGD $52,000/年',
    deadline: '2025-02-28',
    reason: '新加坡国立大学是亚洲顶尖学府，该专业结合金融与科技，符合学生的跨领域学习意向。新加坡作为亚洲金融中心，提供丰富的实习和就业机会。根据学生背景，录取概率很高。',
    showDetails: false,
    isFavorite: false
  },
  {
    id: 4,
    学校中文名: '香港大学',
    学校英文名: 'The University of Hong Kong',
    专业中文名: '人工智能理学硕士',
    专业英文名: 'Master of Science in Artificial Intelligence',
    专业大类: '工科',
    专业方向: '人工智能',
    所在学院: '工程学院',
    入学时间: '9月',
    项目时长: '1年',
    项目官网: 'https://www.msccs.cs.hku.hk/programmes/artificial-intelligence/',
    培养目标: '该项目旨在培养在人工智能领域具有扎实理论基础和实践能力的高级人才。学生将系统学习机器学习、深度学习、自然语言处理、计算机视觉等核心技术，并通过项目实践培养解决实际问题的能力。毕业生将能在科技公司、研究机构担任AI工程师、研究员等职位。',
    申请要求: '要求持有计算机科学、数学、统计学或相关工程学科的学士学位，GPA 3.0/4.0以上。编程能力强且有相关项目经验的申请者将获得优先考虑。',
    语言要求: '托福总分不低于80，雅思总分不低于6.5。',
    申请时间: '2025年9月入学的申请截止日期为2025年1月31日（第一轮）和2025年3月31日（第二轮）。',
    课程设置: '机器学习基础、深度学习、强化学习、自然语言处理、计算机视觉、大数据处理、AI系统设计、AI道德与安全、人工智能实践项目等',
    专业代码: 'MSc(AI)',
    // 保留原有的功能性字段
    name: '香港大学',
    location: '中国香港',
    domain: 'hku.hk',
    program: '人工智能理学硕士',
    matchLevel: '保',
    matchScore: 89,
    highlights: ['港大顶尖声誉', 'AI领域强势', '一年制项目'],
    ranking: 'QS排名 #21',
    tuitionRange: 'HK$240,000/年',
    deadline: 'Round1: 2025-01-31',
    reason: '香港大学在亚洲拥有卓越声誉，其人工智能硕士项目结合了理论与实践，且位于国际金融中心香港，提供了广阔的就业前景。对于有意向在亚洲发展的学生是极佳选择。',
    showDetails: false,
    isFavorite: false
  },
  {
    id: 5,
    学校中文名: '伦敦大学学院',
    学校英文名: 'University College London',
    专业中文名: '计算金融理学硕士',
    专业英文名: 'MSc Computational Finance',
    专业大类: '商科',
    专业方向: '金融工程',
    所在学院: '计算机科学系',
    入学时间: '9月',
    项目时长: '1年',
    项目官网: 'https://www.ucl.ac.uk/prospective-students/graduate/taught-degrees/computational-finance-msc',
    培养目标: '该项目旨在培养具备金融、数学和计算机科学跨学科能力的专业人才。学生将学习现代计算方法在金融建模、风险管理和交易中的应用，掌握金融数据分析和算法交易等前沿技术。毕业生将能在投资银行、资产管理公司和金融科技公司胜任量化分析师、风险建模师等角色。',
    申请要求: '要求拥有数学、物理、工程、计算机科学或其他定量学科的优秀学士学位（英国二级一等荣誉学位或国际同等学历）。需有较强的数学背景和编程能力，具有金融领域知识者优先。',
    语言要求: '雅思总分要求7.0，各单项不低于6.5；托福总分要求100，各单项不低于24。',
    申请时间: '2025年入学申请于2024年10月开放，建议在2025年1月前提交申请，最终截止日期为2025年7月1日，但名额可能提前满。',
    课程设置: '金融数学、计算统计学、机器学习、数值方法、金融衍生品定价、金融风险管理、高频交易系统、C++在金融中的应用、计算金融项目等',
    专业代码: 'COMPF0095',
    // 保留原有的功能性字段
    name: '伦敦大学学院',
    location: '英国 · 伦敦',
    domain: 'ucl.ac.uk',
    program: '计算金融理学硕士',
    matchLevel: '稳',
    matchScore: 81,
    highlights: ['金融量化顶尖项目', '英国一年制硕士', '金融科技交叉领域'],
    ranking: 'QS排名 #8',
    tuitionRange: '£35,300/年',
    deadline: '2025-01-31推荐',
    reason: '伦敦大学学院的计算金融项目适合有定量背景的学生，且位于全球金融中心伦敦，实习和就业机会丰富。该项目结合了计算机科学和金融，符合学生跨领域学习的需求。',
    showDetails: false,
    isFavorite: false
  },
  {
    id: 6,
    学校中文名: '牛津大学',
    学校英文名: 'University of Oxford',
    专业中文名: '金融经济学理学硕士',
    专业英文名: 'MSc Financial Economics',
    专业大类: '商科',
    专业方向: '金融经济',
    所在学院: '萨伊德商学院',
    入学时间: '9月',
    项目时长: '1年',
    项目官网: 'https://www.sbs.ox.ac.uk/programmes/msc-financial-economics',
    培养目标: '该项目旨在为学生提供现代金融理论和实践的深度知识，培养在金融市场、投资银行、资产管理等领域的专业技能。课程结合理论学习和实践应用，让学生掌握前沿的金融分析工具和方法。',
    申请要求: '要求具有经济学、金融学、数学或相关定量学科的优秀学士学位（英国一等或二等一级荣誉学位）。需要较强的数学和统计学背景，GMAT或GRE成绩优异者优先考虑。',
    语言要求: '雅思总分要求7.5，各单项不低于7.0；托福总分要求110，听力不低于22，阅读不低于24，口语不低于25，写作不低于24。',
    申请时间: '2025年入学申请截止日期为2025年1月6日，建议尽早提交申请以增加录取机会。',
    课程设置: '金融理论、投资分析、公司金融、衍生品市场、风险管理、计量经济学、行为金融学、金融建模、投资组合管理等',
    专业代码: 'MSc-FE',
    name: '牛津大学',
    location: '英国 · 牛津',
    domain: 'ox.ac.uk',
    program: '金融经济学理学硕士',
    matchLevel: '冲',
    matchScore: 68,
    highlights: ['世界顶尖学府', '萨伊德商学院声誉卓著', '金融业界认可度极高'],
    ranking: 'QS排名 #2',
    tuitionRange: '£48,670/年',
    deadline: '2025-01-06',
    reason: '牛津大学萨伊德商学院的金融经济学项目是全球顶尖的金融硕士项目之一。虽然竞争激烈，但其卓越的学术声誉和强大的校友网络为毕业生提供了无与伦比的职业发展机会。',
    showDetails: false,
    isFavorite: false
  },
  {
    id: 7,
    学校中文名: '剑桥大学',
    学校英文名: 'University of Cambridge',
    专业中文名: '机器学习与机器智能哲学硕士',
    专业英文名: 'MPhil in Machine Learning and Machine Intelligence',
    专业大类: '工科',
    专业方向: '机器学习',
    所在学院: '工程学院',
    入学时间: '10月',
    项目时长: '1年',
    项目官网: 'https://www.mlmi.eng.cam.ac.uk/',
    培养目标: '该项目旨在培养在机器学习和人工智能领域具有深厚理论基础和创新能力的研究型人才。学生将接受严格的学术训练，掌握最前沿的机器学习技术，为进一步的博士研究或高端技术职业奠定基础。',
    申请要求: '要求具有计算机科学、数学、物理、工程或相关学科的优秀学士学位（英国一等荣誉学位或国际同等学历）。需要扎实的数学基础和编程能力，有机器学习相关经验者优先。',
    语言要求: '雅思总分要求7.5，各单项不低于7.0；托福总分要求110，各单项不低于25。',
    申请时间: '2025年入学申请截止日期为2025年1月3日，强烈建议在12月初前提交申请。',
    课程设置: '机器学习基础、深度学习、贝叶斯方法、强化学习、计算机视觉、自然语言处理、神经网络、统计学习理论、研究项目等',
    专业代码: 'MPhil-MLMI',
    name: '剑桥大学',
    location: '英国 · 剑桥',
    domain: 'cam.ac.uk',
    program: '机器学习与机器智能哲学硕士',
    matchLevel: '稳',
    matchScore: 72,
    highlights: ['世界顶尖AI研究中心', '一对一导师制', '研究导向型项目'],
    ranking: 'QS排名 #3',
    tuitionRange: '£37,176/年',
    deadline: '2025-01-03',
    reason: '剑桥大学的机器学习项目代表了该领域的最高学术水平。虽然申请竞争极其激烈，但其卓越的研究环境和世界级的师资力量使其成为顶尖学生的理想选择。',
    showDetails: false,
    isFavorite: false
  },
  {
    id: 8,
    学校中文名: '斯坦福大学',
    学校英文名: 'Stanford University',
    专业中文名: '计算机科学理学硕士',
    专业英文名: 'Master of Science in Computer Science',
    专业大类: '工科',
    专业方向: '计算机科学',
    所在学院: '工程学院',
    入学时间: '9月',
    项目时长: '2年',
    项目官网: 'https://cs.stanford.edu/academics/masters',
    培养目标: '该项目旨在为学生提供计算机科学领域的深度知识和研究能力。学生可以选择专业方向包括人工智能、系统、理论计算机科学等，培养在科技行业或学术界的领导能力。',
    申请要求: '要求具有计算机科学或相关领域的学士学位，GPA 3.5/4.0以上。需要提供GRE成绩，编程能力强且有相关项目经验或研究背景者优先考虑。',
    语言要求: '托福总分要求89，雅思总分要求7.0。母语非英语的申请者需要提供英语水平证明。',
    申请时间: '2025年秋季入学申请截止日期为2024年12月3日。申请材料需要提前准备，包括推荐信和个人陈述。',
    课程设置: '算法设计与分析、机器学习、人工智能、计算机系统、数据库系统、计算机网络、软件工程、计算机图形学、自然语言处理等',
    专业代码: 'MS-CS',
    name: '斯坦福大学',
    location: '美国 · 加利福尼亚州',
    domain: 'stanford.edu',
    program: '计算机科学理学硕士',
    matchLevel: '稳',
    matchScore: 75,
    highlights: ['硅谷核心位置', '顶尖科技公司实习机会', '世界一流师资'],
    ranking: 'QS排名 #3',
    tuitionRange: '$58,416/年',
    deadline: '2024-12-03',
    reason: '斯坦福大学计算机科学项目位于硅谷核心，与顶尖科技公司联系紧密。虽然录取竞争激烈，但其卓越的学术声誉和无与伦比的就业前景使其成为计算机科学学生的梦想学府。',
    showDetails: false,
    isFavorite: false
  },
  {
    id: 9,
    学校中文名: '哈佛大学',
    学校英文名: 'Harvard University',
    专业中文名: '数据科学理学硕士',
    专业英文名: 'Master of Science in Data Science',
    专业大类: '理科',
    专业方向: '数据科学',
    所在学院: '工程与应用科学学院',
    入学时间: '9月',
    项目时长: '2年',
    项目官网: 'https://iacs.seas.harvard.edu/education/graduate-program',
    培养目标: '该项目旨在培养在数据科学领域具有深厚理论基础和实践能力的专业人才。学生将学习统计学、机器学习、计算方法等核心知识，并通过实际项目培养解决复杂数据问题的能力。',
    申请要求: '要求具有数学、统计学、计算机科学、工程或相关定量学科的学士学位，GPA 3.7/4.0以上。需要较强的数学和编程背景，有数据分析经验者优先。',
    语言要求: '托福总分要求100，雅思总分要求7.0。所有国际学生都需要参加英语水平测试。',
    申请时间: '2025年秋季入学申请截止日期为2024年12月15日。建议提前3-6个月开始准备申请材料。',
    课程设置: '统计推断、机器学习、数据可视化、大数据系统、计算统计学、贝叶斯分析、深度学习、数据伦理、毕业项目等',
    专业代码: 'SM-DS',
    name: '哈佛大学',
    location: '美国 · 马萨诸塞州',
    domain: 'harvard.edu',
    program: '数据科学理学硕士',
    matchLevel: '稳',
    matchScore: 70,
    highlights: ['常春藤盟校声誉', '跨学科研究机会', '强大校友网络'],
    ranking: 'QS排名 #4',
    tuitionRange: '$54,768/年',
    deadline: '2024-12-15',
    reason: '哈佛大学数据科学项目结合了统计学、计算机科学和领域专业知识。虽然竞争非常激烈，但哈佛的品牌价值和学术资源为学生提供了无与伦比的发展平台。',
    showDetails: false,
    isFavorite: false
  },
  {
    id: 10,
    学校中文名: '多伦多大学',
    学校英文名: 'University of Toronto',
    专业中文名: '应用计算理学硕士',
    专业英文名: 'Master of Science in Applied Computing',
    专业大类: '工科',
    专业方向: '应用计算',
    所在学院: '计算机科学系',
    入学时间: '9月',
    项目时长: '2年',
    项目官网: 'https://web.cs.toronto.edu/graduate/mscac',
    培养目标: '该项目为具有计算机科学背景的学生提供在应用计算领域的高级训练。学生将学习前沿的计算技术，并通过实习和项目获得实际工作经验，为在科技行业的成功职业生涯做准备。',
    申请要求: '要求具有计算机科学或相关领域的学士学位，GPA B+以上。需要有扎实的编程基础和数学背景，相关工作经验或研究经历者优先考虑。',
    语言要求: '托福总分要求93，写作/口语不低于22；雅思总分要求7.0，各单项不低于6.5。',
    申请时间: '2025年秋季入学申请截止日期为2025年1月15日。建议在截止日期前至少一个月提交完整申请。',
    课程设置: '高级算法、机器学习、软件工程、数据库系统、分布式系统、人机交互、计算机视觉、自然语言处理、产业实习等',
    专业代码: 'MScAC',
    name: '多伦多大学',
    location: '加拿大 · 安大略省',
    domain: 'utoronto.ca',
    program: '应用计算理学硕士',
    matchLevel: '保',
    matchScore: 88,
    highlights: ['加拿大顶尖学府', '带薪实习机会', '移民友好政策'],
    ranking: 'QS排名 #21',
    tuitionRange: 'CAD $59,910/年',
    deadline: '2025-01-15',
    reason: '多伦多大学应用计算项目结合了学术学习和实际工作经验，且加拿大的移民政策对国际学生友好。该项目的实习机会和就业前景都非常优秀，是很好的保底选择。',
    showDetails: false,
    isFavorite: false
  }
];

// 模拟搜索建议 - 院校
const querySchools = (queryString, callback) => {
  // 实际项目中应从API获取数据
  const schools = [
    { value: '清华大学', label: '清华大学', secondaryText: 'Tsinghua University' },
    { value: '北京大学', label: '北京大学', secondaryText: 'Peking University' },
    { value: '复旦大学', label: '复旦大学', secondaryText: 'Fudan University' },
    { value: '上海交通大学', label: '上海交通大学', secondaryText: 'Shanghai Jiao Tong University' },
    { value: '浙江大学', label: '浙江大学', secondaryText: 'Zhejiang University' },
    { value: '南京大学', label: '南京大学', secondaryText: 'Nanjing University' },
    { value: '中国人民大学', label: '中国人民大学', secondaryText: 'Renmin University of China' },
    { value: '武汉大学', label: '武汉大学', secondaryText: 'Wuhan University' },
    { value: '哈尔滨工业大学', label: '哈尔滨工业大学', secondaryText: 'Harbin Institute of Technology' },
    { value: '西安交通大学', label: '西安交通大学', secondaryText: "Xi'an Jiaotong University" },
    { value: '北京航空航天大学', label: '北京航空航天大学', secondaryText: 'Beihang University' },
    { value: '同济大学', label: '同济大学', secondaryText: 'Tongji University' },
    { value: '天津大学', label: '天津大学', secondaryText: 'Tianjin University' },
    { value: '哈佛大学', label: '哈佛大学', secondaryText: 'Harvard University' },
    { value: '麻省理工学院', label: '麻省理工学院', secondaryText: 'Massachusetts Institute of Technology' },
    { value: '斯坦福大学', label: '斯坦福大学', secondaryText: 'Stanford University' },
    { value: '剑桥大学', label: '剑桥大学', secondaryText: 'University of Cambridge' },
    { value: '牛津大学', label: '牛津大学', secondaryText: 'University of Oxford' },
    { value: '加州大学伯克利分校', label: '加州大学伯克利分校', secondaryText: 'UC Berkeley' },
    { value: '伦敦大学学院', label: '伦敦大学学院', secondaryText: 'University College London' },
    { value: '帝国理工学院', label: '帝国理工学院', secondaryText: 'Imperial College London' },
    { value: '多伦多大学', label: '多伦多大学', secondaryText: 'University of Toronto' },
    { value: '墨尔本大学', label: '墨尔本大学', secondaryText: 'University of Melbourne' },
  ];
  
  // 在搜索时同时匹配学校名称和英文名称
  const results = queryString
    ? schools.filter(school => 
        school.value.toLowerCase().includes(queryString.toLowerCase()) || 
        (school.label && school.label.toLowerCase().includes(queryString.toLowerCase())) ||
        (school.secondaryText && school.secondaryText.toLowerCase().includes(queryString.toLowerCase()))
      )
    : schools;
    
  callback(results);
};

// 模拟搜索建议 - 专业
const queryMajors = (queryString, callback) => {
  // 实际项目中应从API获取数据
  const majors = [
    { value: '计算机科学与技术', label: '计算机科学与技术', secondaryText: '理工类-计算机' },
    { value: '软件工程', label: '软件工程', secondaryText: '理工类-计算机' },
    { value: '人工智能', label: '人工智能', secondaryText: '理工类-计算机' },
    { value: '数据科学与大数据技术', label: '数据科学与大数据技术', secondaryText: '理工类-计算机' },
    { value: '信息安全', label: '信息安全', secondaryText: '理工类-计算机' },
    { value: '电子信息工程', label: '电子信息工程', secondaryText: '理工类-电子' },
    { value: '通信工程', label: '通信工程', secondaryText: '理工类-电子' },
    { value: '集成电路设计与集成系统', label: '集成电路设计与集成系统', secondaryText: '理工类-电子' },
    { value: '机械工程', label: '机械工程', secondaryText: '理工类-机械' },
    { value: '自动化', label: '自动化', secondaryText: '理工类-电气' },
    { value: '电气工程及其自动化', label: '电气工程及其自动化', secondaryText: '理工类-电气' },
    { value: '土木工程', label: '土木工程', secondaryText: '理工类-土木' },
    { value: '工程管理', label: '工程管理', secondaryText: '理工类-管理' },
    { value: '建筑学', label: '建筑学', secondaryText: '理工类-建筑' },
    { value: '城市规划', label: '城市规划', secondaryText: '理工类-规划' },
    { value: '工商管理', label: '工商管理', secondaryText: '经管类-管理' },
    { value: '会计学', label: '会计学', secondaryText: '经管类-会计' },
    { value: '财务管理', label: '财务管理', secondaryText: '经管类-财务' },
    { value: '金融学', label: '金融学', secondaryText: '经管类-金融' },
    { value: '经济学', label: '经济学', secondaryText: '经管类-经济' },
    { value: '国际经济与贸易', label: '国际经济与贸易', secondaryText: '经管类-经济' },
    { value: '市场营销', label: '市场营销', secondaryText: '经管类-营销' },
    { value: '电子商务', label: '电子商务', secondaryText: '经管类-商务' },
    { value: '法学', label: '法学', secondaryText: '人文社科-法学' },
    { value: '英语', label: '英语', secondaryText: '人文社科-外语' },
    { value: '日语', label: '日语', secondaryText: '人文社科-外语' },
    { value: '新闻学', label: '新闻学', secondaryText: '人文社科-传媒' },
    { value: '广告学', label: '广告学', secondaryText: '人文社科-传媒' },
    { value: '传播学', label: '传播学', secondaryText: '人文社科-传媒' },
  ];
  
  // 匹配专业名称和分类
  const results = queryString
    ? majors.filter(major => 
        major.value.toLowerCase().includes(queryString.toLowerCase()) || 
        (major.label && major.label.toLowerCase().includes(queryString.toLowerCase())) ||
        (major.secondaryText && major.secondaryText.toLowerCase().includes(queryString.toLowerCase()))
      )
    : majors;
    
  callback(results);
};

/**
 * 方法定义
 */

// 获取推荐结果的方法 (目前仅模拟)
const getRecommendations = () => {
  isLoading.value = true;
  
  // 模拟API请求延迟
  // 注意: 实际项目中应通过API获取数据
  setTimeout(() => {
    recommendations.value = initRecommendations(mockRecommendations);
    hasSubmitted.value = true;
    isLoading.value = false;
    ElMessage.success('已生成推荐院校列表，可为学生提供选校参考！');
  }, 1500);
};

// 提交表单
const handleSubmit = () => {
  // 在实际项目中，这里应该有表单验证逻辑
  console.log('提交的学生数据:', profileForm);
  getRecommendations();
};

// 收藏/取消收藏学校
const toggleFavorite = (school) => {
  school.isFavorite = !school.isFavorite;
  const message = school.isFavorite 
    ? `已收藏 ${school.name}，方便后续查看` 
    : `已取消收藏 ${school.name}`;
  ElMessage.success(message);
};

// 添加logo加载错误处理方法
const handleLogoError = (event, school) => {
  // 如果logo加载失败，显示学校名称首字母
  event.target.style.display = 'none';
  const container = event.target.parentElement;
  if (container) {
    const fallbackText = document.createElement('span');
    fallbackText.className = 'text-2xl font-bold text-gray-400';
    // 使用正确的字段名：学校中文名
    const schoolName = school.学校中文名 || school.name || 'U';
    fallbackText.textContent = schoolName.charAt(0);
    container.appendChild(fallbackText);
  }
};

// 动画处理方法
const handleEnter = (el, done) => {
  const height = el.scrollHeight;
  el.style.height = '0px';
  el.style.opacity = '0';
  
  // 触发重绘
  el.offsetHeight;
  
  // 设置过渡属性
  el.style.transition = 'height 0.7s ease, opacity 0.7s ease';
  el.style.height = `${height}px`;
  el.style.opacity = '1';
  
  el.addEventListener('transitionend', function onEnd() {
    el.style.height = 'auto';
    el.removeEventListener('transitionend', onEnd);
    done();
  });
};

const handleLeave = (el, done) => {
  const height = el.scrollHeight;
  el.style.height = `${height}px`;
  
  // 触发重绘
  el.offsetHeight;
  
  // 设置过渡属性
  el.style.transition = 'height 0.7s ease, opacity 0.7s ease';
  el.style.height = '0px';
  el.style.opacity = '0';
  
  el.addEventListener('transitionend', function onEnd() {
    el.style.height = '0px';
    el.removeEventListener('transitionend', onEnd);
    done();
  });
};

// 切换详情显示的方法
const toggleDetails = (school) => {
  if (!school.detailsLoaded) {
    // 第一次展开时计算高度
    school.detailsHeight = 250; // 初始估计高度，可以稍大一些以避免初次闪烁
    school.detailsLoaded = true;
    school.showDetails = true;
    
    // 在下一个渲染周期计算实际高度
    setTimeout(() => {
      const detailsEls = document.querySelectorAll('.details-content');
      for (const el of detailsEls) {
        if (el.closest(`[data-school-id="${school.id}"]`)) {
          school.originalHeight = el.scrollHeight; // 使用 scrollHeight 获取完整内容高度
          school.detailsHeight = school.originalHeight;
          break;
        }
      }
    }, 150); // 增加延迟确保内容渲染完毕
  } else {
    // 已经加载过，直接切换状态
    school.showDetails = !school.showDetails;
    school.detailsHeight = school.showDetails ? school.originalHeight : 0;
  }
};

// 初始化学校数据
const initRecommendations = (schools) => {
  return schools.map(school => {
    return {
      ...school,
      detailsHeight: 0,
      detailsLoaded: false,
      originalHeight: 0, // 添加原始高度字段
      showDetails: false
    };
  });
};

// 生命周期钩子
onMounted(() => {
  console.log('智能选校助手组件已加载');
  
  // 监听窗口大小变化，重新计算详情区域高度
  window.addEventListener('resize', recalculateHeights);
});

// 窗口大小变化时重新计算高度
const recalculateHeights = () => {
  if (!recommendations.value.length) return;
  
  setTimeout(() => {
    const detailsEls = document.querySelectorAll('.details-content');
    for (const el of detailsEls) {
      const schoolId = el.closest('[data-school-id]')?.getAttribute('data-school-id');
      if (schoolId) {
        const school = recommendations.value.find(s => s.id == schoolId);
        if (school && school.detailsLoaded) {
          school.originalHeight = el.scrollHeight; // 使用 scrollHeight
          if (school.showDetails) {
            school.detailsHeight = school.originalHeight;
          }
        }
      }
    }
  }, 150); // 增加延迟
};

// 筛选和排序后的推荐结果
const filteredRecommendations = computed(() => {
  // 先根据匹配度筛选
  let result = recommendations.value;
  
  if (filterLevel.value) {
    result = result.filter(school => school.matchLevel === filterLevel.value);
  }
  
  // 再根据选择的方式排序
  const [field, order] = sortBy.value.split('-');
  
  return result.sort((a, b) => {
    // 对于排名，需要特殊处理，因为是字符串格式如 "QS排名 #5"
    if (field === 'ranking') {
      const rankA = parseInt(a.ranking.match(/\d+/)[0], 10);
      const rankB = parseInt(b.ranking.match(/\d+/)[0], 10);
      return order === 'asc' ? rankA - rankB : rankB - rankA;
    }
    
    // 对于其他数值字段
    return order === 'asc' ? a[field] - b[field] : b[field] - a[field];
  });
});

</script> 

<style>
/* 全局覆盖Element Plus的主题色 - 不使用scoped */
.el-select-dropdown__item.is-hovering {
  background-color: rgba(79, 70, 229, 0.1) !important;
  color: #4F46E5 !important;
}

.el-select-dropdown__item.is-selected {
  color: #4F46E5 !important;
  font-weight: 500 !important;
}

.el-select-dropdown__item.is-selected::after {
  background: #4F46E5 !important;
}

.el-select-dropdown__item.is-selected.is-hovering {
  background-color: rgba(79, 70, 229, 0.2) !important;
  color: #4F46E5 !important;
}
</style>

<style lang="postcss" scoped>
/* 调整表单控件样式 */
:deep(.el-input__wrapper),
:deep(.el-select .el-input__wrapper) {
  @apply !shadow-none bg-slate-50 border border-gray-200 hover:border-gray-300 transition-colors duration-150 rounded-md;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus),
:deep(.el-select .el-input__wrapper.is-focus) {
  @apply bg-white border-[#4F46E5] ring-1 ring-[#4F46E5]/50;
  box-shadow: none !important;
}

/* 表单区域折叠/展开动画效果 */
.cursor-pointer {
  @apply hover:text-primary transition-colors duration-200;
}

/* 扩展图标的旋转动画 */
.rotate-180 {
  transform: rotate(180deg);
  transition: transform 0.3s ease-in-out;
}
.material-icons-outlined.text-gray-400 {
  transition: transform 0.3s ease-in-out;
}

/* 折叠区域过渡效果 - 改进以防止抖动 */
.form-section-content {
  transition: max-height 0.4s ease-in-out, opacity 0.3s ease-in-out;
  will-change: max-height, opacity;
  overflow: hidden;
  position: relative; /* 添加定位，确保内容不会影响外部布局 */
}

/* 确保整个表单容器具有固定宽度，防止滚动条引起的布局变化 */
.pro-card {
  @apply overflow-x-hidden; /* 防止水平滚动条出现 */
  width: 100%; /* 固定宽度 */
}

/* 添加最小高度，防止在内容变化时页面布局突变 */
.school-assistant-page {
  min-height: 100vh; /* 最小高度确保内容变化不会导致整体高度剧烈变化 */
}

/* 表单分组间隔 */
.divide-y > div {
  @apply relative;
}

.divide-y > div:after {
  content: '';
  @apply absolute left-0 right-0 bottom-0 border-b border-gray-100;
}

/* 表单标题样式增强 */
.flex.items-center.cursor-pointer {
  @apply rounded-md -ml-1 px-1 py-0.5 hover:bg-gray-100;
}

/* 调整按钮样式 */
:deep(.el-button--primary) {
  @apply !bg-[#4F46E5] !border-[#4F46E5] !text-sm;
}

/* 调整下拉菜单样式 */
:deep(.el-select-dropdown__item) {
  @apply !text-sm;
}

/* 调整表单项间距 */
.form-section {
  @apply mb-4 last:mb-0;
}

/* 调整标签样式 */
.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

/* 添加操作按钮样式 */
:deep(.el-button.el-button--primary.is-link) {
  --el-button-text-color: #606266;
}

:deep(.el-button.el-button--danger.is-link) {
  --el-button-text-color: #f56c6c;
}

:deep(.el-dropdown-menu__item) {
  @apply text-gray-600;
}

:deep(.el-dropdown-menu__item i) {
  @apply text-gray-400;
}

:deep(.el-dropdown-menu__item:hover) {
  @apply text-primary bg-primary/5;
}

:deep(.el-dropdown-menu__item.danger:hover) {
  @apply text-danger bg-danger/5;
}

/* 添加新的样式 */
:deep(.el-button--text) {
  padding: 0;
}

:deep(.el-button--text:hover) {
  background-color: transparent;
  text-decoration: underline;
}

/* 优化卡片间距 */
.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

/* 优化表单样式 */
:deep(.el-input__wrapper) {
  @apply shadow-none border border-gray-300 hover:border-[#4F46E5] transition-colors duration-300;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  @apply border-[#4F46E5] ring-1 ring-[#4F46E5] ring-opacity-50;
  box-shadow: none !important;
}

:deep(.el-select .el-input__wrapper) {
  @apply shadow-none border border-gray-300 hover:border-[#4F46E5];
  box-shadow: none !important;
}

:deep(.el-select .el-input__wrapper.is-focus) {
  @apply border-[#4F46E5] ring-1 ring-[#4F46E5] ring-opacity-50;
  box-shadow: none !important;
}

/* 表单分组样式 */
.form-section {
  @apply relative;
}

.form-section:before {
  content: '';
  @apply absolute left-4 top-0 bottom-0 w-px bg-gray-200;
}

/* 必填项标记样式 */
.required-mark {
  @apply text-red-500 ml-0.5;
}

/* 输入框组样式 */
.input-group {
  @apply space-y-4;
}

.input-group:last-child {
  @apply mb-0;
}

/* 提示文本样式 */
.help-text {
  @apply mt-1 text-xs text-gray-500;
}

/* 分隔线样式 */
.section-divider {
  @apply my-6 border-t border-gray-100;
}

/* 图标按钮样式 */
.icon-button {
  @apply inline-flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 text-gray-500 hover:bg-gray-200 transition-colors duration-200;
}

/* 添加过渡动画 */
.fade-enter-active,
.fade-leave-active {
  @apply transition-opacity duration-300;
}

.fade-enter-from,
.fade-leave-to {
  @apply opacity-0;
}

/* 详情展开/收起动画 */
.slide-fade-enter-active,
.slide-fade-leave-active {
  @apply transition-all overflow-hidden;
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  @apply opacity-0 h-0;
}

/* 学校卡片样式增强 */
.school-card {
  @apply transform transition-transform duration-300;
}

.school-card:hover {
  @apply shadow-md -translate-y-1;
}

/* 详情区域增强 */
.details-container {
  @apply relative z-0 overflow-hidden;
}

.details-content {
  @apply transition-all duration-300 ease-in-out;
}

/* 卡片样式定义 */
.pro-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-100;
  margin-bottom: 1rem;
}

.pro-card-header {
  @apply border-b border-gray-100 flex items-center;
  padding: 0 16px;
  height: 3.5rem;
}

.pro-card-title {
  @apply text-gray-800 font-medium flex items-center;
  font-weight: 600;
}

.pro-card-body {
  @apply p-4;
}

.pro-card-footer {
  @apply p-3.5 border-t border-gray-100 flex justify-end;
}

/* 学校Logo样式 */
.school-logo-container {
  @apply flex-shrink-0 w-14 h-14 bg-white rounded-lg flex items-center justify-center overflow-hidden mr-4 border border-gray-100 shadow-sm;
}

.school-logo-container img {
  @apply h-10 w-10 object-contain;
}

.school-logo-container span {
  @apply text-2xl font-bold text-gray-400;
  line-height: 1;
}

/* 优化Logo显示 */
.school-card .school-logo-container {
  @apply bg-gray-50;
}

.school-card .school-logo-container img {
  @apply max-w-full max-h-full p-1;
  filter: grayscale(0.1);
}

.school-card:hover .school-logo-container img {
  filter: grayscale(0);
}

/* 浮动动画效果 */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* 优化过渡效果 */
.school-card {
  @apply transform transition-all duration-300;
}

.school-card:hover {
  @apply shadow-lg -translate-y-1 border-[#4F46E5]/20;
}

/* 匹配度标签动画 */
.school-card .px-2\.5 {
  @apply transition-all duration-300;
}

.school-card:hover .px-2\.5 {
  @apply scale-105;
}

/* 优化按钮悬停效果 */
.el-button:hover {
  @apply transform scale-105 transition-transform duration-200;
}

/* 覆盖 Element Plus 下拉选择框的蓝色主题为紫色 */
:deep(.el-select .el-select__wrapper.is-focused) {
  @apply !border-[#4F46E5];
  box-shadow: 0 0 0 1px #4F46E5 inset !important;
}
:deep(.el-select-dropdown__item.is-selected.is-hovering) {
  @apply !bg-[#4F46E5]/20;
}

/* 下拉菜单的加载图标 */
:deep(.el-select-dropdown .el-select-dropdown__loading) {
  @apply !text-[#4F46E5];
}

/* 多选标签 */
:deep(.el-select .el-tag.is-info) {
  @apply !bg-[#4F46E5]/10 !text-[#4F46E5] !border-[#4F46E5]/20;
}

/* 多选标签的关闭按钮 */
:deep(.el-select .el-tag.is-info .el-tag__close) {
  @apply !text-[#4F46E5] hover:!bg-[#4F46E5]/20;
}

/* 下拉菜单的滚动条 */
:deep(.el-select-dropdown__wrap::-webkit-scrollbar-thumb) {
  @apply !bg-[#4F46E5]/30;
}

:deep(.el-select-dropdown__wrap::-webkit-scrollbar-thumb:hover) {
  @apply !bg-[#4F46E5]/50;
}

/* 禁用覆盖Element Plus的默认主题色变量 */
:deep(.el-select) {
  --el-color-primary: #4F46E5 !important;
  --el-color-primary-light-3: #7C7AED !important;
  --el-color-primary-light-5: #9D9BF2 !important;
  --el-color-primary-light-7: #BEBDF7 !important;
  --el-color-primary-light-8: #CFCEF9 !important;
  --el-color-primary-light-9: #DFDFFB !important;
  --el-color-primary-dark-2: #3F37B7 !important;
}

/* 强制覆盖下拉菜单选项的所有状态 - 使用更具体的选择器 */
:deep(.el-select-dropdown) {
  .el-select-dropdown__item {
    &.is-hovering {
      background-color: rgba(79, 70, 229, 0.1) !important;
      color: #4F46E5 !important;
    }
    
    &.is-selected {
      color: #4F46E5 !important;
      font-weight: 500 !important;
      
      &::after {
        background: #4F46E5 !important;
      }
    }
    
    &.is-selected.is-hovering {
      background-color: rgba(79, 70, 229, 0.2) !important;
      color: #4F46E5 !important;
    }
  }
}

/* 全局覆盖Element Plus主题色 */
:root {
  --el-color-primary: #4F46E5 !important;
}

/* 覆盖收藏按钮的悬浮状态颜色 */
:deep(.el-button:not(.el-button--primary):hover) {
  @apply !bg-gray-100 !border-gray-300 !text-gray-600;
}

:deep(.el-button:not(.el-button--primary):active) {
  @apply !bg-gray-200 !border-gray-400 !text-gray-700;
}
</style> 