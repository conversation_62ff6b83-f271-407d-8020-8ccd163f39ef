<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-50 flex items-center justify-center p-4">
    <div class="w-full max-w-md relative">
      <!-- 装饰元素 -->
      <div class="absolute -top-6 -left-6 w-16 h-16 bg-blue-100 rounded-full opacity-70 blur-lg"></div>
      <div class="absolute -bottom-8 -right-8 w-20 h-20 bg-indigo-100 rounded-full opacity-70 blur-lg"></div>
      <div class="absolute top-1/3 -right-4 w-8 h-8 bg-purple-100 rounded-full opacity-60 blur-md"></div>
      
      <!-- 忘记密码卡片 -->
      <div class="bg-white rounded-2xl shadow-lg p-8 space-y-6 border border-gray-100 relative z-10 backdrop-blur-sm">
        <!-- Logo区域 -->
        <div class="flex flex-col items-center space-y-3">
          <div class="bg-gradient-to-r from-primary to-primary-light rounded-lg p-3 shadow-md transform hover:scale-105 transition-transform duration-300">
            <h1 class="text-xl font-bold text-white">TunshuEdu</h1>
          </div>
          <h2 class="text-gray-600 font-medium">留学行业的AI工具箱</h2>
        </div>

        <!-- 忘记密码表单 -->
        <div class="text-center">
          <h3 class="text-lg font-semibold text-gray-800 mb-2">忘记密码</h3>
          <p class="text-sm text-gray-600 mb-4">请输入您的注册邮箱，我们将向您发送重置密码的链接</p>
        </div>

        <form @submit.prevent="handleSubmit" class="space-y-5">
          <div class="group">
            <label class="block text-sm font-medium text-gray-700 mb-1">邮箱</label>
            <div class="relative">
              <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                  <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                </svg>
              </span>
              <input
                v-model="form.email"
                type="email"
                placeholder="请输入邮箱"
                class="w-full pl-10 pr-4 py-3 rounded-xl bg-gray-50 border border-gray-200 focus:border-primary focus:ring-2 focus:ring-primary focus:ring-opacity-20 transition-colors duration-200 group-hover:bg-gray-100"
                required
              />
            </div>
          </div>

          <button
            type="submit"
            class="w-full bg-gradient-to-r from-primary to-primary-light text-white py-3 px-4 rounded-xl shadow hover:shadow-md hover:translate-y-[-2px] active:translate-y-[0px] transition-all duration-200 font-medium"
            :disabled="loading"
          >
            <span v-if="!loading">发送重置链接</span>
            <span v-else class="flex items-center justify-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              发送中...
            </span>
          </button>
        </form>

        <!-- 提示信息 -->
        <div class="text-xs text-gray-500 text-center">
          重置链接将在30分钟内有效，请及时查收邮件
        </div>

        <!-- 底部链接 -->
        <div class="flex items-center justify-center pt-4 border-t border-gray-100">
          <div class="flex items-center space-x-1">
            <span class="text-gray-500 text-sm">想起密码了？</span>
            <router-link to="/login" class="text-primary hover:text-primary-dark text-sm font-medium transition-colors">
              返回登录
            </router-link>
          </div>
        </div>
      </div>
      
      <!-- 版权信息 -->
      <div class="mt-6 text-center text-xs text-gray-500">
        <span>© 2025 囤鼠科技教育平台</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { forgotPassword } from '@/api/auth'

const router = useRouter()
const loading = ref(false)

const form = reactive({
  email: ''
})

const handleSubmit = async () => {
  if (!form.email) {
    ElMessage({
      message: '请输入邮箱',
      type: 'warning',
      duration: 3000,
      showClose: true
    })
    return
  }

  loading.value = true
  try {
    console.log('正在发送密码重置邮件到:', form.email)
    await forgotPassword(form.email)
    ElMessage({
      message: '重置链接已发送到您的邮箱',
      type: 'success',
      duration: 3000
    })
    router.push('/login')
  } catch (error) {
    console.error('密码重置邮件发送失败:', error)
    let message = '发送失败，请稍后重试'
    if (error.response?.data?.detail) {
      message = error.response.data.detail
    } else if (error.message) {
      message = error.message
    }
    ElMessage({
      message,
      type: 'error',
      duration: 5000,
      showClose: true
    })
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
input::placeholder {
  color: #9CA3AF;
}

/* 添加背景动画效果 */
.bg-gradient-to-br {
  background-size: 300% 300%;
  animation: gradientAnimation 15s ease infinite;
}

@keyframes gradientAnimation {
  0% {
    background-position: 0% 0%;
  }
  50% {
    background-position: 100% 100%;
  }
  100% {
    background-position: 0% 0%;
  }
}
</style> 