<template>
  <div class="login-page min-h-screen bg-gradient-to-br from-blue-50 to-indigo-50 flex items-center justify-center p-4">
    <div class="w-full max-w-md relative">
      <!-- 装饰元素 -->
      <div class="absolute -top-6 -left-6 w-16 h-16 bg-blue-100 rounded-full opacity-70 blur-lg"></div>
      <div class="absolute -bottom-8 -right-8 w-20 h-20 bg-indigo-100 rounded-full opacity-70 blur-lg"></div>
      <div class="absolute top-1/2 -left-4 w-8 h-8 bg-purple-100 rounded-full opacity-60 blur-md"></div>
      
      <!-- 登录卡片 -->
      <div class="bg-white rounded-2xl shadow-lg p-8 space-y-6 border border-gray-100 relative z-10 backdrop-blur-sm">
        <!-- Logo区域 -->
        <div class="flex flex-col items-center space-y-3">
          <div class="bg-gradient-to-r from-primary to-primary-light rounded-lg p-3 shadow-md transform hover:scale-105 transition-transform duration-300">
            <h1 class="text-xl font-bold text-white">TunshuEdu</h1>
          </div>
          <h2 class="text-gray-600 font-medium">留学行业的AI工具箱</h2>
        </div>

        <!-- 登录表单 -->
        <form @submit.prevent="handleSubmit" class="space-y-5">
          <div class="space-y-4">
            <div class="group">
              <label class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
              <div class="relative">
                <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                  </svg>
                </span>
                <input
                  v-model="form.username"
                  type="text"
                  placeholder="请输入用户名"
                  class="w-full pl-10 pr-4 py-3 rounded-xl bg-gray-50 border border-gray-200 focus:border-primary focus:ring-2 focus:ring-primary focus:ring-opacity-20 transition-colors duration-200 group-hover:bg-gray-100"
                  required
                />
              </div>
            </div>

            <div class="group">
              <label class="block text-sm font-medium text-gray-700 mb-1">密码</label>
              <div class="relative">
                <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 116 0z" clip-rule="evenodd" />
                  </svg>
                </span>
                <input
                  v-model="form.password"
                  type="password"
                  placeholder="请输入密码"
                  class="w-full pl-10 pr-4 py-3 rounded-xl bg-gray-50 border border-gray-200 focus:border-primary focus:ring-2 focus:ring-primary focus:ring-opacity-20 transition-colors duration-200 group-hover:bg-gray-100"
                  required
                />
              </div>
            </div>
          </div>

          <button
            type="submit"
            class="w-full bg-gradient-to-r from-primary to-primary-light text-white py-3 px-4 rounded-xl shadow hover:shadow-md hover:translate-y-[-2px] active:translate-y-[0px] transition-all duration-200 font-medium"
            :disabled="loading"
          >
            <span v-if="!loading">登录</span>
            <span v-else class="flex items-center justify-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              登录中...
            </span>
          </button>
        </form>

        <!-- 底部链接 -->
        <div class="flex items-center justify-between pt-4 border-t border-gray-100">
          <div class="flex items-center space-x-1">
            <span class="text-gray-500 text-sm">没有账号？</span>
            <router-link to="/register" class="text-primary hover:text-primary-dark text-sm font-medium transition-colors">
              去注册
            </router-link>
          </div>
          <router-link to="/forgot-password" class="text-primary hover:text-primary-dark text-sm font-medium transition-colors">
            忘记密码？
          </router-link>
        </div>
        
        <!-- 调试信息 -->
        <div v-if="showDebugInfo" class="mt-4 p-4 bg-gray-50 rounded-xl border border-gray-200">
          <h3 class="text-sm font-medium text-gray-700 mb-2">调试信息</h3>
          <div class="text-xs text-gray-600 space-y-1">
            <p>API URL: {{ apiUrl }}</p>
            <p>Token状态: {{ authStore.token ? '已存在' : '不存在' }}</p>
            <p>错误信息: {{ errorMessage || '无' }}</p>
          </div>
          <div class="mt-2">
            <button 
              @click="clearAuthData" 
              class="text-xs text-red-600 hover:text-red-700 transition-colors"
            >
              清除认证数据
            </button>
          </div>
        </div>
      </div>
      
      <!-- 版权信息和调试开关 -->
      <div class="mt-6 text-center text-xs text-gray-500 flex justify-center items-center space-x-2">
        <span>© 2025 囤鼠科技教育平台</span>
        <button 
          @click="showDebugInfo = !showDebugInfo" 
          class="text-gray-400 hover:text-gray-600 transition-colors"
        >
          🔧
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import { clearAuth } from '@/utils/auth'

const router = useRouter()
const authStore = useAuthStore()
const loading = ref(false)
const errorMessage = ref('')
const showDebugInfo = ref(false)
const apiUrl = ref(import.meta.env.VITE_API_URL || '未设置')

const form = reactive({
  username: '',
  password: ''
})

// 清除认证数据
const clearAuthData = () => {
  clearAuth()
  ElMessage({
    message: '认证数据已清除',
    type: 'success'
  })
  location.reload()
}

onMounted(() => {
  // 在控制台打印环境信息
  console.log('环境信息:')
  console.log('- API URL:', apiUrl.value)
  console.log('- Token:', authStore.token ? '存在' : '不存在')
  console.log('- 用户:', authStore.user ? '已登录' : '未登录')
})

const handleSubmit = async () => {
  if (!form.username || !form.password) {
    ElMessage({
      message: '请输入用户名和密码',
      type: 'warning',
      duration: 5000,
      showClose: true
    })
    return
  }

  loading.value = true
  errorMessage.value = ''
  
  try {
    console.log('正在尝试登录...', { username: form.username })
    await authStore.login(form.username, form.password)
    console.log('登录成功，准备跳转')
    
    ElMessage({
      message: '登录成功',
      type: 'success',
      duration: 3000
    })
    
    // 获取重定向URL（如果存在）
    const redirectPath = router.currentRoute.value.query.redirect || '/dashboard'
    console.log('跳转到:', redirectPath)
    router.push(redirectPath)
  } catch (error) {
    console.error('登录失败:', error)
    
    // 提取错误信息
    let message = '账号或密码错误'
    if (error.response?.data?.detail) {
      message = error.response.data.detail
    } else if (error.response?.data?.error) {
      message = error.response.data.error
    } else if (error.message) {
      message = error.message
    }
    
    // 显示错误消息
    errorMessage.value = message
    ElMessage({
      message: message,
      type: 'error',
      duration: 5000,
      showClose: true,
      customClass: 'login-error-message'
    })
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
input::placeholder {
  color: #9CA3AF;
}

.login-error-message {
  z-index: 9999;
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  animation: fadeInOut 5s ease-in-out;
}

@keyframes fadeInOut {
  0% { opacity: 0; transform: translateX(-50%) translateY(-20px); }
  10% { opacity: 1; transform: translateX(-50%) translateY(0); }
  90% { opacity: 1; transform: translateX(-50%) translateY(0); }
  100% { opacity: 0; transform: translateX(-50%) translateY(-20px); }
}

/* 添加背景动画效果 */
.login-page.bg-gradient-to-br {
  background-size: 300% 300% !important;
  animation: gradientAnimation 15s ease infinite !important;
  background-image: linear-gradient(to bottom right, #EBF5FF, #EEF2FF) !important;
  background-color: transparent !important;
  overflow: hidden !important;
}

@keyframes gradientAnimation {
  0% {
    background-position: 0% 0%;
  }
  50% {
    background-position: 100% 100%;
  }
  100% {
    background-position: 0% 0%;
  }
}
</style>