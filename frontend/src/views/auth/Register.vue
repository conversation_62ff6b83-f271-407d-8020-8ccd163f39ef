<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-50 flex items-center justify-center p-4">
    <div class="w-full max-w-md relative">
      <!-- 装饰元素 -->
      <div class="absolute -top-6 -left-6 w-16 h-16 bg-blue-100 rounded-full opacity-70 blur-lg"></div>
      <div class="absolute -bottom-8 -right-8 w-20 h-20 bg-indigo-100 rounded-full opacity-70 blur-lg"></div>
      <div class="absolute top-1/4 -right-4 w-8 h-8 bg-purple-100 rounded-full opacity-60 blur-md"></div>
      
      <!-- 注册卡片 -->
      <div class="bg-white rounded-2xl shadow-lg p-8 space-y-6 border border-gray-100 relative z-10 backdrop-blur-sm">
        <!-- Logo区域 -->
        <div class="flex flex-col items-center space-y-3">
          <div class="bg-gradient-to-r from-primary to-primary-light rounded-lg p-3 shadow-md transform hover:scale-105 transition-transform duration-300">
            <h1 class="text-xl font-bold text-white">TunshuEdu</h1>
          </div>
          <h2 class="text-gray-600 font-medium">留学行业的AI工具箱</h2>
        </div>

        <!-- 注册表单 -->
        <form @submit.prevent="handleSubmit" class="space-y-5">
          <div class="space-y-4">
            <div class="group">
              <label class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
              <div class="relative">
                <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                  </svg>
                </span>
                <input
                  v-model="form.username"
                  type="text"
                  placeholder="请输入用户名"
                  class="w-full pl-10 pr-4 py-3 rounded-xl bg-gray-50 border border-gray-200 focus:border-primary focus:ring-2 focus:ring-primary focus:ring-opacity-20 transition-colors duration-200 group-hover:bg-gray-100"
                  required
                />
              </div>
            </div>

            <div class="group">
              <label class="block text-sm font-medium text-gray-700 mb-1">邮箱</label>
              <div class="relative">
                <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                  </svg>
                </span>
                <input
                  v-model="form.email"
                  type="email"
                  placeholder="请输入邮箱"
                  class="w-full pl-10 pr-4 py-3 rounded-xl bg-gray-50 border border-gray-200 focus:border-primary focus:ring-2 focus:ring-primary focus:ring-opacity-20 transition-colors duration-200 group-hover:bg-gray-100"
                  required
                  pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$"
                />
              </div>
            </div>

            <div class="group">
              <label class="block text-sm font-medium text-gray-700 mb-1">密码</label>
              <div class="relative">
                <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 116 0z" clip-rule="evenodd" />
                  </svg>
                </span>
                <input
                  v-model="form.password"
                  type="password"
                  placeholder="请输入密码"
                  class="w-full pl-10 pr-4 py-3 rounded-xl bg-gray-50 border border-gray-200 focus:border-primary focus:ring-2 focus:ring-primary focus:ring-opacity-20 transition-colors duration-200 group-hover:bg-gray-100"
                  required
                  minlength="6"
                />
              </div>
            </div>

            <div class="group">
              <label class="block text-sm font-medium text-gray-700 mb-1">确认密码</label>
              <div class="relative">
                <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 116 0z" clip-rule="evenodd" />
                  </svg>
                </span>
                <input
                  v-model="form.confirmPassword"
                  type="password"
                  placeholder="请再次输入密码"
                  class="w-full pl-10 pr-4 py-3 rounded-xl bg-gray-50 border border-gray-200 focus:border-primary focus:ring-2 focus:ring-primary focus:ring-opacity-20 transition-colors duration-200 group-hover:bg-gray-100"
                  required
                  minlength="6"
                />
              </div>
            </div>
          </div>

          <button
            type="submit"
            class="w-full bg-gradient-to-r from-primary to-primary-light text-white py-3 px-4 rounded-xl shadow hover:shadow-md hover:translate-y-[-2px] active:translate-y-[0px] transition-all duration-200 font-medium"
            :disabled="loading"
          >
            <span v-if="!loading">注册</span>
            <span v-else class="flex items-center justify-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              注册中...
            </span>
          </button>
        </form>

        <!-- 底部链接 -->
        <div class="flex items-center justify-center pt-4 border-t border-gray-100">
          <div class="flex items-center space-x-1">
            <span class="text-gray-500 text-sm">已有账号？</span>
            <router-link to="/login" class="text-primary hover:text-primary-dark text-sm font-medium transition-colors">
              去登录
            </router-link>
          </div>
        </div>
      </div>
      
      <!-- 版权信息 -->
      <div class="mt-6 text-center text-xs text-gray-500">
        <span>© 2025 囤鼠科技教育平台</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()
const loading = ref(false)

const form = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: ''
})

const handleSubmit = async () => {
  // 验证表单
  if (!form.username || !form.email || !form.password || !form.confirmPassword) {
    ElMessage.warning('请填写所有必填字段')
    return
  }

  // 验证邮箱格式
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
  if (!emailRegex.test(form.email)) {
    ElMessage.warning('请输入有效的邮箱地址')
    return
  }

  // 验证密码
  if (form.password.length < 6) {
    ElMessage.warning('密码长度不能少于6位')
    return
  }

  // 验证两次密码是否一致
  if (form.password !== form.confirmPassword) {
    ElMessage.warning('两次输入的密码不一致')
    return
  }

  loading.value = true
  try {
    console.log('正在注册用户:', form.username, form.email)
    await authStore.register(form.username, form.email, form.password)
    ElMessage.success('注册成功')
    router.push('/login')
  } catch (error) {
    console.error('注册失败:', error)
    let message = '注册失败，请稍后重试'
    if (error.response?.data?.detail) {
      message = error.response.data.detail
    } else if (error.message) {
      message = error.message
    }
    ElMessage.error(message)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
input::placeholder {
  color: #9CA3AF;
}

/* 添加背景动画效果 */
.bg-gradient-to-br {
  background-size: 300% 300%;
  animation: gradientAnimation 15s ease infinite;
}

@keyframes gradientAnimation {
  0% {
    background-position: 0% 0%;
  }
  50% {
    background-position: 100% 100%;
  }
  100% {
    background-position: 0% 0%;
  }
}
</style> 