/**
 * TunshuEdu 全局样式表
 *
 * 本文件包含全局CSS样式定义，主要包括：
 * 1. Tailwind CSS 框架导入
 * 2. 全局变量定义（颜色系统）
 * 3. 基础样式重置
 * 4. Element Plus 组件样式定制
 * 5. 自定义组件样式
 * 6. 响应式布局样式
 * 7. 动画和过渡效果
 */

/* Tailwind CSS 框架导入 */
@tailwind base;    /* 基础样式 */
@tailwind components;  /* 组件样式 */
@tailwind utilities;   /* 工具类 */

/**
 * 全局CSS变量定义
 *
 * 定义应用的主要颜色系统，这些变量可以在整个应用中使用
 * 与Tailwind配置中的颜色保持一致，便于统一管理
 */
:root {
  /* 主题色系 */
  --primary: #4f46e5;      /* 主色调 - 靛蓝色 */
  --primary-dark: #4338ca; /* 主色调深色版 - 用于悬停状态 */
  --primary-light: #818cf8; /* 主色调浅色版 - 用于背景和次要元素 */

  /* 功能色系 */
  --success: #10b981;      /* 成功状态 - 绿色 */
  --warning: #f59e0b;      /* 警告状态 - 橙色 */
  --danger: #ef4444;       /* 危险/错误状态 - 红色 */
  --info: #3b82f6;         /* 信息状态 - 蓝色 */
}

/**
 * 全局基础样式
 *
 * 设置应用的基础样式，包括背景色、文字颜色、字体等
 * 应用于整个文档的基础样式设置
 */
body {
  @apply bg-gray-50 text-gray-900;
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;  /* 在WebKit浏览器中平滑字体 */
  -moz-osx-font-smoothing: grayscale;   /* 在Firefox中平滑字体 */
  font-feature-settings: 'kern' 1, 'liga' 1;  /* 启用字体特性 */
  text-rendering: optimizeLegibility;  /* 优化文本渲染 */
}

/**
 * Element Plus 组件库主题定制
 *
 * 通过覆盖Element Plus的默认样式，使其与应用的设计系统保持一致
 * 主要包括按钮、表单、对话框等组件的样式定制
 */
.el-button--primary {
  @apply bg-primary hover:bg-primary-dark border-primary hover:border-primary-dark;
}

.el-button--primary.is-plain {
  @apply text-primary border-primary hover:bg-primary hover:text-white;
}

/**
 * 按钮大小优化
 *
 * 调整Element Plus按钮的大小，使其更符合设计规范
 * 定义了small和mini两种尺寸的按钮样式
 */
.el-button--small {
  @apply text-xs py-1 px-3;  /* 小号按钮文字和内边距 */
  height: 32px;  /* 固定高度，确保一致性 */
}

.el-button--mini {
  @apply text-xs py-0.5 px-2;  /* 迷你按钮文字和内边距 */
  height: 24px;  /* 固定高度，确保一致性 */
}

/**
 * 按钮样式增强
 *
 * 增强Element Plus按钮的视觉效果，添加过渡动画和阴影
 * 使按钮更具立体感和交互反馈
 */
.el-button {
  @apply font-medium transition-all duration-200;  /* 字体加粗和过渡动画 */
  border-radius: 0.375rem;  /* 圆角大小 */
}

.el-button--primary {
  @apply shadow-sm;  /* 主要按钮添加轻微阴影 */
}

.el-button--primary:hover {
  @apply shadow;  /* 悬停时增加阴影深度，提升立体感 */
}

.el-button--link {
  @apply p-1.5 hover:bg-gray-100 rounded-md;  /* 链接按钮悬停效果 */
}

/**
 * 图标按钮样式
 *
 * 为仅包含图标的按钮定义固定大小，确保视觉一致性
 * 适用于工具栏和操作列的图标按钮
 */
.icon-btn {
  @apply w-8 h-8 flex items-center justify-center !p-0;  /* 固定宽高和内边距 */
  min-width: 2rem !important;  /* 防止按钮文本影响宽度 */
}

/* 对话框样式增强 */
.el-dialog {
  @apply rounded-xl overflow-hidden shadow-lg;
  border: 1px solid rgba(0,0,0,0.05);
}

.el-dialog__header {
  @apply py-4 px-6 bg-gray-50 border-b border-gray-100;
}

.el-dialog__title {
  @apply text-base font-medium text-gray-800;
}

.el-dialog__headerbtn {
  @apply top-4 right-4;
}

.el-dialog__body {
  @apply p-6;
}

.el-dialog__footer {
  @apply py-4 px-6 border-t border-gray-100 bg-gray-50;
}

.el-menu {
  @apply border-0;
}

.el-menu-item.is-active {
  @apply text-primary bg-primary-light bg-opacity-10;
}

.el-menu-item:hover {
  @apply bg-gray-100;
}

/* 卡片组件优化 */
.el-card {
  @apply border-0 shadow-sm;
  border-radius: 0.5rem !important;
}

.el-card__header {
  @apply border-b border-gray-100 bg-gray-50 bg-opacity-50 py-4;
}

/* 表单组件优化 */
.el-input__inner {
  @apply rounded-lg border-gray-200;
}

.el-input.is-focus .el-input__inner {
  @apply border-primary;
}

/* 表单样式增强 */
.el-form-item__label {
  @apply text-sm text-gray-600 font-medium;
}

.el-form-item__content {
  @apply text-sm;
}

.el-input__inner, .el-select .el-input__inner, .el-textarea__inner {
  @apply border-gray-200 focus:border-primary text-gray-700 shadow-sm;
  transition: all 0.2s;
}

.el-input__inner:hover, .el-select .el-input__inner:hover, .el-textarea__inner:hover {
  @apply border-gray-300;
}

/* 表格样式增强 */
.el-table th {
  @apply bg-gray-50 text-gray-600 text-xs font-semibold uppercase tracking-wider;
}

.el-table td {
  @apply text-sm text-gray-700;
}

.el-table--striped .el-table__body tr.el-table__row--striped td {
  @apply bg-gray-50 bg-opacity-50;
}

/* 整体交互反馈优化 */
button, a, .el-button {
  @apply transition-all duration-200;
}

/* 边距优化 - 提供一致的8px倍数间距 */
.section-padding {
  @apply py-6 px-6;
}

.card-padding {
  @apply p-5;
}

/**
 * 下拉菜单样式优化
 *
 * 美化Element Plus的下拉菜单组件，使其更符合整体设计风格
 * 统一颜色为紫色主题，增强视觉效果
 */
.el-dropdown-menu {
  @apply rounded-lg shadow-lg border border-gray-100 overflow-hidden;
  padding: 0 !important;
}

.el-dropdown-menu__item {
  @apply text-sm py-2.5 px-4 transition-colors;
}

.el-dropdown-menu__item:hover {
  @apply bg-primary bg-opacity-5 text-primary;
}

.el-dropdown-menu__item:focus {
  @apply bg-primary bg-opacity-5 text-primary;
}

.el-dropdown-menu__item--divided:before {
  @apply bg-gray-100;
  height: 1px;
  margin: 0;
}

.el-dropdown-menu__item i,
.el-dropdown-menu__item .material-icons-outlined {
  @apply text-primary;
}

/**
 * 开关组件样式优化
 *
 * 将Element Plus的开关组件颜色从默认的蓝色改为紫色
 * 统一应用的主题色系
 */
.el-switch.is-checked .el-switch__core {
  background-color: var(--primary) !important;
  border-color: var(--primary) !important;
}

.el-switch.is-checked:hover .el-switch__core {
  background-color: var(--primary-dark) !important;
  border-color: var(--primary-dark) !important;
}

.el-switch:not(.is-checked) .el-switch__core {
  border-color: #dcdfe6 !important;
}

.el-switch:not(.is-checked):hover .el-switch__core {
  border-color: #c0c4cc !important;
}

.el-switch__core {
  transition: all 0.2s ease;
}

/* 阴影优化 */
.shadow-card {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.05);
}

.shadow-card-hover:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* 过渡动画 */
.page-enter-active,
.page-leave-active {
  transition: all 0.2s;
}

.page-enter-from,
.page-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

/* 响应式布局辅助类 */
.responsive-padding {
  @apply px-4 sm:px-6 md:px-8;
}

/* 增强的按钮样式 */
.btn {
  @apply px-4 py-2 rounded-lg font-medium text-sm transition-all duration-200;
}

.btn-primary {
  @apply bg-primary text-white hover:bg-primary-dark;
}

.btn-secondary {
  @apply bg-gray-100 text-gray-700 hover:bg-gray-200;
}

.btn-outline {
  @apply border border-gray-300 text-gray-700 hover:bg-gray-50;
}

/**
 * 专业卡片组件样式
 *
 * 定义了一套统一的卡片组件样式，包括卡片头部、标题、内容和底部
 * 这些样式可以用于创建一致的卡片式UI组件
 */
.pro-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-100;  /* 卡片基础样式 */
}

.pro-card-header {
  @apply border-b border-gray-100 flex items-center justify-between;  /* 卡片头部样式 */
  height: 3.5rem; /* 统一表头高度，确保视觉一致性 */
  padding: 0 0.875rem;  /* 左右内边距 */
}

.pro-card-title {
  @apply text-sm font-medium text-gray-700 flex items-center;  /* 卡片标题样式 */
}

.pro-card-title .icon {
  @apply mr-2 text-gray-500 text-sm;  /* 标题图标样式 */
}

.pro-card-body {
  @apply p-4;  /* 卡片内容区域内边距 */
}

.pro-card-footer {
  @apply p-3.5 border-t border-gray-100 flex justify-end;  /* 卡片底部样式，右对齐按钮 */
}

/**
 * 自定义滚动条样式
 *
 * 美化默认的浏览器滚动条，使其更符合整体UI风格
 * 注意：这些样式仅在WebKit内核的浏览器中生效（Chrome、Safari等）
 */
::-webkit-scrollbar {
  width: 6px;    /* 垂直滚动条宽度 */
  height: 6px;   /* 水平滚动条高度 */
}

::-webkit-scrollbar-track {
  background: #f1f1f1;  /* 滚动条轨道背景色 */
  border-radius: 3px;   /* 轨道圆角 */
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;  /* 滚动条滑块颜色 */
  border-radius: 3px;   /* 滑块圆角 */
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;  /* 滑块悬停颜色 */
}

/**
 * 过渡动画效果
 *
 * 定义了常用的Vue过渡动画，用于组件的进入和离开效果
 * 可以通过<transition name="fade">包裹组件来使用
 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;  /* 淡入淡出动画，持续0.3秒 */
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;  /* 开始和结束时的透明度为0 */
}

/**
 * 消息框样式优化
 *
 * 美化Element Plus的消息框组件，使其更符合整体设计风格
 * 特别是归档确认对话框的样式优化
 */
.el-message-box {
  @apply rounded-xl overflow-hidden shadow-lg;
  border: 1px solid rgba(0,0,0,0.05) !important;
}

.el-message-box__header {
  @apply py-4 px-6 bg-gray-50 border-b border-gray-100;
  padding-top: 16px !important;
  padding-bottom: 16px !important;
}

.el-message-box__title {
  @apply text-base font-medium text-gray-800;
}

.el-message-box__headerbtn {
  @apply top-4 right-4;
}

.el-message-box__content {
  @apply p-6;
  padding-top: 20px !important;
  padding-bottom: 20px !important;
}

.el-message-box__message p {
  @apply text-gray-600 text-sm;
}

.el-message-box__btns {
  @apply py-4 px-6 border-t border-gray-100 bg-gray-50 flex justify-end;
  padding-top: 12px !important;
  padding-bottom: 12px !important;
}

/* 归档确认对话框特定样式 */
.archive-confirm-dialog {
  width: 400px !important;
}

.archive-confirm-dialog .el-message-box__btns .el-button--primary {
  @apply bg-primary border-primary text-white;
}

.archive-confirm-dialog .el-message-box__btns .el-button--primary:hover {
  @apply bg-primary-dark border-primary-dark;
}

.archive-confirm-dialog .el-message-box__btns .el-button:not(.el-button--primary) {
  @apply text-gray-700 border-gray-300;
}

.archive-confirm-dialog .el-message-box__btns .el-button:not(.el-button--primary):hover {
  @apply bg-gray-50 text-gray-900;
}

/**
 * 响应式布局适配
 *
 * 针对小屏幕设备（如手机和平板）的样式调整
 * 主要是简化侧边栏，只显示图标不显示文字
 */
@media (max-width: 768px) {
  /* 侧边栏宽度减小 */
  .el-aside {
    @apply w-16;  /* 设置为固定宽度16（64px） */
  }

  /* 隐藏菜单项文字，只显示图标 */
  .el-menu-item span {
    @apply hidden;
  }

  /* 隐藏子菜单标题文字，只显示图标 */
  .el-sub-menu__title span {
    @apply hidden;
  }
}

/* 头像样式统一定义 */
.avatar-primary {
  background-color: rgba(79, 70, 229, 0.1) !important;
  color: #4F46E5 !important;
}

/* 修复渐变背景类 */
.bg-gradient-to-br.from-primary.to-primary-light {
  background: linear-gradient(to bottom right, #4F46E5, #818CF8) !important;
}

/* 主要色定义 */
.bg-primary {
  background-color: #4F46E5 !important;
}

.bg-primary-light {
  background-color: #818CF8 !important;  
}

.text-primary {
  color: #4F46E5 !important;
}

.bg-primary.bg-opacity-10 {
  background-color: rgba(79, 70, 229, 0.1) !important;
}

/* 确保客户列表头像统一样式 */
.client-list-page .client-table .rounded-full {
  background-color: rgba(79, 70, 229, 0.1) !important;
  color: #4F46E5 !important;
}

/* 强制覆盖 */
.w-8.h-8.rounded-full.bg-gray-100,
.w-8.h-8.rounded-full.bg-gray-50 {
  background-color: rgba(79, 70, 229, 0.1) !important;
  color: #4F46E5 !important;
}

/* 修复渐变背景按钮 */
.bg-gradient-to-r.from-primary.to-primary-light {
  background: linear-gradient(to right, #4F46E5, #818CF8) !important;
  background-color: #4F46E5 !important;
}

/* 修复蓝色背景 */
.bg-blue-50 {
  background-color: rgb(239, 246, 255) !important;
}