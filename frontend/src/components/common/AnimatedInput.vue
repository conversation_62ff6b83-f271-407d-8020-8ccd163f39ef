<template>
  <div class="animated-input-container" :class="{ 'is-active': isActive || hasValue }">
    <!-- 浮动标签 -->
    <div 
      class="animated-label"
      :class="{ 
        'is-active': isActive || hasValue,
        'is-required': required
      }"
    >
      {{ label }}
      <!-- 仅在激活状态显示必选标记，避免重复 -->
      <span v-if="required && (isActive || hasValue)" class="text-red-600">*</span>
    </div>
    
    <!-- 输入区域 - 根据类型显示不同内容 -->
    <div class="input-wrapper" @click="handleWrapperClick">
      <!-- 普通输入框 -->
      <template v-if="type === 'input'">
        <div 
          ref="inputRef"
          class="animated-input-wrapper"
          :class="{ 'is-active': isActive }"
        >
          <input
            :value="modelValue"
            :placeholder="isActive ? placeholder : ''"
            class="animated-input"
            @focus="handleFocus"
            @blur="handleBlur"
            @input="$emit('update:modelValue', $event.target.value)"
          />
        </div>
      </template>
      
      <!-- 下拉选择框 -->
      <template v-else-if="type === 'select'">
        <div 
          ref="inputRef"
          class="animated-select"
          :class="{ 'is-active': isActive }"
          tabindex="0"
          @focus="handleFocus"
          @blur="handleBlur($event, true)"
        >
          <!-- 当激活且无值时显示 placeholder -->
          <div v-if="isActive && !hasValue" class="placeholder-text">
            {{ placeholder }}
          </div>
          <!-- 当有值且 (是多选 或 未激活) 时显示已选值 -->
          <div class="selected-value" v-else-if="hasValue && (props.multiple || !isActive)">
            {{ getDisplayValue }}
          </div>
          
          <!-- 传送门，将下拉菜单移到body直接子元素，避免嵌套和overflow限制 -->
          <Teleport to="body">
            <div 
              v-if="isActive" 
              class="options-container" 
              :style="dropdownStyle"
              @mousedown.prevent
            >
              <div
                v-for="(option, index) in options"
                :key="index"
                class="option-item"
                :class="{ 'is-selected': isSelected(option) }"
                @mousedown="handleOptionSelect(option)"
              >
                {{ option.label }}
                <span v-if="option.secondaryText" class="secondary-text">{{ option.secondaryText }}</span>
              </div>
            </div>
          </Teleport>
        </div>
      </template>

      <!-- 自动完成输入框 -->
      <template v-else-if="type === 'autocomplete'">
        <div 
          ref="inputRef"
          class="animated-autocomplete"
          :class="{ 'is-active': isActive }"
        >
          <input
            :value="modelValue"
            :placeholder="isActive ? placeholder : ''"
            class="autocomplete-input"
            @focus="handleFocus"
            @blur="handleBlur($event, true)"
            @input="handleAutoCompleteInput($event)"
            @click="isActive = true"
          />
          
          <!-- 传送门，将下拉菜单移到body直接子元素 -->
          <Teleport to="body">
            <div 
              v-if="isActive && suggestions.length > 0" 
              class="options-container" 
              :style="dropdownStyle"
              @mousedown.prevent
            >
              <div
                v-for="(suggestion, index) in suggestions"
                :key="index"
                class="option-item"
                @mousedown="handleSuggestionSelect(suggestion)"
              >
                {{ typeof suggestion === 'object' ? suggestion.label || suggestion.value : suggestion }}
                <span v-if="typeof suggestion === 'object' && suggestion.secondaryText" class="secondary-text">
                  {{ suggestion.secondaryText }}
                </span>
              </div>
            </div>
          </Teleport>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onBeforeUnmount, reactive } from 'vue';

const props = defineProps({
  modelValue: {
    type: [String, Number, Array],
    default: ''
  },
  label: {
    type: String,
    required: true
  },
  placeholder: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: 'input',
    validator: (value) => ['input', 'select', 'autocomplete'].includes(value)
  },
  options: {
    type: Array,
    default: () => []
  },
  multiple: {
    type: Boolean,
    default: false
  },
  required: {
    type: Boolean,
    default: false
  },
  fetchSuggestions: {
    type: Function,
    default: null
  }
});

const emit = defineEmits(['update:modelValue', 'focus', 'blur', 'change', 'input']);

// 响应式状态
const isActive = ref(false);
const inputRef = ref(null);
const suggestions = ref([]);

// 下拉框样式
const dropdownStyle = reactive({
  position: 'fixed',
  top: '0px',
  left: '0px',
  width: '100px',
  backgroundColor: '#ffffff',
  zIndex: 10000
});

// 计算值是否存在
const hasValue = computed(() => {
  if (Array.isArray(props.modelValue)) {
    return props.modelValue.length > 0;
  }
  return props.modelValue !== null && props.modelValue !== undefined && props.modelValue !== '';
});

// 获取显示值
const getDisplayValue = computed(() => {
  if (props.type === 'select') {
    if (props.multiple && Array.isArray(props.modelValue)) {
      if (props.modelValue.length === 0) return '';
      
      return props.options
        .filter(option => props.modelValue.includes(option.value))
        .map(option => option.label)
        .join(', ');
    } else {
      const selected = props.options.find(option => option.value === props.modelValue);
      return selected ? selected.label : '';
    }
  }
  return props.modelValue;
});

// 检查选项是否被选中
const isSelected = (option) => {
  if (props.multiple && Array.isArray(props.modelValue)) {
    return props.modelValue.includes(option.value);
  }
  return props.modelValue === option.value;
};

// 设置下拉框位置
const setDropdownPosition = () => {
  if (!inputRef.value || !isActive.value) return;
  
  const inputEl = inputRef.value;
  const rect = inputEl.getBoundingClientRect();
  
  // 更新下拉框位置
  dropdownStyle.top = `${rect.bottom + window.scrollY}px`;
  dropdownStyle.left = `${rect.left + window.scrollX}px`;
  dropdownStyle.width = `${rect.width}px`;
  
  // 防止下拉框超出屏幕底部
  const windowHeight = window.innerHeight;
  const dropdownHeight = Math.min(21 * 16, props.options.length * 35); // 估计高度
  
  if (rect.bottom + dropdownHeight > windowHeight) {
    // 如果下拉框会超出屏幕底部，则显示在输入框上方
    dropdownStyle.top = `${rect.top + window.scrollY - dropdownHeight}px`;
  }
};

// 处理容器点击
const handleWrapperClick = () => {
  if (inputRef.value) {
    inputRef.value.focus();
  }
  isActive.value = true;
  
  // 点击时更新下拉框位置
  setDropdownPosition();
};

// 处理获得焦点
const handleFocus = () => {
  isActive.value = true;
  emit('focus');
  
  // 对于自动完成输入框，在获得焦点时就显示建议列表
  if (props.type === 'autocomplete' && props.fetchSuggestions && typeof props.fetchSuggestions === 'function') {
    props.fetchSuggestions('', (results) => {
      // 显示更多的建议选项（不限制数量）
      suggestions.value = results || [];
      // 设置下拉框位置
      setDropdownPosition();
    });
  } else {
    // 非自动完成类型也需要设置下拉框位置
    setDropdownPosition();
  }
};

// 处理全局点击事件，用于关闭下拉框
const handleGlobalClick = (event) => {
  if (!isActive.value) return;

  const targetEl = event.target;
  const containerEl = inputRef.value?.closest('.animated-input-container');
  
  // 如果点击的不是组件内部元素，则关闭下拉框
  if (containerEl && !containerEl.contains(targetEl) && 
      !targetEl.classList.contains('option-item') && 
      !targetEl.closest('.options-container')) {
    isActive.value = false;
    emit('blur');
  }
};

// 处理失去焦点
const handleBlur = (event, isSelectEvent = false) => {
  // 如果是选择事件，不立即关闭下拉菜单
  if (isSelectEvent) {
    // 延迟处理，避免与选项点击事件冲突
    setTimeout(() => {
      // 此处不直接关闭，而是由全局点击事件处理
    }, 50);
  } else {
    isActive.value = false;
    emit('blur');
  }
};

// 处理选项选择
const handleOptionSelect = (option) => {
  if (props.multiple) {
    const newValue = Array.isArray(props.modelValue) ? [...props.modelValue] : [];
    const index = newValue.indexOf(option.value);
    
    if (index === -1) {
      newValue.push(option.value);
    } else {
      newValue.splice(index, 1);
    }
    
    emit('update:modelValue', newValue);
    emit('change', newValue);
    
    // 多选模式下保持下拉框打开
    setDropdownPosition();
  } else {
    // 单选模式下选择后立即关闭
    emit('update:modelValue', option.value);
    emit('change', option.value);
    isActive.value = false;
  }
};

// 处理自动完成输入
const handleAutoCompleteInput = (event) => {
  const value = event.target.value;
  emit('update:modelValue', value);
  emit('input', value);
  
  if (props.fetchSuggestions && typeof props.fetchSuggestions === 'function') {
    // 确保isActive为true以显示下拉选项
    isActive.value = true;
    
    // 即使没有输入值也显示所有建议
    props.fetchSuggestions(value, (results) => {
      suggestions.value = results || [];
      setDropdownPosition();
      // 如果有返回建议，确保isActive保持true
      if (results && results.length > 0) {
        isActive.value = true;
      } else if (value && value.trim()) {
        // 如果用户已输入内容但没有匹配结果，稍后关闭下拉框
        setTimeout(() => {
          isActive.value = false;
        }, 300);
      }
    });
  }
};

// 处理建议选择
const handleSuggestionSelect = (suggestion) => {
  const value = typeof suggestion === 'object' ? suggestion.value : suggestion;
  emit('update:modelValue', value);
  emit('change', value); // 增加触发change事件
  // 选择后立即关闭下拉框
  isActive.value = false;
  suggestions.value = [];
};

// 添加和移除全局点击事件监听
onMounted(() => {
  document.addEventListener('click', handleGlobalClick);
  window.addEventListener('resize', setDropdownPosition);
  document.addEventListener('scroll', setDropdownPosition, true);
});

onBeforeUnmount(() => {
  document.removeEventListener('click', handleGlobalClick);
  window.removeEventListener('resize', setDropdownPosition);
  document.removeEventListener('scroll', setDropdownPosition, true);
});

// 监听 isActive 状态变化，更新下拉框位置
watch(() => isActive.value, (newVal) => {
  if (newVal) {
    setDropdownPosition();
  }
});
</script>

<style>
/* 全局样式，解决下拉框透明问题 */
.options-container {
  background-color: #ffffff !important;
  max-height: 21rem;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  margin-top: 0.25rem;
}

.options-container .option-item {
  padding: 0.4rem 0.75rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 0.875rem;
  display: flex;
  flex-direction: column;
  background-color: #ffffff !important;
}

.options-container .option-item:hover {
  background-color: #f3f4f6 !important;
}

.options-container .option-item.is-selected {
  background-color: #eff6ff !important;
  color: #4F46E5;
  font-weight: 500;
}

.options-container .secondary-text {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.125rem;
}

/* 添加全局统一间距类 */
.form-section-content .animated-input-container:last-child {
  margin-bottom: 0;
}

/* 修复网格布局间距问题 */
.grid.grid-cols-2 .animated-input-container {
  margin-bottom: 0;
}

/* 确保网格布局中的容器有固定的底部边距 */
.grid.grid-cols-2 {
  margin-bottom: 0.75rem;
}

/* 确保网格布局中输入框的一致性 */
.grid.grid-cols-2 .animated-input-wrapper,
.grid.grid-cols-2 .animated-autocomplete,
.grid.grid-cols-2 .animated-select {
  height: 2.5rem !important;
}

/* 确保网格布局中输入值的垂直位置一致 - 使用统一的边距 */
.grid.grid-cols-2 .animated-input,
.grid.grid-cols-2 .autocomplete-input {
  padding: 1rem 0.4rem 0.3rem !important;
}

.grid.grid-cols-2 .autocomplete-input {
  padding-right: 2rem !important;
}

.grid.grid-cols-2 .selected-value,
.grid.grid-cols-2 .placeholder-text {
  padding-top: 0.5rem !important;
  padding-bottom: 0.3rem !important;
}

/* 统一所有输入框内部文本的水平和垂直位置 */
.animated-input,
.autocomplete-input {
  padding-left: 0.4rem !important;
  padding-right: 0.4rem !important;
}

/* 为有下拉箭头的输入框保留右侧空间 */
.autocomplete-input {
  padding-right: 2rem !important;
}

.animated-select {
  padding-left: 0.4rem !important;
  padding-right: 2rem !important;
}
</style>

<style scoped>
.animated-input-container {
  position: relative;
  margin-bottom: 0.5rem;
  width: 100%;
  height: 2.5rem !important; /* 固定高度确保一致性 */
}

.animated-label {
  position: absolute;
  left: 0.4rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.8125rem;
  color: #6b7280;
  transition: all 0.2s ease-in-out;
  pointer-events: none;
  padding: 0;
  background-color: transparent;
  z-index: 1;
}

/* 确保所有输入类型的标签位置一致 */
.animated-input-container .animated-label {
  left: 0.4rem !important;
}

.animated-label.is-active {
  top: 0rem;
  left: 0.4rem !important;
  transform: translateY(0) scale(0.85);
  transform-origin: left center;
  color: #4F46E5;
  background-color: transparent;
  font-weight: 500;
}

.animated-label.is-required:not(.is-active)::after {
  content: "*";
  color: #ef4444;
  margin-left: 0.005rem;
  position: relative;
  top: -0.125rem;
}

.input-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.animated-input-wrapper,
.animated-autocomplete {
  position: relative;
  padding: 0;
  display: flex;
  align-items: center;
  min-height: 2.5rem;
  width: 100%;
  height: 2.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background-color: #fff;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.animated-input-wrapper.is-active,
.animated-autocomplete.is-active {
  border-color: #4F46E5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.animated-input,
.autocomplete-input {
  width: 100%;
  height: 100%;
  padding: 1rem 0.4rem 0.3rem;
  border: none;
  outline: none;
  background: transparent;
  color: #374151;
  font-size: 0.8125rem;
  z-index: 1;
}

/* 为自动完成输入框添加右侧内边距，为下拉箭头留出空间 */
.autocomplete-input {
  padding-right: 2rem;
}

.animated-select {
  cursor: pointer;
  user-select: none;
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
  height: 2.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background-color: #fff;
  transition: all 0.2s ease;
  box-sizing: border-box;
  padding: 1rem 2rem 0.3rem 0.4rem;
}

.animated-select.is-active {
  border-color: #4F46E5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* 下拉箭头样式 - 添加到两种输入类型 */
.animated-select::after,
.animated-autocomplete::after {
  content: '';
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  width: 0.75rem;
  height: 0.75rem;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236B7280'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  transition: transform 0.2s ease;
}

.animated-select.is-active::after,
.animated-autocomplete.is-active::after {
  transform: translateY(-50%) rotate(180deg);
}

.selected-value {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 0.8125rem;
  color: #374151;
  padding-top: 0.5rem;
  padding-bottom: 0.3rem;
  padding-left: 0 !important;
}

.placeholder-text {
  font-size: 0.8125rem;
  color: #9ca3af;
  padding-top: 0.5rem;
  padding-bottom: 0.3rem;
  padding-left: 0 !important;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style> 