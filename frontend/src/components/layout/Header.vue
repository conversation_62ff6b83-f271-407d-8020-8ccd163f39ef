<template>
  <header class="bg-white shadow-sm h-16 flex items-center justify-between px-6 border-b border-gray-200">
    <!-- 左侧 -->
    <div class="flex items-center">
      <h1 class="text-lg font-medium text-gray-800">{{ currentTitle }}</h1>
    </div>

    <!-- 右侧用户信息 -->
    <div class="flex items-center space-x-4">
      <span class="text-gray-600 hidden md:inline-block">{{ displayName }}</span>
      <el-dropdown trigger="click">
        <div class="avatar-wrapper flex items-center cursor-pointer transition-all hover:opacity-80 rounded-lg p-1">
          <div class="user-avatar w-9 h-9 rounded-full flex items-center justify-center text-white shadow-sm">
            <span class="text-sm font-medium">{{ authStore.userInitial() }}</span>
          </div>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <div class="px-4 py-3 border-b border-gray-100">
              <p class="text-sm font-medium text-gray-900">{{ displayName }}</p>
              <p class="text-xs text-gray-500 mt-0.5">{{ authStore.user?.email }}</p>
            </div>
            <el-dropdown-item @click="handleSettings" class="h-10">
              <div class="flex items-center h-full">
                <span class="material-icons-outlined text-sm mr-2">settings</span>
                账户设置
              </div>
            </el-dropdown-item>
            <el-dropdown-item @click="handleLogout" class="h-10">
              <div class="flex items-center h-full">
                <span class="material-icons-outlined text-sm mr-2">logout</span>
                退出登录
              </div>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </header>
</template>

<script setup>
import { computed, onMounted, ref, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// 获取当前页面标题
const currentTitle = computed(() => {
  return route.meta.title || ''
})

// 用户显示名称
const displayName = computed(() => {
  return authStore.user?.nickname || authStore.user?.username || '用户'
})

// 处理账户设置
const handleSettings = () => {
  router.push('/account/settings')
}

// 处理退出登录
const handleLogout = async () => {
  try {
    authStore.logout()
    ElMessage.success('退出成功')
  } catch (error) {
    console.error('Logout failed:', error)
    ElMessage.error('退出失败')
  }
}
</script>

<style scoped>
.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

/* 头像样式 - 使用CSS渐变替代TailwindCSS类 */
.user-avatar {
  background: linear-gradient(to right, #4F46E5, #818CF8);
}

/* 头像样式 - 移除焦点框 */
.avatar-wrapper:focus,
.avatar-wrapper:active,
:deep(.avatar-wrapper:focus-visible) {
  outline: none !important;
  box-shadow: none !important;
  border: none !important;
}

/* 下拉菜单样式优化 */
:deep(.el-dropdown-menu) {
  min-width: 180px;
  padding: 4px 0;
}

:deep(.el-dropdown-menu__item) {
  height: 40px;
  line-height: 40px;
  padding: 0 12px;
  margin: 0;
}

:deep(.el-dropdown-menu__item:hover),
:deep(.el-dropdown-menu__item:focus),
:deep(.el-dropdown-menu__item:active) {
  color: #6366f1 !important; /* 统一的紫色 */
  background-color: rgba(99, 102, 241, 0.05) !important; /* 极淡的紫色背景 */
  outline: none !important;
  border-color: transparent !important;
  box-shadow: none !important;
}

:deep(.el-dropdown-menu__item .material-icons-outlined) {
  font-size: 18px;
  color: var(--el-text-color-regular);
  margin-right: 8px;
}

:deep(.el-dropdown-menu__item:hover .material-icons-outlined),
:deep(.el-dropdown-menu__item:focus .material-icons-outlined),
:deep(.el-dropdown-menu__item:active .material-icons-outlined) {
  color: #6366f1 !important; /* 与悬停文字相同的紫色 */
}

/* 覆盖Element Plus的默认蓝色焦点和激活状态 */
:deep(.el-dropdown-menu__item.is-disabled:hover),
:deep(.el-dropdown-menu__item.is-disabled:focus) {
  background-color: transparent !important;
  color: var(--el-text-color-disabled) !important;
}

:deep(.el-popper) {
  --el-dropdown-menuItem-hover-fill: rgba(99, 102, 241, 0.05) !important;
  --el-dropdown-menuItem-hover-color: #6366f1 !important;
}

:deep(.el-dropdown) {
  outline: none !important;
}

:deep(.el-dropdown__popper) {
  outline: none !important;
}

:deep(.el-dropdown-link),
:deep(.el-dropdown-link:focus),
:deep(.el-dropdown-link:active) {
  outline: none !important;
  box-shadow: none !important;
  border: none !important;
}
</style>