<template>
  <aside class="w-56 h-full bg-white border-r border-gray-100 shadow-sm flex flex-col">
    <!-- Logo区域 -->
    <div class="h-16 flex items-center px-6 border-b border-gray-100">
      <div class="flex items-center space-x-2">
        <div class="w-9 h-9 flex items-center justify-center">
          <img src="@/assets/logo.svg" alt="TunshuEdu Logo" class="w-8 h-8 object-contain" />
        </div>
        <span class="text-base font-semibold text-gray-1000">TunshuEdu</span>
      </div>
    </div>

    <!-- 导航菜单 -->
    <nav class="p-4 flex-1 overflow-y-auto scrollbar-thin">
      <!-- 通用功能 -->
      <div class="mb-6">
        <p class="px-4 text-xs font-medium text-gray-400 mb-3 uppercase tracking-wider">通用</p>
        <div class="space-y-0.5">
          <router-link to="/dashboard" class="sidebar-link" :class="{ 'sidebar-link-active': $route.path === '/dashboard' }">
            <span class="material-icons-outlined text-[20px]">dashboard</span>
            <span>总览</span>
          </router-link>

          <router-link to="/clients" class="sidebar-link" :class="{ 'sidebar-link-active': $route.path.startsWith('/clients') }">
            <span class="material-icons-outlined text-[20px]">people</span>
            <span>客户档案</span>
          </router-link>

          <router-link to="/school-assistant" class="sidebar-link" :class="{ 'sidebar-link-active': $route.path === '/school-assistant' }">
            <span class="material-icons-outlined text-[20px]">school</span>
            <span>智能选校</span>
          </router-link>

          <router-link to="/programs" class="sidebar-link" :class="{ 'sidebar-link-active': $route.path === '/programs' }">
            <span class="material-icons-outlined text-[20px]">library_books</span>
            <span>项目数据库</span>
          </router-link>
        </div>
      </div>

      <!-- 文书写作 -->
      <div class="mb-6">
        <p class="px-4 text-xs font-medium text-gray-400 mb-3 uppercase tracking-wider">文书写作</p>
        <div class="space-y-0.5">
          <router-link to="/write/recommendation" class="sidebar-link" :class="{ 'sidebar-link-active': $route.path === '/write/recommendation' }">
            <span class="material-icons-outlined text-[20px]">edit_note</span>
            <span>写推荐信</span>
          </router-link>

          <router-link to="/write/cv" class="sidebar-link" :class="{ 'sidebar-link-active': $route.path === '/write/cv' }">
            <span class="material-icons-outlined text-[20px]">article</span>
            <span>写简历</span>
          </router-link>

          <router-link to="/write/ps" class="sidebar-link" :class="{ 'sidebar-link-active': $route.path === '/write/ps' }">
            <span class="material-icons-outlined text-[20px]">rate_review</span>
            <span>写个人陈述</span>
          </router-link>
        </div>
      </div>

      <!-- AI工具 -->
      <div class="mb-6">
        <p class="px-4 text-xs font-medium text-gray-400 mb-3 uppercase tracking-wider">AI工具</p>
        <div class="space-y-0.5">
          <router-link to="/ai-detector" class="sidebar-link" :class="{ 'sidebar-link-active': $route.path === '/ai-detector' }">
            <span class="material-icons-outlined text-[20px]">analytics</span>
            <span>AI率检测</span>
          </router-link>

          <router-link to="/ai-reducer" class="sidebar-link" :class="{ 'sidebar-link-active': $route.path === '/ai-reducer' }">
            <span class="material-icons-outlined text-[20px]">tune</span>
            <span>AI率降低</span>
          </router-link>
        </div>
      </div>

      <!-- 账户管理 -->
      <div class="mb-6">
        <p class="px-4 text-xs font-medium text-gray-400 mb-3 uppercase tracking-wider">系统</p>
        <div class="space-y-0.5">
          <router-link to="/account/settings" class="sidebar-link" :class="{ 'sidebar-link-active': $route.path === '/account/settings' }">
            <span class="material-icons-outlined text-[20px]">settings</span>
            <span>账户设置</span>
          </router-link>
        </div>
      </div>
    </nav>

    <!-- 底部信息 -->
    <div class="p-4 border-t border-gray-100 text-xs text-gray-400 text-center">
      <p>TunshuEdu v1.0 Beta</p>
      <p class="mt-1">© 2025 囤鼠科技</p>
    </div>
  </aside>
</template>

<script setup>
// 侧边栏逻辑
</script>

<style lang="postcss" scoped>
.sidebar-link {
  @apply flex items-center space-x-3 px-4 py-2.5 text-sm rounded-lg text-gray-600 hover:bg-gray-50 transition-colors duration-150;
}

.sidebar-link-active {
  @apply bg-primary bg-opacity-10 text-primary font-medium;
}

.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  font-feature-settings: 'liga';
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

/* 滚动条样式 */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 4px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.3);
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.5);
}
</style>