import request from '@/utils/request'

/**
 * 获取专业项目列表
 * @param {Object} params - 查询参数
 * @param {string} params.school_name - 学校名称
 * @param {string} params.region - 地区
 * @param {string} params.program_direction - 专业方向
 * @param {string} params.degree - 学位类型
 * @param {string} params.program_category - 专业大类
 * @param {number} params.limit - 返回数量限制
 * @returns {Promise} 专业项目列表
 */
export function getProgramList(params = {}) {
  return request({
    url: '/api/ai-selection/data/programs',
    method: 'get',
    params
  })
}

/**
 * 获取专业项目详情
 * @param {number} programId - 专业项目ID
 * @returns {Promise} 专业项目详情
 */
export function getProgramDetail(programId) {
  return request({
    url: `/api/ai-selection/data/programs/${programId}`,
    method: 'get'
  })
}

/**
 * 获取所有地区列表
 * @returns {Promise} 地区列表
 */
export function getRegionList() {
  return request({
    url: '/api/ai-selection/data/regions',
    method: 'get'
  })
}

/**
 * 获取学校列表
 * @param {Object} params - 查询参数
 * @param {string} params.region - 按地区筛选
 * @param {string} params.qs_rank_range - 按QS排名范围筛选
 * @returns {Promise} 学校列表
 */
export function getSchoolList(params = {}) {
  return request({
    url: '/api/ai-selection/data/schools',
    method: 'get',
    params
  })
}

/**
 * 获取系统统计信息
 * @returns {Promise} 统计信息
 */
export function getProgramStatistics() {
  return request({
    url: '/api/ai-selection/data/statistics',
    method: 'get'
  })
}

/**
 * 获取专业大类列表
 * @returns {Promise} 专业大类列表
 */
export function getProgramCategories() {
  return request({
    url: '/api/ai-selection/data/categories',
    method: 'get'
  })
}

/**
 * 获取专业方向列表
 * @returns {Promise} 专业方向列表
 */
export function getProgramDirections() {
  return request({
    url: '/api/ai-selection/data/directions',
    method: 'get'
  })
}

/**
 * 获取符合筛选条件的专业项目总数
 * @param {Object} params - 查询参数
 * @param {string} params.school_name - 学校名称
 * @param {string} params.region - 地区
 * @param {string} params.program_direction - 专业方向
 * @param {string} params.degree - 学位类型
 * @param {string} params.program_category - 专业大类
 * @returns {Promise} 总数信息
 */
export function getProgramCount(params = {}) {
  return request({
    url: '/api/ai-selection/data/programs/count',
    method: 'get',
    params
  })
} 