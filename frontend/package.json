{"name": "tunshuedu-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@heroicons/vue": "^2.0.18", "@material-icons/font": "^1.0.36", "@types/sortablejs": "^1.15.8", "@vueuse/core": "^10.7.0", "axios": "^1.6.2", "element-plus": "^2.4.3", "pinia": "^2.1.7", "sass": "^1.69.5", "sortablejs": "^1.15.6", "vue": "^3.3.8", "vue-router": "^4.2.5"}, "devDependencies": {"@stagewise/toolbar-vue": "^0.1.2", "@vitejs/plugin-vue": "^4.5.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "unplugin-auto-import": "^0.16.7", "unplugin-vue-components": "^0.25.2", "vite": "^5.0.0"}}