<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Font Test - TunshuEdu</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            margin: 40px;
            line-height: 1.6;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        .font-weight-400 { font-weight: 400; }
        .font-weight-500 { font-weight: 500; }
        .font-weight-600 { font-weight: 600; }
        .font-weight-700 { font-weight: 700; }
        .english-text {
            font-size: 16px;
            margin: 10px 0;
        }
        .chinese-text {
            font-size: 16px;
            margin: 10px 0;
        }
        .mixed-text {
            font-size: 16px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Font Display Test - Inter Font Family</h1>
    
    <div class="test-section">
        <h2>English Text Tests</h2>
        <div class="english-text font-weight-400">
            Regular (400): The quick brown fox jumps over the lazy dog. ABCDEFGHIJKLMNOPQRSTUVWXYZ abcdefghijklmnopqrstuvwxyz 0123456789
        </div>
        <div class="english-text font-weight-500">
            Medium (500): The quick brown fox jumps over the lazy dog. ABCDEFGHIJKLMNOPQRSTUVWXYZ abcdefghijklmnopqrstuvwxyz 0123456789
        </div>
        <div class="english-text font-weight-600">
            SemiBold (600): The quick brown fox jumps over the lazy dog. ABCDEFGHIJKLMNOPQRSTUVWXYZ abcdefghijklmnopqrstuvwxyz 0123456789
        </div>
        <div class="english-text font-weight-700">
            Bold (700): The quick brown fox jumps over the lazy dog. ABCDEFGHIJKLMNOPQRSTUVWXYZ abcdefghijklmnopqrstuvwxyz 0123456789
        </div>
    </div>

    <div class="test-section">
        <h2>Chinese Text Tests</h2>
        <div class="chinese-text font-weight-400">
            常规 (400): 这是一段中文测试文本，用于检查中文字体显示效果。
        </div>
        <div class="chinese-text font-weight-500">
            中等 (500): 这是一段中文测试文本，用于检查中文字体显示效果。
        </div>
        <div class="chinese-text font-weight-600">
            半粗 (600): 这是一段中文测试文本，用于检查中文字体显示效果。
        </div>
        <div class="chinese-text font-weight-700">
            粗体 (700): 这是一段中文测试文本，用于检查中文字体显示效果。
        </div>
    </div>

    <div class="test-section">
        <h2>Mixed Text Tests</h2>
        <div class="mixed-text font-weight-400">
            Regular: TunshuEdu 留学行业的AI工具箱 - Master of Science in Computer Science
        </div>
        <div class="mixed-text font-weight-500">
            Medium: TunshuEdu 留学行业的AI工具箱 - Master of Science in Computer Science
        </div>
        <div class="mixed-text font-weight-600">
            SemiBold: TunshuEdu 留学行业的AI工具箱 - Master of Science in Computer Science
        </div>
        <div class="mixed-text font-weight-700">
            Bold: TunshuEdu 留学行业的AI工具箱 - Master of Science in Computer Science
        </div>
    </div>

    <div class="test-section">
        <h2>Special Characters & Symbols</h2>
        <div class="english-text font-weight-400">
            Punctuation: .,;:!?()[]{}'""-–—
        </div>
        <div class="english-text font-weight-400">
            Numbers: 0123456789
        </div>
        <div class="english-text font-weight-400">
            Symbols: @#$%^&*+=<>|\/~`
        </div>
    </div>

    <script>
        // 检查字体是否加载成功
        document.fonts.ready.then(function() {
            console.log('All fonts loaded successfully');
            
            // 检查 Inter 字体是否可用
            if (document.fonts.check('16px Inter')) {
                console.log('Inter font is available');
                document.body.style.backgroundColor = '#f0f9ff';
            } else {
                console.log('Inter font is NOT available');
                document.body.style.backgroundColor = '#fef2f2';
                
                // 显示警告
                const warning = document.createElement('div');
                warning.style.cssText = 'background: #fee2e2; border: 1px solid #fecaca; color: #dc2626; padding: 16px; margin: 20px 0; border-radius: 8px; font-weight: 600;';
                warning.textContent = 'Warning: Inter font is not loaded properly. Fallback fonts are being used.';
                document.body.insertBefore(warning, document.body.firstChild);
            }
        });
    </script>
</body>
</html>
