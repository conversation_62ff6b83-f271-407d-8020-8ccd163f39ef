from pydantic import BaseModel, EmailStr, Field, validator
from typing import Optional
from datetime import datetime

class UserBase(BaseModel):
    """
    用户基础信息模型
    """
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    email: EmailStr = Field(..., description="电子邮箱")
    
class UserCreate(UserBase):
    """
    创建用户请求模型
    """
    password: str = Field(..., min_length=6, description="密码，至少6个字符")
    
    @validator('username')
    def username_alphanumeric(cls, v):
        """
        验证用户名是否只包含字母和数字
        """
        if not v.isalnum():
            raise ValueError('用户名只能包含字母和数字')
        return v

class UserLogin(BaseModel):
    """
    用户登录请求模型
    """
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")

class UserUpdate(BaseModel):
    """
    更新用户信息请求模型
    """
    nickname: Optional[str] = Field(None, min_length=2, max_length=50, description="用户昵称")

class ChangePassword(BaseModel):
    """
    修改密码请求模型
    """
    old_password: str = Field(..., description="旧密码")
    new_password: str = Field(..., min_length=6, description="新密码，至少6个字符")

class UserResponse(BaseModel):
    """
    用户信息响应模型
    """
    id: int
    username: str
    email: EmailStr
    nickname: Optional[str] = None
    role: str
    is_active: bool
    last_login: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        """
        ORM 模式配置，允许直接从 ORM 模型创建
        """
        orm_mode = True

class Token(BaseModel):
    """
    令牌响应模型
    """
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    user: Optional[UserResponse] = None
    
class TokenPayload(BaseModel):
    """
    令牌载荷模型
    """
    sub: Optional[int] = None  # 用户 ID
    exp: Optional[int] = None  # 过期时间 