"""AI选校系统配置"""
from app.core.config import settings

# 系统配置
CANDIDATE_POOL_SIZE = 50  # 初始候选池大小
FINAL_RECOMMENDATIONS = 10  # 最终推荐数量
GPA_TOLERANCE = 0.05  # GPA匹配容差范围 (±5%)
MIN_CASE_THRESHOLD = 3  # 最小案例数阈值，低于此值时启用RAG补充

# 评分权重
WEIGHTS = {
    "school_tier_match": 0.4,  # 院校层级匹配权重
    "program_direction_match": 0.3,  # 专业方向匹配权重
    "experience_match": 0.2,  # 经历匹配权重
    "academic_performance_match": 0.1  # 学术表现匹配权重
}

# LLM API配置 - 从主配置获取
SILICONE_FLOW_API_KEY = getattr(settings, 'SILICONE_FLOW_API_KEY', "sk-taokvnpffjwdidxvqjvtieltmwuaqwfhmihdjerqqxlzofru") 