from pydantic import BaseModel, Field
from typing import Optional, Dict, Any

class CaseBase(BaseModel):
    """案例基础信息模型"""
    id: int
    student_name: str
    undergraduate_school: str
    undergraduate_school_tier: str
    undergraduate_major: str
    gpa: float
    offer_region: str
    offer_degree: str
    offer_major_direction: str

class CaseDetail(CaseBase):
    """案例详细信息模型"""
    language_score: Dict[str, float]
    key_experiences: str
    offer_program_id: int
    offer_program_name: Optional[str] = None
    offer_school_name: Optional[str] = None
    
    # 可选的相似度信息（用于检索结果）
    similarity_score: Optional[float] = None

class CaseCreateRequest(BaseModel):
    """创建案例请求模型"""
    student_name: str = Field(..., description="学生姓名")
    undergraduate_school: str = Field(..., description="本科学校")
    undergraduate_school_tier: str = Field(..., description="本科学校层级")
    undergraduate_major: str = Field(..., description="本科专业")
    gpa: float = Field(..., description="绩点")
    offer_region: str = Field(..., description="获得offer的地区")
    offer_degree: str = Field(..., description="获得offer的学位类型")
    offer_major_direction: str = Field(..., description="获得offer的专业方向")
    language_score: Dict[str, float] = Field(..., description="语言成绩")
    key_experiences: str = Field(..., description="关键经历描述")
    offer_program_id: int = Field(..., description="获得offer的专业项目ID") 