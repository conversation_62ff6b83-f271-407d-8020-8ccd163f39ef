from pydantic import BaseModel, Field
from typing import Optional, Dict, Any

class CaseBase(BaseModel):
    """案例基础信息模型"""
    id: int
    student_name: str
    undergraduate_school: str
    undergraduate_school_tier: str
    undergraduate_major: str
    gpa: float
    target_region: str
    target_degree: str
    target_major_direction: str

class CaseDetail(CaseBase):
    """案例详细信息模型"""
    language_score: Dict[str, float]
    key_experiences: str
    target_school_id: int
    target_school_name: str
    target_program_id: int
    target_program_name: str
    
    # 可选的相似度信息（用于检索结果）
    similarity_score: Optional[float] = None 