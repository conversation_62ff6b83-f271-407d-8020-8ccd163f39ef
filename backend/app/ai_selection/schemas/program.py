from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any

class ProgramBase(BaseModel):
    """专业基础信息模型"""
    id: int
    name: str
    name_en: str
    discipline: str
    faculty: str
    description: str
    degree: str
    duration: str
    tuition: str
    gpa_requirement: float
    
    # 关联信息
    school_id: int
    school_name: str
    school_tier: str
    region_id: int
    region_name: str

class ProgramDetail(ProgramBase):
    """专业详细信息模型，包含课程和申请要求"""
    courses: List[str]
    application_requirements: str
    language_requirements: Dict[str, float]

class CandidateProgram(ProgramBase):
    """候选专业模型，包含匹配评分"""
    school_tier_match: float = Field(0.0, description="院校层级匹配分")
    program_direction_match: float = Field(0.0, description="专业方向契合分")
    experience_match: float = Field(0.0, description="经历匹配分")
    academic_performance_match: float = Field(0.0, description="学术细节匹配分")
    total_match: float = Field(0.0, description="总匹配分")
    recommendation_reason: Optional[str] = Field(None, description="推荐理由") 