from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any

class ProgramBase(BaseModel):
    """专业项目基础信息模型（包含学校信息）"""
    id: int
    
    # 学校信息
    school_name_cn: str
    school_name_en: Optional[str] = None
    school_qs_name: Optional[str] = None
    school_qs_rank: Optional[str] = None
    school_region: Optional[str] = None
    
    # 专业信息
    program_code: Optional[int] = None
    degree: Optional[str] = None
    program_name_cn: str
    program_name_en: Optional[str] = None
    program_category: Optional[str] = None
    program_direction: Optional[str] = None
    faculty: Optional[str] = None
    
    # 申请信息
    enrollment_time: Optional[str] = None
    program_duration: Optional[str] = None
    program_tuition: Optional[str] = None
    gpa_requirements: Optional[float] = None

class ProgramDetail(ProgramBase):
    """专业详细信息模型，包含课程和申请要求"""
    application_time: Optional[str] = None
    application_requirements: Optional[str] = None
    language_requirements: Optional[str] = None
    program_objectives: Optional[str] = None
    courses: Optional[str] = None
    program_website: Optional[str] = None
    other_cost: Optional[str] = None
    degree_evaluation: Optional[str] = None

class CandidateProgram(ProgramBase):
    """候选专业模型，包含匹配评分"""
    school_tier_match: float = Field(0.0, description="院校层级匹配分")
    program_direction_match: float = Field(0.0, description="专业方向契合分")
    experience_match: float = Field(0.0, description="经历匹配分")
    academic_performance_match: float = Field(0.0, description="学术细节匹配分")
    total_match: float = Field(0.0, description="总匹配分")
    recommendation_reason: Optional[str] = Field(None, description="推荐理由")

class ProgramCreateRequest(BaseModel):
    """创建专业项目请求模型"""
    # 学校信息（必填）
    school_name_cn: str = Field(..., description="学校中文名")
    school_name_en: Optional[str] = Field(None, description="学校英文名")
    school_qs_name: Optional[str] = Field(None, description="学校QS英文名")
    school_qs_rank: Optional[str] = Field(None, description="学校QS排名")
    school_region: Optional[str] = Field(None, description="学校所在地区")
    
    # 专业信息（必填）
    program_name_cn: str = Field(..., description="专业中文名")
    program_name_en: Optional[str] = Field(None, description="专业英文名")
    program_category: Optional[str] = Field(None, description="专业大类")
    program_direction: Optional[str] = Field(None, description="专业方向")
    faculty: Optional[str] = Field(None, description="所在学院")
    degree: Optional[str] = Field(None, description="申请学位类型")
    
    # 可选信息
    program_code: Optional[int] = Field(None, description="专业代码")
    enrollment_time: Optional[str] = Field(None, description="入学时间")
    program_duration: Optional[str] = Field(None, description="项目时长")
    program_tuition: Optional[str] = Field(None, description="项目学费")
    application_time: Optional[str] = Field(None, description="申请时间")
    application_requirements: Optional[str] = Field(None, description="申请要求")
    gpa_requirements: Optional[float] = Field(None, description="绩点要求")
    language_requirements: Optional[str] = Field(None, description="语言要求")
    program_objectives: Optional[str] = Field(None, description="培养目标")
    courses: Optional[str] = Field(None, description="课程设置")
    program_website: Optional[str] = Field(None, description="项目官网")
    other_cost: Optional[str] = Field(None, description="年开销预估值")
    degree_evaluation: Optional[str] = Field(None, description="留服认证") 