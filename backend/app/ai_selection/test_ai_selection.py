"""
测试AI选校系统API的脚本
"""
import requests
import json

# API基础URL
BASE_URL = "http://localhost:8000/api/ai-selection"

def test_regions():
    """测试获取地区列表"""
    print("\n=== 测试获取地区列表 ===")
    response = requests.get(f"{BASE_URL}/data/regions")
    if response.status_code == 200:
        regions = response.json()
        print(f"成功获取 {len(regions)} 个地区:")
        for region in regions[:3]:  # 只显示前3个
            print(f"  - {region['name']} (ID: {region['id']})")
    else:
        print(f"请求失败: {response.status_code}")

def test_schools():
    """测试获取学校列表"""
    print("\n=== 测试获取学校列表 ===")
    response = requests.get(f"{BASE_URL}/data/schools")
    if response.status_code == 200:
        schools = response.json()
        print(f"成功获取 {len(schools)} 个学校:")
        for school in schools[:3]:  # 只显示前3个
            print(f"  - {school['name']} ({school['tier']}, {school['region_name']})")
    else:
        print(f"请求失败: {response.status_code}")

def test_recommendation():
    """测试推荐接口"""
    print("\n=== 测试推荐接口 ===")
    
    # 测试数据
    user_input = {
        "undergraduate_school": "南京大学",
        "undergraduate_school_tier": "tier1",
        "undergraduate_major": "电子工程",
        "gpa": 85,
        "target_regions": [
            "香港"
        ],
        "target_degree": "硕士",
        "target_major_direction": "电子",
        "language_scores": {
            "additionalProp1": 0,
            "additionalProp2": 0,
            "additionalProp3": 0
        },
        "key_experiences": "材料学SCI二区文章二作",
        "career_goals": "先进的为电子设备、材料制造",
        "preferences": "香港地区"
        }
    
    print("发送用户输入:")
    print(json.dumps(user_input, indent=2, ensure_ascii=False))
    
    response = requests.post(
        f"{BASE_URL}/recommendation/recommend",
        json=user_input,
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 200:
        result = response.json()
        print("\n推荐结果:")
        recommendations = result.get("recommendations", [])
        print(f"获得 {len(recommendations)} 个推荐:")
        
        for rec in recommendations[:3]:  # 只显示前3个
            print(f"\n{rec['rank']}. {rec['school_name']} - {rec['program_name_cn']}")
            print(f"   地区: {rec['region_name']}")
            print(f"   总分: {rec['scores']['total_match']:.2f}")
            print(f"   推荐理由: {rec['recommendation_reason'][:100]}...")
    else:
        print(f"请求失败: {response.status_code}")
        print(f"错误信息: {response.text}")

def test_analyze_profile():
    """测试用户画像分析"""
    print("\n=== 测试用户画像分析 ===")
    
    user_input = {
        "undergraduate_school": "南京大学",
        "undergraduate_school_tier": "tier1",
        "undergraduate_major": "计算机",
        "gpa": 88.0,
        "target_regions": ["香港"],
        "target_degree": "硕士",
        "target_major_direction": "计算机",
        "key_experiences": "大厂开发实习，算法比赛"
    }
    
    response = requests.post(
        f"{BASE_URL}/recommendation/analyze_profile",
        json=user_input,
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 200:
        profile = response.json()
        print("增强后的用户画像:")
        print(f"  - 推断的兴趣领域: {profile.get('inferred_interest_domain')}")
        print(f"  - 学术潜力: {profile.get('academic_potential')}")
        print(f"  - 提取的技能: {profile.get('extracted_skills')}")
    else:
        print(f"请求失败: {response.status_code}")

if __name__ == "__main__":
    print("开始测试AI选校系统API...")
    print(f"API地址: {BASE_URL}")
    
    # 运行测试
    test_regions()
    test_schools()
    test_analyze_profile()
    test_recommendation()
    
    print("\n测试完成！") 