"""
RAG (Retrieval Augmented Generation) 功能实现
使用实际大模型API进行向量嵌入和检索
"""

import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
from typing import List, Dict, Any, Tuple
import requests
import os

from app.ai_selection.config import SILICONE_FLOW_API_KEY

class SiliconeFlowEmbedding:
    def __init__(self, api_key: str = SILICONE_FLOW_API_KEY):
        self.api_key = api_key
        self.base_url = "https://api.siliconflow.cn/v1"
        
    def get_embedding(self, input: str) -> np.ndarray:
        """获取单个文本的向量表示"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": "BAAI/bge-large-zh-v1.5",
            "input": input,
            "encoding_format": "float"
        }
        
        response = requests.post(
            f"{self.base_url}/embeddings",
            headers=headers,
            json=payload
        )
        
        if response.status_code != 200:
            raise Exception(f"API调用失败: {response.text}")
            
        embedding = response.json()["data"][0]["embedding"]
        return np.array(embedding)

# 创建嵌入模型实例
embedding_model = SiliconeFlowEmbedding()

def embed_text(text: str) -> np.ndarray:
    """
    将文本转换为向量表示（实际API实现）
    
    Args:
        text: 输入文本
        
    Returns:
        包含嵌入向量的numpy数组
    """
    try:
        return embedding_model.get_embedding(text)
    except Exception as e:
        print(f"嵌入API调用失败: {e}")
        # 如果API调用失败，回退到模拟实现
        np.random.seed(hash(text) % 2**32)
        return np.random.randn(1024)  # 假设向量维度是1024

def vector_search(
    query_vector: np.ndarray, 
    document_vectors: List[np.ndarray], 
    texts: List[str], 
    top_k: int = 3
) -> List[Tuple[str, float]]:
    """
    基于向量相似度搜索最相关的文档
    
    Args:
        query_vector: 查询向量
        document_vectors: 文档向量列表
        texts: 原始文本列表
        top_k: 返回的最相关结果数量
        
    Returns:
        包含(文本, 相似度分数)的列表，按相似度降序排列
    """
    if not document_vectors:
        return []
    
    # 计算余弦相似度
    document_vectors_matrix = np.vstack(document_vectors)
    similarities = cosine_similarity([query_vector], document_vectors_matrix)[0]
    
    # 取前top_k个结果
    top_indices = np.argsort(similarities)[::-1][:top_k]
    return [(texts[i], float(similarities[i])) for i in top_indices]

def match_program_with_experience(
    program_info: Dict[str, Any],
    user_experiences: str,
    top_similar_cases: List[Dict[str, Any]] = None
) -> Tuple[float, str]:
    """
    评估专业与用户经历的匹配度
    
    Args:
        program_info: 专业信息
        user_experiences: 用户经历描述
        top_similar_cases: 相似案例列表（可选）
        
    Returns:
        匹配分数和匹配理由
    """
    # 准备提示词，让模型评估匹配度
    prompt = f"""
    请你作为留学选校顾问，评估学生的经历与专业的匹配度。

    专业信息:
    专业名称: {program_info.get('program_name_cn', '')}
    学科类别: {program_info.get('program_direction', '')}
    专业描述: {program_info.get('program_objectives', '')}

    学生经历:
    {user_experiences}

    请严格按照以下格式回答:
    1. 给出一个0到1之间的匹配度分数，其中0表示完全不匹配，1表示完全匹配
    2. 给出匹配度评价理由，不超过150个字
    
    答案格式:
    分数: [数字]
    理由: [评价理由]
    """
    
    try:
        # 调用大模型API获取回答
        from app.ai_selection.utils.llm import process_text_with_api
        response = process_text_with_api(prompt)
        
        # 解析回答
        lines = response.strip().split('\n')
        score_line = next((line for line in lines if line.startswith("分数:")), None)
        reason_line = next((line for line in lines if line.startswith("理由:")), None)
        
        if score_line and reason_line:
            try:
                score = float(score_line.replace("分数:", "").strip())
                reason = reason_line.replace("理由:", "").strip()
                
                # 如果有相似案例，增加案例支持的理由
                if top_similar_cases:
                    matching_case_count = sum(1 for case in top_similar_cases 
                                            if case.get('target_program_id') == program_info.get('id'))
                    if matching_case_count > 0:
                        reason += f" 另外，我们发现有{matching_case_count}个与您背景相似的学生成功申请了该专业。"
                
                return score, reason
            except ValueError:
                # 如果无法解析分数，使用备选方法
                pass
                
        # 如果API调用失败或解析失败，回退到关键词匹配方法
        return _fallback_keyword_match(program_info, user_experiences, top_similar_cases)
        
    except Exception as e:
        print(f"匹配评估API调用失败: {e}")
        return _fallback_keyword_match(program_info, user_experiences, top_similar_cases)

def _fallback_keyword_match(
    program_info: Dict[str, Any],
    user_experiences: str,
    top_similar_cases: List[Dict[str, Any]] = None
) -> Tuple[float, str]:
    """当API调用失败时的备选关键词匹配方法"""
    # 从专业描述中提取关键词
    program_direction = program_info.get('program_direction', '')
    
    # 构建专业关键词列表
    if '数学' in program_direction:
        keywords = ['数学', '分析', '代数', '几何', '拓扑', '研究', '证明', '推导']
    elif '电子' in program_direction:
        keywords = ['电子', '电路', '设计', '微电子', '芯片', '器件', '集成', '材料', '制造']
    elif '商业' in program_direction:
        keywords = ['商业', '分析', '数据', '市场', '金融', '投资', '预测', '策略', '管理']
    elif '计算机' in program_direction:
        keywords = ['编程', '算法', '软件', '系统', '数据', '机器学习', '人工智能', '开发', '设计']
    else:
        keywords = []
    
    # 计算关键词匹配数量
    match_count = sum(1 for kw in keywords if kw in user_experiences)
    match_score = min(1.0, match_count / max(1, len(keywords) * 0.5))
    
    # 生成匹配理由
    if match_score > 0.8:
        reason = f"您的经历与{program_info.get('program_name_cn')}专业的核心要求高度匹配。您的背景包含该专业重视的关键技能和知识点。"
    elif match_score > 0.5:
        reason = f"您的经历与{program_info.get('program_name_cn')}专业有较好的匹配度。部分技能和经验与专业要求相关。"
    else:
        reason = f"您的经历与{program_info.get('program_name_cn')}专业的匹配度一般。可能需要补充相关经验或强化特定技能。"
    
    # 如果有相似案例，增加案例支持的理由
    if top_similar_cases:
        matching_case_count = sum(1 for case in top_similar_cases 
                                if case.get('target_program_id') == program_info.get('id'))
        if matching_case_count > 0:
            reason += f" 另外，我们发现有{matching_case_count}个与您背景相似的学生成功申请了该专业。"
    
    return match_score, reason 