"""
LLM (Large Language Model) 功能实现
使用实际大模型API进行文本生成和分析
"""

from typing import Dict, Any, List, Tuple
import requests
import asyncio
import aiohttp
from concurrent.futures import ThreadPoolExecutor
import time

from app.ai_selection.config import SILICONE_FLOW_API_KEY

# 创建并发限制信号量，避免API过载
API_SEMAPHORE = asyncio.Semaphore(10)  # 最多同时10个API请求

# 线程池执行器，用于同步API调用的异步化
executor = ThreadPoolExecutor(max_workers=10)

async def process_text_with_api_async(prompt: str) -> str:
    """
    异步调用大模型API处理文本，并返回结果
    
    Args:
        prompt: 提示词
        
    Returns:
        模型生成的回复
    """
    async with API_SEMAPHORE:  # 限制并发数量
        loop = asyncio.get_event_loop()
        # 在线程池中执行同步API调用
        try:
            result = await loop.run_in_executor(executor, process_text_with_api, prompt)
            return result
        except Exception as e:
            print(f"异步API调用失败: {e}")
            return "API调用失败，请稍后重试。"

def process_text_with_api(prompt: str) -> str:
    """
    调用大模型API处理文本，并返回结果
    
    Args:
        prompt: 提示词
        
    Returns:
        模型生成的回复
    """
    url = "https://api.siliconflow.cn/v1/chat/completions"

    payload = {
        "model": "Qwen/Qwen3-32B",
        "messages": [
            {
                "role": "user",
                "content": prompt
            }
        ],
        "stream": False,
        "max_tokens": 512,
        "stop": None,
        "temperature": 0.7,
        "top_p": 0.7,
        "top_k": 50,
        "frequency_penalty": 0.5,
        "n": 1,
        "response_format": {"type": "text"}
    }
    headers = {
        "Authorization": f"Bearer {SILICONE_FLOW_API_KEY}",
        "Content-Type": "application/json"
    }

    try:
        # 添加超时设置：连接超时5秒，读取超时15秒
        response = requests.request("POST", url, json=payload, headers=headers, timeout=(5, 15))
        return response.json().get("choices")[0].get("message").get("content")
    except Exception as e:
        print(f"API调用失败: {e}")
        return "API调用失败，请稍后重试。"

def extract_key_entities_from_experience(experience: str) -> List[str]:
    """
    从用户经历中提取关键实体
    
    Args:
        experience: 用户经历描述
        
    Returns:
        提取的关键实体列表
    """
    if not experience:
        return []
    
    prompt = f"""
    请分析以下学生的经历描述，提取其中包含的关键技能、经验类型和领域知识。
    返回一个标签列表，每个标签应该是简短的名词短语，表示一种技能、经验类型或领域知识。
    
    经历描述:
    {experience}
    
    请直接列出标签，每行一个，不要有编号或其他格式。例如:
    研究经验
    Python编程
    机器学习
    """
    
    try:
        response = process_text_with_api(prompt)
        # 解析响应，获取实体列表
        entities = [line.strip() for line in response.strip().split('\n') if line.strip()]
        return entities
    except Exception as e:
        print(f"提取实体API调用失败: {e}")
        # 回退到关键词匹配方法
        return _fallback_entity_extraction(experience)

def _fallback_entity_extraction(experience: str) -> List[str]:
    """API调用失败时的备选实体提取方法"""
    entities = []
    
    # 简单的关键词匹配
    if "研究" in experience:
        entities.append("研究经验")
    if "实习" in experience:
        entities.append("实习经验")
    
    domain_keywords = {
        "电子": ["电路", "芯片", "微电子", "集成", "半导体", "FPGA", "电路设计", "PCB"],
        "计算机": ["编程", "算法", "软件", "开发", "Python", "Java", "C++", "系统", "网络", "数据库"],
        "数学": ["数学", "建模", "统计", "分析", "优化", "概率", "拓扑"],
        "商业": ["金融", "投资", "市场", "策略", "分析", "管理", "营销"]
    }
    
    for domain, keywords in domain_keywords.items():
        for kw in keywords:
            if kw in experience:
                entities.append(f"{domain}领域技能")
                break
    
    # 通用技能
    if any(kw in experience for kw in ["项目", "设计", "系统"]):
        entities.append("项目经验")
    if any(kw in experience for kw in ["竞赛", "比赛", "获奖", "奖项"]):
        entities.append("竞赛获奖")
    
    return entities

def enhance_user_profile(basic_profile: Dict[str, Any], experiences: str) -> Dict[str, Any]:
    """
    增强用户画像，添加从非结构化描述中提取的信息
    
    Args:
        basic_profile: 基本用户信息
        experiences: 用户经历描述
        
    Returns:
        增强后的用户画像
    """
    enhanced_profile = basic_profile.copy()
    
    # 提取关键实体和技能
    key_entities = extract_key_entities_from_experience(experiences)
    enhanced_profile["extracted_skills"] = key_entities
    
    # 使用大模型推断兴趣领域和学术潜力
    if experiences:
        prompt = f"""
        请分析以下学生的背景信息和经历，推断:
        1. 该学生最可能感兴趣的学术领域
        2. 该学生的学术潜力评级（高/中等偏上/中等）
        
        学生背景:
        本科学校: {basic_profile.get("undergraduate_school", "")}
        本科专业: {basic_profile.get("undergraduate_major", "")}
        GPA: {basic_profile.get("gpa", "")}
        
        学生经历:
        {experiences}
        
        请按以下格式回答:
        兴趣领域: [领域名称]
        学术潜力: [评级]
        """
        
        try:
            response = process_text_with_api(prompt)
            
            # 解析响应
            lines = response.strip().split('\n')
            interest_line = next((line for line in lines if line.startswith("兴趣领域:")), None)
            potential_line = next((line for line in lines if line.startswith("学术潜力:")), None)
            
            if interest_line:
                enhanced_profile["inferred_interest_domain"] = interest_line.replace("兴趣领域:", "").strip()
            if potential_line:
                enhanced_profile["academic_potential"] = potential_line.replace("学术潜力:", "").strip()
            
            # 如果解析失败，使用备选方法
            if not enhanced_profile.get("inferred_interest_domain") or not enhanced_profile.get("academic_potential"):
                fallback_results = _fallback_profile_enhancement(basic_profile, experiences, key_entities)
                if not enhanced_profile.get("inferred_interest_domain"):
                    enhanced_profile["inferred_interest_domain"] = fallback_results.get("inferred_interest_domain")
                if not enhanced_profile.get("academic_potential"):
                    enhanced_profile["academic_potential"] = fallback_results.get("academic_potential")
        
        except Exception as e:
            print(f"增强用户画像API调用失败: {e}")
            # 使用备选方法
            fallback_results = _fallback_profile_enhancement(basic_profile, experiences, key_entities)
            enhanced_profile["inferred_interest_domain"] = fallback_results.get("inferred_interest_domain")
            enhanced_profile["academic_potential"] = fallback_results.get("academic_potential")
    
    else:
        # 如果没有经历信息，使用备选方法
        fallback_results = _fallback_profile_enhancement(basic_profile, "", key_entities)
        enhanced_profile["inferred_interest_domain"] = fallback_results.get("inferred_interest_domain")
        enhanced_profile["academic_potential"] = fallback_results.get("academic_potential")
    
    return enhanced_profile

def _fallback_profile_enhancement(
    basic_profile: Dict[str, Any], 
    experiences: str, 
    key_entities: List[str]
) -> Dict[str, Any]:
    """API调用失败时的备选用户画像增强方法"""
    result = {}
    
    # 推断主要兴趣领域
    major = basic_profile.get("undergraduate_major", "").lower()
    interest_domain = None
    
    if any(kw in major for kw in ["计算机", "软件", "信息"]):
        interest_domain = "计算机科学与技术"
    elif any(kw in major for kw in ["电子", "电气", "通信"]):
        interest_domain = "电子与电气工程"
    elif any(kw in major for kw in ["数学", "统计"]):
        interest_domain = "数学与统计"
    elif any(kw in major for kw in ["经济", "金融", "商业", "管理"]):
        interest_domain = "商业与经济"
    else:
        # 从经历中推断
        if "计算机领域技能" in key_entities:
            interest_domain = "计算机科学与技术"
        elif "电子领域技能" in key_entities:
            interest_domain = "电子与电气工程"
        elif "数学领域技能" in key_entities:
            interest_domain = "数学与统计"
        elif "商业领域技能" in key_entities:
            interest_domain = "商业与经济"
    
    if interest_domain:
        result["inferred_interest_domain"] = interest_domain
    
    # 评估学术潜力
    gpa = float(basic_profile.get("gpa", 0))
    has_research = "研究经验" in key_entities
    
    if gpa > 85 and has_research:
        academic_potential = "高"
    elif gpa > 80 or has_research:
        academic_potential = "中等偏上"
    else:
        academic_potential = "中等"
    
    result["academic_potential"] = academic_potential
    
    return result

def generate_recommendation_reason(
    user_profile: Dict[str, Any],
    school_info: Dict[str, Any],
    program_info: Dict[str, Any],
    scores: Dict[str, float],
    similar_cases: List[Dict[str, Any]] = None
) -> str:
    """
    生成个性化推荐理由
    
    Args:
        user_profile: 用户画像
        school_info: 学校信息
        program_info: 专业信息
        scores: 各维度评分
        similar_cases: 相似案例列表（可选）
        
    Returns:
        推荐理由文本
    """
    # 准备提示词
    prompt = f"""
    请你作为留学选校顾问，为学生生成一段有说服力的院校专业推荐理由。
    
    学生信息:
    本科学校: {user_profile.get('undergraduate_school', '')}
    本科学校层级: {user_profile.get('undergraduate_school_tier', '')}
    本科专业: {user_profile.get('undergraduate_major', '')}
    GPA: {user_profile.get('gpa', '')}
    
    推荐院校与专业:
    学校名称: {school_info.get('name', '')}
    学校层级: {school_info.get('tier', '')}
    专业名称: {program_info.get('name', '')}
    专业学科: {program_info.get('discipline', '')}
    
    匹配评分:
    院校层级匹配分: {scores.get('school_tier_match', 0)*10:.1f}/10
    专业方向契合分: {scores.get('program_direction_match', 0)*10:.1f}/10
    经历匹配分: {scores.get('experience_match', 0)*10:.1f}/10
    学术表现匹配分: {scores.get('academic_performance_match', 0)*10:.1f}/10
    总匹配分: {scores.get('total_match', 0)*10:.1f}/10
    
    相似案例数量: {len(similar_cases) if similar_cases else 0}
    
    请根据以上信息，生成一段不超过200字的推荐理由，包括:
    1. 学校匹配度评价
    2. 专业方向契合度评价
    3. 经历与专业的相关性
    4. 如果有相似案例支持，请简要提及
    
    回答格式:
    [直接输出推荐理由，不要包含任何前缀]
    """
    
    try:
        return process_text_with_api(prompt)
    except Exception as e:
        print(f"生成推荐理由API调用失败: {e}")
        # 回退到模板式推荐理由
        return _fallback_recommendation_reason(user_profile, school_info, program_info, scores, similar_cases)

def _fallback_recommendation_reason(
    user_profile: Dict[str, Any],
    school_info: Dict[str, Any],
    program_info: Dict[str, Any],
    scores: Dict[str, float],
    similar_cases: List[Dict[str, Any]] = None
) -> str:
    """API调用失败时的备选推荐理由生成方法"""
    # 组装各部分内容
    school_tier_match = scores.get("school_tier_match", 0)
    program_match = scores.get("program_direction_match", 0)
    experience_match = scores.get("experience_match", 0)
    
    # 生成学校匹配部分
    if school_tier_match > 0.8:
        school_part = f"{school_info.get('name')}的录取难度与您的背景非常匹配，是适合您申请的目标。"
    elif school_tier_match > 0.5:
        school_part = f"{school_info.get('name')}对于您来说是具有一定挑战性但可达的目标，建议作为冲刺选择。"
    else:
        school_part = f"{school_info.get('name')}对于您当前的背景可能有一定难度，如果申请需要同时考虑更多保底选择。"
    
    # 生成专业匹配部分
    if program_match > 0.8:
        program_part = f"您的背景与{program_info.get('name')}专业方向高度相关，专业匹配度很高。"
    elif program_match > 0.5:
        program_part = f"您的背景与{program_info.get('name')}专业方向有一定的关联性，可以考虑申请。"
    else:
        program_part = f"您的背景与{program_info.get('name')}专业的相关性不是特别强，可能需要在申请材料中特别说明您的兴趣和转专业的动机。"
    
    # 生成经历匹配部分
    if experience_match > 0.8:
        experience_part = f"您的实习和项目经历非常符合该专业的要求，是很有竞争力的申请者。"
    elif experience_match > 0.5:
        experience_part = f"您有一些与该专业相关的经历，这将对您的申请有所帮助。"
    else:
        experience_part = f"建议您在申请前尝试获取更多与该专业相关的实践经验，以增强申请竞争力。"
    
    # 生成案例支持部分
    case_part = ""
    if similar_cases:
        # 在新的数据结构中，案例直接通过offer_program_id关联到专业
        matching_cases = [case for case in similar_cases 
                          if case.get("offer_program_id") == program_info.get("id")]
        if matching_cases:
            case_part = f"\n\n数据显示，我们有{len(matching_cases)}个与您背景类似的学生成功获得了该校该专业的录取，这进一步支持了我们的推荐。"
    
    # 组合完整推荐理由
    recommendation_reason = f"{school_part} {program_part} {experience_part}{case_part}"
    
    return recommendation_reason 