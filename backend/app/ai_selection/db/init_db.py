"""
AI选校系统数据库初始化脚本
用于创建数据库表和初始化种子数据
"""
import asyncio
from app.db.database import engine
from app.ai_selection.db.models import Base
from app.ai_selection.db.seed import seed_ai_selection_data

async def init_ai_selection_db():
    """初始化AI选校系统数据库"""
    print("开始初始化AI选校系统数据库...")
    
    # 创建所有表
    async with engine.begin() as conn:
        # 注意：这里使用 run_sync 来执行同步的 create_all 方法
        await conn.run_sync(Base.metadata.create_all)
    
    print("数据库表创建完成！")
    
    # 初始化种子数据
    print("开始初始化种子数据...")
    await seed_ai_selection_data()
    
    print("AI选校系统数据库初始化完成！")

if __name__ == "__main__":
    asyncio.run(init_ai_selection_db()) 