import json
from sqlalchemy import select
from app.db.database import get_db
from app.ai_selection.db.models import (
    AISelectionProgram as Program, 
    AISelectionCase as Case
)
from app.ai_selection.utils.rag import embed_text
import numpy as np

# 示例数据
async def seed_ai_selection_data():
    async for session in get_db():
        try:
            # 检查数据库中是否已有数据
            result = await session.execute(select(Program))
            existing_programs = result.scalars().all()
            if existing_programs:
                print("AI选校系统数据库已有数据，跳过初始化")
                return
            
            # 添加专业数据（包含学校和地区信息）
            programs_data = [
                {
                    # 学校信息
                    "school_name_cn": "帝国理工学院",
                    "school_name_en": "Imperial College London",
                    "school_qs_name": "Imperial College London",
                    "school_qs_rank": "9",
                    "school_region": "英国",
                    
                    # 专业信息
                    "program_name_cn": "纯数学理学硕士",
                    "program_name_en": "MSc Pure Mathematics",
                    "program_category": "理学",
                    "program_direction": "数学",
                    "faculty": "数学学院",
                    "degree": "硕士",
                    "program_duration": "1年",
                    "program_tuition": "35700英镑/年",
                    "application_requirements": "具有2:1学位，需要数学或应用数学背景",
                    "gpa_requirements": 85.0,
                    "language_requirements": "雅思6.5分或托福92分",
                    "program_objectives": "帝国理工学院纯数学理学硕士项目为期一年，向学生提供纯数学领域不同方面的培训。",
                    "courses": "代数几何、偏微分方程解析方法、交换代数",
                    "enrollment_time": "9月",
                    "application_time": "次年1月截止",
                },
                {
                    # 学校信息
                    "school_name_cn": "香港理工大学",
                    "school_name_en": "The Hong Kong Polytechnic University",
                    "school_qs_name": "The Hong Kong Polytechnic University",
                    "school_qs_rank": "65",
                    "school_region": "香港",
                    
                    # 专业信息
                    "program_name_cn": "微电子技术与材料理学硕士",
                    "program_name_en": "MSc in Microelectronic Technology and Materials",
                    "program_category": "工学",
                    "program_direction": "电子工程",
                    "faculty": "工程学院",
                    "degree": "硕士",
                    "program_duration": "1年",
                    "program_tuition": "170000港币/年",
                    "application_requirements": "需要电子工程、物理学或材料科学背景",
                    "gpa_requirements": 80.0,
                    "language_requirements": "雅思6.0分或托福80分",
                    "program_objectives": "香港理工大学微电子技术与材料理学硕士项目培养学生在微电子技术与材料领域的专业知识和技能。",
                    "courses": "半导体物理、微电子器件、集成电路设计",
                    "enrollment_time": "9月",
                    "application_time": "次年4月截止",
                },
                {
                    # 学校信息
                    "school_name_cn": "南洋理工大学",
                    "school_name_en": "Nanyang Technological University",
                    "school_qs_name": "Nanyang Technological University",
                    "school_qs_rank": "26",
                    "school_region": "新加坡",
                    
                    # 专业信息
                    "program_name_cn": "人工智能理学硕士",
                    "program_name_en": "MSc in Artificial Intelligence",
                    "program_category": "工学",
                    "program_direction": "计算机科学",
                    "faculty": "计算机科学与工程学院",
                    "degree": "硕士",
                    "program_duration": "1年",
                    "program_tuition": "52000新币/年",
                    "application_requirements": "需要计算机科学或相关专业背景，有编程经验",
                    "gpa_requirements": 80.0,
                    "language_requirements": "雅思6.5分或托福90分",
                    "program_objectives": "南洋理工大学人工智能理学硕士项目为学生提供在人工智能领域的深入培训。",
                    "courses": "机器学习、深度学习、自然语言处理",
                    "enrollment_time": "8月",
                    "application_time": "次年2月截止",
                },
                {
                    # 学校信息
                    "school_name_cn": "香港中文大学",
                    "school_name_en": "The Chinese University of Hong Kong",
                    "school_qs_name": "The Chinese University of Hong Kong",
                    "school_qs_rank": "47",
                    "school_region": "香港",
                    
                    # 专业信息
                    "program_name_cn": "金融学理学硕士",
                    "program_name_en": "MSc in Finance",
                    "program_category": "商学",
                    "program_direction": "金融",
                    "faculty": "商学院",
                    "degree": "硕士",
                    "program_duration": "1年",
                    "program_tuition": "400000港币/年",
                    "application_requirements": "需要商科、经济学或相关专业背景",
                    "gpa_requirements": 85.0,
                    "language_requirements": "雅思6.5分或托福79分",
                    "program_objectives": "香港中文大学金融学理学硕士项目培养学生在金融领域的专业知识和技能。",
                    "courses": "投资学、公司金融、金融衍生品、风险管理",
                    "enrollment_time": "9月",
                    "application_time": "次年3月截止",
                },
                {
                    # 学校信息
                    "school_name_cn": "麻省理工学院",
                    "school_name_en": "Massachusetts Institute of Technology",
                    "school_qs_name": "Massachusetts Institute of Technology",
                    "school_qs_rank": "1",
                    "school_region": "美国",
                    
                    # 专业信息
                    "program_name_cn": "计算机科学理学硕士",
                    "program_name_en": "MSc in Computer Science",
                    "program_category": "工学",
                    "program_direction": "计算机科学",
                    "faculty": "电气工程与计算机科学学院",
                    "degree": "硕士",
                    "program_duration": "2年",
                    "program_tuition": "59750美元/年",
                    "application_requirements": "需要计算机科学或相关专业背景，优秀的数学基础",
                    "gpa_requirements": 90.0,
                    "language_requirements": "雅思7.0分或托福100分",
                    "program_objectives": "麻省理工学院计算机科学理学硕士项目为学生提供计算机科学领域的世界顶尖教育。",
                    "courses": "算法设计与分析、计算机系统、机器学习、软件工程",
                    "enrollment_time": "9月",
                    "application_time": "次年12月截止",
                }
            ]
            
            print("为专业生成嵌入向量...")
            
            # 为每个专业生成嵌入向量
            for program_data in programs_data:
                # 生成表示专业的文本
                program_text = f"""
                学校: {program_data.get('school_name_cn', '')}
                地区: {program_data.get('school_region', '')}
                专业名称: {program_data.get('program_name_cn', '')}
                英文名称: {program_data.get('program_name_en', '')}
                学科分类: {program_data.get('program_direction', '')}
                专业描述: {program_data.get('program_objectives', '')}
                """
                
                try:
                    # 生成嵌入向量
                    embedding = embed_text(program_text)
                    # 将向量转为JSON格式存储
                    program_data["embedding"] = json.dumps(embedding.tolist())
                except Exception as e:
                    print(f"为专业生成嵌入向量时出错: {e}")
                    program_data["embedding"] = None  # 如果生成失败，存为NULL
            
            program_objects = []
            for program_data in programs_data:
                program = Program(**program_data)
                session.add(program)
                program_objects.append(program)
            
            await session.commit()
            
            # 刷新以获取ID
            for program in program_objects:
                await session.refresh(program)
            
            # 添加案例数据（更新字段名称）
            cases_data = [
                {
                    "student_name": "L同学",
                    "undergraduate_school": "某普通一本大学",
                    "undergraduate_school_tier": "tier3",
                    "undergraduate_major": "微电子科学与工程",
                    "gpa": 82.6,
                    "offer_region": "香港",
                    "offer_degree": "硕士",
                    "offer_major_direction": "微电子技术与材料",
                    "language_score": json.dumps({"雅思": 6.0}),
                    "key_experiences": "富士康科技集团研发部门实习生、二级运算放大器设计",
                    "offer_program_id": program_objects[1].id,  # 香港理工的微电子技术与材料
                },
                {
                    "student_name": "Z同学",
                    "undergraduate_school": "985高校",
                    "undergraduate_school_tier": "tier1",
                    "undergraduate_major": "计算机科学与技术",
                    "gpa": 85.0,
                    "offer_region": "新加坡",
                    "offer_degree": "硕士",
                    "offer_major_direction": "人工智能",
                    "language_score": json.dumps({"雅思": 6.5}),
                    "key_experiences": "食堂座位管理系统、京东网安实习、KTH交换",
                    "offer_program_id": program_objects[2].id,  # 南洋理工的人工智能
                },
                {
                    "student_name": "W同学",
                    "undergraduate_school": "211高校",
                    "undergraduate_school_tier": "tier2",
                    "undergraduate_major": "金融学",
                    "gpa": 88.0,
                    "offer_region": "香港",
                    "offer_degree": "硕士",
                    "offer_major_direction": "金融",
                    "language_score": json.dumps({"雅思": 7.0}),
                    "key_experiences": "中信证券实习、量化投资研究项目、CFA一级通过",
                    "offer_program_id": program_objects[3].id,  # 香港中文大学的金融学
                },
                {
                    "student_name": "C同学",
                    "undergraduate_school": "985高校",
                    "undergraduate_school_tier": "tier1",
                    "undergraduate_major": "数学与应用数学",
                    "gpa": 92.0,
                    "offer_region": "英国",
                    "offer_degree": "硕士", 
                    "offer_major_direction": "数学",
                    "language_score": json.dumps({"雅思": 7.5, "GRE": 330}),
                    "key_experiences": "数学建模竞赛国家一等奖、北京大学数学夏令营、研究论文发表",
                    "offer_program_id": program_objects[0].id,  # 帝国理工的纯数学
                }
            ]
            
            print("为案例生成嵌入向量...")
            
            # 为每个案例生成嵌入向量
            for case_data in cases_data:
                # 生成表示学生背景的文本
                case_background = f"""
                本科学校: {case_data.get('undergraduate_school', '')}
                本科学校层级: {case_data.get('undergraduate_school_tier', '')}
                本科专业: {case_data.get('undergraduate_major', '')}
                GPA: {case_data.get('gpa', '')}
                目标地区: {case_data.get('offer_region', '')}
                目标学位: {case_data.get('offer_degree', '')}
                目标专业方向: {case_data.get('offer_major_direction', '')}
                关键经历: {case_data.get('key_experiences', '')}
                """
                
                try:
                    # 生成嵌入向量
                    embedding = embed_text(case_background)
                    # 将向量转为JSON格式存储
                    case_data["embedding"] = json.dumps(embedding.tolist())
                except Exception as e:
                    print(f"为案例生成嵌入向量时出错: {e}")
                    case_data["embedding"] = None  # 如果生成失败，存为NULL
            
            case_objects = []
            for case_data in cases_data:
                case = Case(**case_data)
                session.add(case)
                case_objects.append(case)
            
            await session.commit()
            
            print("AI选校系统种子数据初始化完成！")
            print(f"已添加 {len(program_objects)} 个专业项目")
            print(f"已添加 {len(case_objects)} 个申请案例")
            
        except Exception as e:
            print(f"初始化种子数据时出错: {e}")
            await session.rollback()
            raise
        finally:
            await session.close()
        break  # 只需要一个session 