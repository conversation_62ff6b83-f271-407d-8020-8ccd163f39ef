import json
from sqlalchemy import select
from app.db.database import get_db
from app.ai_selection.db.models import (
    AISelectionRegion as Region, 
    AISelectionSchool as School, 
    AISelectionProgram as Program, 
    AISelectionCase as Case,
    ai_selection_program_school as program_school
)
from app.ai_selection.utils.rag import embed_text
import numpy as np

# 示例数据
async def seed_ai_selection_data():
    async for session in get_db():
        try:
            # 检查数据库中是否已有数据
            result = await session.execute(select(Region))
            existing_regions = result.scalars().all()
            if existing_regions:
                print("AI选校系统数据库已有数据，跳过初始化")
                return
            
            # 添加地区数据
            regions_data = [
                {"region_name": "英国"},
                {"region_name": "香港"},
                {"region_name": "新加坡"},
                {"region_name": "美国"},
                {"region_name": "澳大利亚"},
            ]
            
            region_objects = []
            for region_data in regions_data:
                region = Region(**region_data)
                session.add(region)
                region_objects.append(region)
            
            await session.commit()
            
            # 刷新以获取ID
            for region in region_objects:
                await session.refresh(region)
            
            # 添加学校数据
            schools_data = [
                {"school_name": "帝国理工学院", "tier": "tier1", "region_id": region_objects[0].id},
                {"school_name": "香港理工大学", "tier": "tier1", "region_id": region_objects[1].id},
                {"school_name": "香港中文大学", "tier": "tier1", "region_id": region_objects[1].id},
                {"school_name": "南洋理工大学", "tier": "tier1", "region_id": region_objects[2].id},
                {"school_name": "哈佛大学", "tier": "tier1", "region_id": region_objects[3].id},
                {"school_name": "麻省理工学院", "tier": "tier1", "region_id": region_objects[3].id},
                {"school_name": "墨尔本大学", "tier": "tier1", "region_id": region_objects[4].id},
            ]
            
            school_objects = []
            for school_data in schools_data:
                school = School(**school_data)
                session.add(school)
                school_objects.append(school)
            
            await session.commit()
            
            # 刷新以获取ID
            for school in school_objects:
                await session.refresh(school)
            
            # 添加专业数据（简化版）
            programs_data = [
                {
                    "program_name_cn": "纯数学理学硕士",
                    "program_name_en": "MSc Pure Mathematics",
                    "program_direction": "数学",
                    "faculty": "数学学院",
                    "program_objectives": "帝国理工学院纯数学理学硕士项目为期一年，向学生提供纯数学领域不同方面的培训。",
                    "courses": json.dumps(["代数几何", "偏微分方程解析方法", "交换代数"]),
                    "application_requirements": "具有2:1学位，需要数学或应用数学背景",
                    "degree": "硕士",
                    "program_duration": "1年",
                    "program_tuition": "35700英镑/年",
                    "language_requirements": json.dumps({"雅思": 6.5, "托福": 92}),
                    "gpa_requirements": 85.0
                },
                {
                    "program_name_cn": "微电子技术与材料理学硕士",
                    "program_name_en": "MSc in Microelectronic Technology and Materials",
                    "program_direction": "电子工程",
                    "faculty": "工程学院",
                    "program_objectives": "香港理工大学微电子技术与材料理学硕士项目培养学生在微电子技术与材料领域的专业知识和技能。",
                    "courses": json.dumps(["半导体物理", "微电子器件", "集成电路设计"]),
                    "application_requirements": "需要电子工程、物理学或材料科学背景",
                    "degree": "硕士",
                    "program_duration": "1年",
                    "program_tuition": "170000港币/年",
                    "language_requirements": json.dumps({"雅思": 6.0, "托福": 80}),
                    "gpa_requirements": 80.0
                },
                {
                    "program_name_cn": "人工智能理学硕士",
                    "program_name_en": "MSc in Artificial Intelligence",
                    "program_direction": "计算机科学",
                    "faculty": "计算机科学学院",
                    "program_objectives": "南洋理工大学人工智能理学硕士项目为学生提供在人工智能领域的深入培训。",
                    "courses": json.dumps(["机器学习", "深度学习", "自然语言处理"]),
                    "application_requirements": "需要计算机科学或相关专业背景，有编程经验",
                    "degree": "硕士",
                    "program_duration": "1年",
                    "program_tuition": "52000新币/年",
                    "language_requirements": json.dumps({"雅思": 6.5, "托福": 90}),
                    "gpa_requirements": 80.0
                }
            ]
            
            print("为专业生成嵌入向量...")
            
            # 为每个专业生成嵌入向量
            for program_data in programs_data:
                # 生成表示专业的文本
                program_text = f"""
                专业名称: {program_data.get('program_name_cn', '')}
                英文名称: {program_data.get('program_name_en', '')}
                学科分类: {program_data.get('program_direction', '')}
                专业描述: {program_data.get('program_objectives', '')}
                """
                
                try:
                    # 生成嵌入向量
                    embedding = embed_text(program_text)
                    # 将向量转为JSON格式存储
                    program_data["embedding"] = json.dumps(embedding.tolist())
                except Exception as e:
                    print(f"为专业生成嵌入向量时出错: {e}")
                    program_data["embedding"] = None  # 如果生成失败，存为NULL
            
            program_objects = []
            for program_data in programs_data:
                program = Program(**program_data)
                session.add(program)
                program_objects.append(program)
            
            await session.commit()
            
            # 刷新以获取ID
            for program in program_objects:
                await session.refresh(program)
            
            # 关联专业和学校
            program_school_data = [
                {"program_id": program_objects[0].id, "school_id": school_objects[0].id},  # 帝国理工的纯数学
                {"program_id": program_objects[1].id, "school_id": school_objects[1].id},  # 香港理工的微电子
                {"program_id": program_objects[2].id, "school_id": school_objects[3].id},  # 南洋理工的人工智能
            ]
            
            for ps_data in program_school_data:
                insert_stmt = program_school.insert().values(**ps_data)
                await session.execute(insert_stmt)
            
            await session.commit()
            
            # 添加案例数据
            cases_data = [
                {
                    "student_name": "L同学",
                    "undergraduate_school": "某普通一本大学",
                    "undergraduate_school_tier": "tier3",
                    "undergraduate_major": "微电子科学与工程",
                    "gpa": 82.6,
                    "target_region": "香港",
                    "target_degree": "硕士",
                    "target_major_direction": "微电子技术与材料",
                    "language_score": json.dumps({"雅思": 6.0}),
                    "key_experiences": "富士康科技集团研发部门实习生、二级运算放大器设计",
                    "target_school_id": school_objects[1].id,  # 香港理工
                    "target_program_id": program_objects[1].id,  # 微电子技术与材料
                },
                {
                    "student_name": "Z同学",
                    "undergraduate_school": "985高校",
                    "undergraduate_school_tier": "tier1",
                    "undergraduate_major": "计算机科学与技术",
                    "gpa": 85.0,
                    "target_region": "新加坡",
                    "target_degree": "硕士",
                    "target_major_direction": "人工智能",
                    "language_score": json.dumps({"雅思": 6.5}),
                    "key_experiences": "食堂座位管理系统、京东网安实习、KTH交换",
                    "target_school_id": school_objects[3].id,  # 南洋理工
                    "target_program_id": program_objects[2].id,  # 人工智能
                }
            ]
            
            print("为案例生成嵌入向量...")
            
            # 为每个案例生成嵌入向量
            for case_data in cases_data:
                # 生成表示学生背景的文本
                case_background = f"""
                本科学校: {case_data.get('undergraduate_school', '')}
                本科学校层级: {case_data.get('undergraduate_school_tier', '')}
                本科专业: {case_data.get('undergraduate_major', '')}
                GPA: {case_data.get('gpa', '')}
                """
                
                try:
                    # 生成嵌入向量
                    embedding = embed_text(case_background)
                    # 将向量转为JSON格式存储
                    case_data["embedding"] = json.dumps(embedding.tolist())
                except Exception as e:
                    print(f"为案例生成嵌入向量时出错: {e}")
                    case_data["embedding"] = None  # 如果生成失败，存为NULL
            
            # 插入案例数据
            for case_data in cases_data:
                case = Case(**case_data)
                session.add(case)
            
            await session.commit()
            
            print("AI选校系统数据库初始化完成！")
            
        except Exception as e:
            await session.rollback()
            print(f"AI选校系统数据库初始化出错: {e}")
            raise 