from sqlalchemy import Column, Integer, String, Float, Text, ForeignKey
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship

from app.db.database import Base

# 专业模型（合并了学校信息和地区信息）
class AISelectionProgram(Base):
    __tablename__ = "ai_selection_programs"
    
    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="ID，自增主键")
    
    # 学校信息
    school_name_cn = Column(String(200), nullable=False, index=True, comment="学校中文名")
    school_name_en = Column(String(200), nullable=True, index=True, comment="学校英文名")
    school_qs_name = Column(String(200), nullable=True, comment="学校QS英文名")
    school_qs_rank = Column(String(100), nullable=True, comment="学校QS排名")
    school_region = Column(String(100), nullable=True, comment="学校所在地区")

    # 专业信息
    program_code = Column(Integer, nullable=True, comment="专业代码")
    degree = Column(String(50), nullable=True, comment="申请学位类型")
    program_name_cn = Column(String(200), nullable=False, index=True, comment="专业中文名")
    program_name_en = Column(String(200), nullable=True, index=True, comment="专业英文名")
    program_category = Column(String(100), nullable=True, comment="专业大类")
    program_direction = Column(String(200), nullable=True, comment="专业方向")
    faculty = Column(String(200), nullable=True, comment="所在学院")
    
    enrollment_time = Column(String(100), nullable=True, comment="入学时间")
    program_duration = Column(String(100), nullable=True, comment="项目时长")
    program_tuition = Column(String(100), nullable=True, comment="项目学费")
    application_time = Column(Text, nullable=True, comment="申请时间")
    application_requirements = Column(Text, nullable=True, comment="申请要求")
    gpa_requirements = Column(Float, nullable=True, comment="绩点要求")
    language_requirements = Column(Text, nullable=True, comment="语言要求")

    program_objectives = Column(Text, nullable=True, comment="培养目标")
    courses = Column(Text, nullable=True, comment="课程设置")

    # 其他信息
    program_website = Column(String(500), nullable=True, comment="项目官网")
    other_cost = Column(String(100), nullable=True, comment="年开销预估值")
    degree_evaluation = Column(Text, nullable=True, comment="留服认证")
    
    # 向量嵌入（用于AI匹配）- 暂时注释掉，数据库中不存在此字段
    # embedding = Column(JSONB, nullable=True, comment="专业描述的向量嵌入")
    
    # 关联到案例
    cases = relationship("AISelectionCase", back_populates="offer_program")

# 案例模型（修改字段名称）
class AISelectionCase(Base):
    __tablename__ = "ai_selection_cases"
    
    id = Column(Integer, primary_key=True, index=True)
    student_name = Column(String(100))
    undergraduate_school = Column(String(200))
    undergraduate_school_tier = Column(String(50))
    undergraduate_major = Column(String(200))
    gpa = Column(Float)
    offer_region = Column(String(100))  # target_region -> offer_region
    offer_degree = Column(String(50))   # target_degree -> offer_degree
    offer_major_direction = Column(String(200))  # target_major_direction -> offer_major_direction
    language_score = Column(JSONB)  # 存储为JSONB，包含多种语言考试成绩
    key_experiences = Column(Text)  # 关键经历描述
    offer_program_id = Column(Integer, ForeignKey("ai_selection_programs.id"))  # target_program_id -> offer_program_id
    # 移除 target_school_id，因为学校信息已经合并到program表中
    # embedding = Column(JSONB, nullable=True)  # 案例描述的向量嵌入 - 暂时注释掉
    
    offer_program = relationship("AISelectionProgram", back_populates="cases") 