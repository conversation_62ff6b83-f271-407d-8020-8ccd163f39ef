from sqlalchemy import Column, Integer, String, Float, Text, ForeignKey, JSON, Table
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship

from app.db.database import Base

# 专业与学校的关联表
ai_selection_program_school = Table(
    'ai_selection_program_school',
    Base.metadata,
    Column('program_id', Integer, ForeignKey('ai_selection_programs.id'), primary_key=True),
    Column('school_id', Integer, ForeignKey('ai_selection_schools.id'), primary_key=True)
)

# 地区模型
class AISelectionRegion(Base):
    __tablename__ = "ai_selection_regions"
    
    id = Column(Integer, primary_key=True, index=True)
    region_name = Column(String(100), unique=True, index=True)
    
    schools = relationship("AISelectionSchool", back_populates="region")

# 学校模型
class AISelectionSchool(Base):
    __tablename__ = "ai_selection_schools"
    
    id = Column(Integer, primary_key=True, index=True)
    school_name = Column(String(200), index=True)
    tier = Column(String(50))  # 学校层级: tier1, tier2, tier3等
    region_id = Column(Integer, ForeignKey("ai_selection_regions.id"))
    
    region = relationship("AISelectionRegion", back_populates="schools")
    programs = relationship("AISelectionProgram", secondary=ai_selection_program_school, back_populates="schools")
    cases = relationship("AISelectionCase", back_populates="target_school")

# 专业模型
class AISelectionProgram(Base):
    __tablename__ = "ai_selection_programs"
    
    id = Column(Integer, primary_key=True, index=True)
    program_name_cn = Column(String(200), index=True)
    program_name_en = Column(String(200), index=True)  # 英文名
    program_direction = Column(String(100), index=True)  # 学科分类
    faculty = Column(String(200))  # 所属学院
    program_objectives = Column(Text)  # 项目描述
    courses = Column(JSONB)  # 课程列表，存为JSONB
    application_requirements = Column(Text)  # 申请要求
    degree = Column(String(50))  # 学位类型：硕士、博士等
    program_duration = Column(String(50))  # 学制
    program_tuition = Column(String(100))  # 学费
    language_requirements = Column(JSONB)  # 语言要求，存为JSONB
    gpa_requirements = Column(Float)  # GPA要求
    embedding = Column(JSONB, nullable=True)  # 专业描述的向量嵌入
    
    schools = relationship("AISelectionSchool", secondary=ai_selection_program_school, back_populates="programs")
    cases = relationship("AISelectionCase", back_populates="target_program")

# 案例模型
class AISelectionCase(Base):
    __tablename__ = "ai_selection_cases"
    
    id = Column(Integer, primary_key=True, index=True)
    student_name = Column(String(100))
    undergraduate_school = Column(String(200))
    undergraduate_school_tier = Column(String(50))
    undergraduate_major = Column(String(200))
    gpa = Column(Float)
    target_region = Column(String(100))
    target_degree = Column(String(50))
    target_major_direction = Column(String(200))
    language_score = Column(JSONB)  # 存储为JSONB，包含多种语言考试成绩
    key_experiences = Column(Text)  # 关键经历描述
    target_school_id = Column(Integer, ForeignKey("ai_selection_schools.id"))
    target_program_id = Column(Integer, ForeignKey("ai_selection_programs.id"))
    embedding = Column(JSONB, nullable=True)  # 案例描述的向量嵌入
    
    target_school = relationship("AISelectionSchool", back_populates="cases")
    target_program = relationship("AISelectionProgram", back_populates="cases") 