# AI智能选校系统集成文档

## 系统概述

AI智能选校系统是一个基于RAG（检索增强生成）和LLM的留学智能选校推荐系统，现已集成到TunshuEdu平台中。

## 目录结构

```
app/ai_selection/
├── __init__.py
├── api/
│   ├── __init__.py
│   ├── router.py
│   └── endpoints/
│       ├── __init__.py
│       ├── recommendation.py    # 推荐相关接口
│       └── data.py              # 数据查询接口
├── core/
│   ├── __init__.py
│   ├── user_profile.py          # 用户画像构建
│   ├── candidate_pool.py        # 候选池生成
│   ├── school_matching.py       # 学校匹配
│   ├── program_matching.py      # 专业匹配
│   └── ranking.py               # 排序和推荐生成
├── db/
│   ├── __init__.py
│   ├── models.py                # 数据库模型（带ai_selection_前缀）
│   ├── seed.py                  # 种子数据
│   └── init_db.py               # 数据库初始化脚本
├── schemas/
│   ├── __init__.py
│   ├── user.py                  # 用户相关模型
│   └── recommendation.py        # 推荐相关模型
├── utils/
│   ├── __init__.py
│   ├── rag.py                   # RAG功能实现
│   └── llm.py                   # LLM功能实现
├── config.py                    # 配置文件
└── README.md                    # 本文档
```

## 数据库表结构

所有表都使用`ai_selection_`前缀，避免与主系统冲突：

- `ai_selection_regions` - 地区表
- `ai_selection_schools` - 学校表
- `ai_selection_programs` - 专业表
- `ai_selection_cases` - 案例表
- `ai_selection_program_school` - 专业-学校关联表

## API端点

### 推荐接口

- `POST /api/ai-selection/recommendation/recommend` - 获取选校推荐
  - 请求体：
    ```json
    {
      "undergraduate_school": "某985高校",
      "undergraduate_school_tier": "tier1",
      "undergraduate_major": "计算机科学与技术",
      "gpa": 85.0,
      "target_regions": ["英国", "香港", "新加坡"],
      "target_degree": "硕士",
      "target_major_direction": "人工智能",
      "language_scores": {"雅思": 6.5},
      "key_experiences": "实习经历、项目经验等"
    }
    ```

- `POST /api/ai-selection/recommendation/analyze_profile` - 分析用户画像

### 数据查询接口

- `GET /api/ai-selection/data/regions` - 获取地区列表
- `GET /api/ai-selection/data/schools` - 获取学校列表
- `GET /api/ai-selection/data/programs` - 获取专业列表
- `GET /api/ai-selection/data/cases` - 获取案例列表

## 初始化步骤

1. **安装依赖**
   ```bash
   cd backend
   pip install -r requirements.txt
   ```

2. **初始化数据库**
   ```bash
   cd backend
   python -m app.ai_selection.db.init_db
   ```
   
   这将：
   - 创建所有ai_selection_前缀的表
   - 初始化示例数据（地区、学校、专业、案例）

3. **启动服务**
   主项目的启动命令会自动包含AI选校系统的路由

## 配置说明

在`config.py`中可以配置：

- `CANDIDATE_POOL_SIZE` - 候选池大小
- `FINAL_RECOMMENDATIONS` - 最终推荐数量
- `GPA_TOLERANCE` - GPA匹配容差
- `WEIGHTS` - 各维度评分权重
- `SILICONE_FLOW_API_KEY` - LLM API密钥

## 技术架构

1. **用户画像构建**：使用LLM增强用户输入，提取关键技能和推断兴趣领域

2. **候选池生成**：
   - 首先使用ORM硬筛选（地区、学位、专业方向）
   - 如果结果不足，使用向量检索补充

3. **多维度匹配**：
   - 院校层级匹配：基于历史案例识别可达院校
   - 专业方向匹配：使用LLM和向量相似度评估
   - 经历匹配：分析用户经历与专业要求的契合度
   - 学术表现匹配：评估GPA等学术指标

4. **排序和推荐**：综合多维度评分，生成个性化推荐理由

## 与主系统的集成

- 共享数据库连接（使用主系统的`get_db`）
- 共享Base模型定义
- 独立的表结构（带前缀）
- 独立的API路由（`/api/ai-selection`）

## 注意事项

1. 所有数据库表都使用`ai_selection_`前缀
2. API路由都在`/api/ai-selection`下
3. 配置可以从主系统的settings中获取
4. 嵌入向量生成需要API密钥

## 未来扩展

- 添加更多学校和专业数据
- 优化向量检索算法
- 增加用户反馈机制
- 支持更多维度的匹配 