# AI智能选校系统集成文档

## 系统概述

AI智能选校系统是一个基于RAG（检索增强生成）和LLM的留学智能选校推荐系统，现已集成到TunshuEdu平台中。

## 目录结构

```
app/ai_selection/
├── __init__.py
├── api/
│   ├── __init__.py
│   ├── router.py
│   └── endpoints/
│       ├── __init__.py
│       ├── recommendation.py    # 推荐相关接口
│       └── data.py              # 数据查询接口
├── core/
│   ├── __init__.py
│   ├── user_profile.py          # 用户画像构建
│   ├── candidate_pool.py        # 候选池生成
│   ├── school_matching.py       # 学校匹配
│   ├── program_matching.py      # 专业匹配
│   └── ranking.py               # 排序和推荐生成
├── db/
│   ├── __init__.py
│   ├── models.py                # 数据库模型（带ai_selection_前缀）
│   ├── seed.py                  # 种子数据
│   └── init_db.py               # 数据库初始化脚本
├── schemas/
│   ├── __init__.py
│   ├── user.py                  # 用户相关模型
│   └── recommendation.py        # 推荐相关模型
├── utils/
│   ├── __init__.py
│   ├── rag.py                   # RAG功能实现
│   └── llm.py                   # LLM功能实现
├── config.py                    # 配置文件
└── README.md                    # 本文档
```

## 数据库表结构（最新版本）

### 表设计改进

系统采用简化的表结构设计，将原来的4张表合并为2张核心表：

#### 1. `ai_selection_programs` - 专业项目表（合并了学校和地区信息）

包含完整的学校和专业信息：

**学校信息字段：**
- `school_name_cn` - 学校中文名（必填）
- `school_name_en` - 学校英文名
- `school_qs_name` - 学校QS英文名
- `school_qs_rank` - 学校QS排名
- `school_region` - 学校所在地区

**专业信息字段：**
- `program_code` - 专业代码
- `degree` - 申请学位类型
- `program_name_cn` - 专业中文名（必填）
- `program_name_en` - 专业英文名
- `program_category` - 专业大类
- `program_direction` - 专业方向
- `faculty` - 所在学院

**申请信息字段：**
- `enrollment_time` - 入学时间
- `program_duration` - 项目时长
- `program_tuition` - 项目学费
- `application_time` - 申请时间
- `application_requirements` - 申请要求
- `gpa_requirements` - 绩点要求
- `language_requirements` - 语言要求

**详细信息字段：**
- `program_objectives` - 培养目标
- `courses` - 课程设置
- `program_website` - 项目官网
- `other_cost` - 年开销预估值
- `degree_evaluation` - 留服认证
- `embedding` - 专业描述的向量嵌入（用于AI匹配）

#### 2. `ai_selection_cases` - 申请案例表（字段已重命名）

存储真实的申请案例，字段名称更新为：

**学生背景：**
- `student_name` - 学生姓名
- `undergraduate_school` - 本科学校
- `undergraduate_school_tier` - 本科学校层级
- `undergraduate_major` - 本科专业
- `gpa` - 绩点
- `language_score` - 语言成绩（JSONB格式）
- `key_experiences` - 关键经历描述

**申请结果（字段重命名）：**
- `offer_region` - 获得offer的地区（原 `target_region`）
- `offer_degree` - 获得offer的学位类型（原 `target_degree`）
- `offer_major_direction` - 获得offer的专业方向（原 `target_major_direction`）
- `offer_program_id` - 获得offer的专业项目ID（原 `target_program_id`）
- `embedding` - 案例描述的向量嵌入

**移除的字段：**
- 移除了 `target_school_id`，因为学校信息已合并到programs表中

### 数据库变更优势

1. **简化结构**：从5张表（regions, schools, programs, program_school, cases）简化为2张表
2. **减少JOIN操作**：查询时不需要多表关联，提升性能
3. **数据完整性**：每个专业项目包含完整的学校和地区信息
4. **便于扩展**：新增学校或专业字段更加灵活
5. **减少数据冗余**：虽然可能存在学校信息重复，但换来了查询性能的提升

## API端点

### 推荐接口

- `POST /api/ai-selection/recommendation/recommend` - 获取选校推荐
  - 请求体：
    ```json
    {
      "undergraduate_school": "某985高校",
      "undergraduate_school_tier": "tier1",
      "undergraduate_major": "计算机科学与技术",
      "gpa": 85.0,
      "target_regions": ["英国", "香港", "新加坡"],
      "target_degree": "硕士",
      "target_major_direction": "人工智能",
      "language_scores": {"雅思": 6.5},
      "key_experiences": "实习经历、项目经验等"
    }
    ```

- `POST /api/ai-selection/recommendation/analyze_profile` - 分析用户画像

### 数据查询接口

- `GET /api/ai-selection/data/programs` - 获取专业项目列表（包含学校信息）
- `GET /api/ai-selection/data/cases` - 获取申请案例列表
- `GET /api/ai-selection/data/schools` - 获取学校列表（从programs表提取）
- `GET /api/ai-selection/data/regions` - 获取地区列表（从programs表提取）

## 数据库迁移

### 执行迁移

如果你有现有的AI选校数据，需要执行数据库迁移：

```bash
cd backend
alembic upgrade head
```

### 重新初始化（全新安装）

如果是全新安装，可以直接初始化：

```bash
cd backend
python -m app.ai_selection.db.init_db
```

这将：
- 创建新的合并表结构
- 初始化示例数据（包含5个世界知名大学的专业项目）
- 生成对应的申请案例数据

### 示例数据

系统将初始化以下示例数据：

**专业项目示例：**
1. 帝国理工学院 - 纯数学理学硕士
2. 香港理工大学 - 微电子技术与材料理学硕士
3. 南洋理工大学 - 人工智能理学硕士
4. 香港中文大学 - 金融学理学硕士
5. 麻省理工学院 - 计算机科学理学硕士

**申请案例示例：**
- L同学：微电子专业 → 香港理工大学微电子技术与材料
- Z同学：计算机专业 → 南洋理工大学人工智能
- W同学：金融专业 → 香港中文大学金融学
- C同学：数学专业 → 帝国理工学院纯数学

## 初始化步骤

1. **安装依赖**
   ```bash
   cd backend
   pip install -r requirements.txt
   ```

2. **执行数据库迁移**
   ```bash
   cd backend
   alembic upgrade head
   ```

3. **初始化种子数据**
   ```bash
   cd backend
   python -m app.ai_selection.db.init_db
   ```

4. **启动服务**
   主项目的启动命令会自动包含AI选校系统的路由

## 配置说明

在`config.py`中可以配置：

- `CANDIDATE_POOL_SIZE` - 候选池大小
- `FINAL_RECOMMENDATIONS` - 最终推荐数量
- `GPA_TOLERANCE` - GPA匹配容差
- `WEIGHTS` - 各维度评分权重
- `SILICONE_FLOW_API_KEY` - LLM API密钥

## 技术架构

1. **用户画像构建**：使用LLM增强用户输入，提取关键技能和推断兴趣领域

2. **候选池生成**：
   - 首先在programs表中使用硬筛选（地区、学位、专业方向）
   - 如果结果不足，使用向量检索补充

3. **多维度匹配**：
   - 院校层级匹配：基于历史案例识别可达院校
   - 专业方向匹配：使用LLM和向量相似度评估
   - 经历匹配：分析用户经历与专业要求的契合度
   - 学术表现匹配：评估GPA等学术指标

4. **排序和推荐**：综合多维度评分，生成个性化推荐理由

## 与主系统的集成

- 共享数据库连接（使用主系统的`get_db`）
- 共享Base模型定义
- 独立的表结构（带前缀）
- 独立的API路由（`/api/ai-selection`）

## 注意事项

1. 所有数据库表都使用`ai_selection_`前缀
2. API路由都在`/api/ai-selection`下
3. 配置可以从主系统的settings中获取
4. 嵌入向量生成需要API密钥
5. 数据库结构已优化，减少了表间关联的复杂性

## 未来扩展

- 添加更多学校和专业数据
- 优化向量检索算法
- 增加用户反馈机制
- 支持更多维度的匹配
- 考虑添加专业排名、学费对比等功能 