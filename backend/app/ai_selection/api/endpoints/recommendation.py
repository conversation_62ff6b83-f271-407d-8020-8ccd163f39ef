from fastapi import APIRouter, Depends, HTTPException
from typing import List, Dict, Any

from app.ai_selection.schemas.user import UserInput, EnhancedUserProfile
from app.ai_selection.schemas.recommendation import RecommendationResponse
from app.ai_selection.core.user_profile import build_user_profile
from app.ai_selection.core.candidate_pool import generate_candidate_pool
from app.ai_selection.core.school_matching import identify_reachable_schools, calculate_school_match_scores
from app.ai_selection.core.program_matching import match_programs_with_user_background
from app.ai_selection.core.ranking import rank_and_generate_recommendations

router = APIRouter()

@router.post("/recommend", response_model=RecommendationResponse)
async def get_school_recommendations(user_input: UserInput) -> RecommendationResponse:
    """
    基于用户输入生成个性化的院校和专业推荐
    
    Args:
        user_input: 用户输入信息
        
    Returns:
        个性化推荐结果
    """
    # 阶段1: 用户画像构建
    enhanced_profile = await build_user_profile(user_input)
    
    # 阶段2: 初步候选池生成
    candidate_pool = await generate_candidate_pool(enhanced_profile)
    
    if not candidate_pool:
        raise HTTPException(
            status_code=404,
            detail="未找到符合条件的专业，请尝试调整专业方向或目标地区"
        )
    
    # 阶段3.A: 院校层级对标
    reachable_schools, similar_cases = await identify_reachable_schools(enhanced_profile)
    
    # 计算每个候选学校的院校层级匹配分数
    candidate_pool = await calculate_school_match_scores(candidate_pool, reachable_schools)
    
    # 阶段3.B: 专业与个人背景深度匹配
    candidate_pool = await match_programs_with_user_background(
        candidate_pool,
        enhanced_profile,
        similar_cases
    )
    
    # 阶段3.C: 综合评分与个性化排序
    recommendations = await rank_and_generate_recommendations(
        candidate_pool,
        enhanced_profile,
        similar_cases
    )
    
    return recommendations

@router.post("/analyze_profile")
async def analyze_user_profile(user_input: UserInput) -> Dict[str, Any]:
    """
    分析用户画像，返回增强后的用户信息
    
    Args:
        user_input: 用户输入信息
        
    Returns:
        增强后的用户画像
    """
    enhanced_profile = await build_user_profile(user_input)
    return enhanced_profile.dict() 