from fastapi import APIRouter, HTTPException, Query
from typing import List, Dict, Any
from sqlalchemy import select, and_
import json

from app.db.database import get_db
from app.ai_selection.db.models import (
    AISelectionRegion as Region, 
    AISelectionSchool as School, 
    AISelectionProgram as Program, 
    AISelectionCase as Case,
    ai_selection_program_school as program_school
)

router = APIRouter()

@router.get("/regions")
async def get_regions() -> List[Dict[str, Any]]:
    """
    获取所有地区列表
    
    Returns:
        地区列表
    """
    async for session in get_db():
        query = select(Region.id, Region.region_name)
        result = await session.execute(query)
        rows = result.fetchall()
        return [{"id": row.id, "name": row.region_name} for row in rows]

@router.get("/schools")
async def get_schools(
    region_id: int = Query(None, description="按地区ID筛选"),
    tier: str = Query(None, description="按学校层级筛选")
) -> List[Dict[str, Any]]:
    """
    获取学校列表，支持按地区和层级筛选
    
    Args:
        region_id: 地区ID（可选）
        tier: 学校层级（可选）
        
    Returns:
        学校列表
    """
    async for session in get_db():
        query = (
            select(
                School.id, 
                School.school_name, 
                School.tier, 
                School.region_id,
                Region.region_name
            )
            .join(Region, School.region_id == Region.id)
        )
        
        conditions = []
        if region_id is not None:
            conditions.append(School.region_id == region_id)
        if tier is not None:
            conditions.append(School.tier == tier)
        
        if conditions:
            query = query.where(and_(*conditions))
        
        result = await session.execute(query)
        rows = result.fetchall()
        
        return [
            {
                "id": row.id,
                "name": row.school_name,
                "tier": row.tier,
                "region_id": row.region_id,
                "region_name": row.region_name
            }
            for row in rows
        ]

@router.get("/programs")
async def get_programs(
    school_id: int = Query(None, description="按学校ID筛选"),
    discipline: str = Query(None, description="按学科分类筛选"),
    degree: str = Query(None, description="按学位类型筛选")
) -> List[Dict[str, Any]]:
    """
    获取专业列表，支持按学校、学科和学位筛选
    
    Args:
        school_id: 学校ID（可选）
        discipline: 学科分类（可选）
        degree: 学位类型（可选）
        
    Returns:
        专业列表
    """
    async for session in get_db():
        query = (
            select(
                Program.id,
                Program.program_name_cn,
                Program.program_name_en,
                Program.program_direction,
                Program.faculty,
                Program.degree,
                School.id.label("school_id"),
                School.school_name
            )
            .select_from(
                program_school
                .join(Program, program_school.c.program_id == Program.id)
                .join(School, program_school.c.school_id == School.id)
            )
        )
        
        conditions = []
        if school_id is not None:
            conditions.append(School.id == school_id)
        if discipline is not None:
            conditions.append(Program.program_direction.like(f"%{discipline}%"))
        if degree is not None:
            conditions.append(Program.degree == degree)
        
        if conditions:
            query = query.where(and_(*conditions))
        
        result = await session.execute(query)
        rows = result.fetchall()
        
        return [
            {
                "id": row.id,
                "name": row.program_name_cn,
                "name_en": row.program_name_en,
                "discipline": row.program_direction,
                "faculty": row.faculty,
                "degree": row.degree,
                "school_id": row.school_id,
                "school_name": row.school_name
            }
            for row in rows
        ]

@router.get("/cases")
async def get_cases(
    limit: int = Query(5, description="返回案例数量"),
    school_id: int = Query(None, description="按学校ID筛选"),
    program_id: int = Query(None, description="按专业ID筛选"),
    undergraduate_tier: str = Query(None, description="按本科院校层级筛选")
) -> List[Dict[str, Any]]:
    """
    获取案例列表，支持各种筛选条件
    
    Args:
        limit: 返回数量
        school_id: 学校ID（可选）
        program_id: 专业ID（可选）
        undergraduate_tier: 本科院校层级（可选）
        
    Returns:
        案例列表
    """
    async for session in get_db():
        query = (
            select(
                Case.id,
                Case.student_name,
                Case.undergraduate_school,
                Case.undergraduate_school_tier,
                Case.undergraduate_major,
                Case.gpa,
                Case.target_region,
                Case.target_degree,
                Case.target_major_direction,
                Case.key_experiences,
                Case.language_score,
                School.id.label("school_id"),
                School.school_name,
                Program.id.label("program_id"),
                Program.program_name_cn
            )
            .join(School, Case.target_school_id == School.id)
            .join(Program, Case.target_program_id == Program.id)
        )
        
        conditions = []
        if school_id is not None:
            conditions.append(School.id == school_id)
        if program_id is not None:
            conditions.append(Program.id == program_id)
        if undergraduate_tier is not None:
            conditions.append(Case.undergraduate_school_tier == undergraduate_tier)
        
        if conditions:
            query = query.where(and_(*conditions))
        
        query = query.limit(limit)
        
        result = await session.execute(query)
        rows = result.fetchall()
        
        cases = []
        for row in rows:
            case_dict = {
                "id": row.id,
                "student_name": row.student_name,
                "undergraduate_school": row.undergraduate_school,
                "undergraduate_school_tier": row.undergraduate_school_tier,
                "undergraduate_major": row.undergraduate_major,
                "gpa": row.gpa,
                "target_region": row.target_region,
                "target_degree": row.target_degree,
                "target_major_direction": row.target_major_direction,
                "key_experiences": row.key_experiences,
                "language_score": json.loads(row.language_score) if isinstance(row.language_score, str) else row.language_score,
                "target_school_id": row.school_id,
                "target_school_name": row.school_name,
                "target_program_id": row.program_id,
                "target_program_name": row.program_name_cn
            }
            cases.append(case_dict)
        
        return cases 