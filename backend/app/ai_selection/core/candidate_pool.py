from typing import List, Dict, Any, Set
import json
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
from sqlalchemy import select, and_, or_
from sqlalchemy.orm import joinedload

from app.db.database import get_db
from app.ai_selection.db.models import (
    AISelectionRegion as Region, 
    AISelectionSchool as School, 
    AISelectionProgram as Program, 
    AISelectionCase as Case, 
    ai_selection_program_school as program_school
)
from app.ai_selection.schemas.user import EnhancedUserProfile
from app.ai_selection.config import CANDIDATE_POOL_SIZE
from app.ai_selection.utils.rag import embed_text

async def generate_candidate_pool(user_profile: EnhancedUserProfile) -> List[Dict[str, Any]]:
    """
    基于用户画像生成初步候选池
    
    Args:
        user_profile: 增强用户画像
        
    Returns:
        候选池列表，包含学校和专业信息
    """
    # 首先使用ORM硬筛选生成候选池
    candidate_pool = await _orm_based_filtering(user_profile)
    
    # 检查候选池大小，如果不足预期，则使用向量检索补充
    min_expected_size = min(CANDIDATE_POOL_SIZE, 10)  # 至少期望10个结果或配置的候选池大小
    
    if len(candidate_pool) < min_expected_size:
        # 获取已有候选池的专业ID
        existing_program_ids = {program["id"] for program in candidate_pool}
        
        # 使用向量检索补充候选池
        vector_results = await _vector_based_filtering(
            user_profile, 
            existing_program_ids, 
            min_expected_size - len(candidate_pool)
        )
        
        # 合并结果
        candidate_pool.extend(vector_results)
    
    return candidate_pool

async def _orm_based_filtering(user_profile: EnhancedUserProfile) -> List[Dict[str, Any]]:
    """
    使用ORM硬筛选条件生成候选池
    
    Args:
        user_profile: 增强用户画像
        
    Returns:
        ORM筛选的候选池
    """
    async for session in get_db():
        # 构建查询参数
        target_regions = user_profile.target_regions
        target_degree = user_profile.target_degree
        target_major_direction = user_profile.target_major_direction
        
        # 构建专业匹配条件（使用宽松匹配）
        discipline_match = "%"  # 默认匹配所有学科
        
        # 基于目标专业方向推断学科领域
        if "计算机" in target_major_direction or "软件" in target_major_direction or "人工智能" in target_major_direction:
            discipline_match = "计算机科学"
        elif "电子" in target_major_direction or "电气" in target_major_direction:
            discipline_match = "电子工程"
        elif "数学" in target_major_direction:
            discipline_match = "数学"
        elif "商业" in target_major_direction or "金融" in target_major_direction or "经济" in target_major_direction:
            discipline_match = "商业"

        # 如果有推断的兴趣领域，也考虑进去
        if user_profile.inferred_interest_domain:
            if "计算机" in user_profile.inferred_interest_domain:
                discipline_match = "计算机科学"
            elif "电子" in user_profile.inferred_interest_domain:
                discipline_match = "电子工程"
            elif "数学" in user_profile.inferred_interest_domain:
                discipline_match = "数学"
            elif "商业" in user_profile.inferred_interest_domain:
                discipline_match = "商业"
        
        # 构建ORM查询
        query = (
            select(
                Program.id,
                Program.program_name_cn,
                Program.program_name_en,
                Program.program_direction,
                Program.faculty,
                Program.program_objectives,
                Program.degree,
                Program.program_duration,
                Program.program_tuition,
                Program.gpa_requirements,
                School.id.label("school_id"),
                School.school_name,
                School.tier.label("school_tier"),
                Region.id.label("region_id"),
                Region.region_name
            )
            .select_from(
                program_school
                .join(Program, program_school.c.program_id == Program.id)
                .join(School, program_school.c.school_id == School.id)
                .join(Region, School.region_id == Region.id)
            )
            .where(
                and_(
                    Program.degree == target_degree,
                    Region.region_name.in_(target_regions),
                    or_(
                        Program.program_direction.like(f"%{discipline_match}%"),
                        Program.program_name_cn.like(f"%{target_major_direction}%"),
                        Program.program_name_en.like(f"%{target_major_direction}%")
                    )
                )
            )
            .limit(CANDIDATE_POOL_SIZE)
        )

        result = await session.execute(query)
        rows = result.fetchall()

        # 转换结果为字典列表
        candidate_pool = []
        for row in rows:
            program_dict = {
                "id": row.id,
                "program_name_cn": row.program_name_cn,
                "program_name_en": row.program_name_en,
                "program_direction": row.program_direction,
                "faculty": row.faculty,
                "program_objectives": row.program_objectives,
                "degree": row.degree,
                "program_duration": row.program_duration,
                "program_tuition": row.program_tuition,
                "gpa_requirements": row.gpa_requirements,
                "school_id": row.school_id,
                "school_name": row.school_name,
                "school_tier": row.school_tier,
                "region_id": row.region_id,
                "region_name": row.region_name,
                "match_source": "orm_filtering"  # 标记来源
            }
            candidate_pool.append(program_dict)

        return candidate_pool

async def _vector_based_filtering(
    user_profile: EnhancedUserProfile,
    exclude_program_ids: Set[int],
    limit: int = 10,
    similarity_threshold: float = 0.5
) -> List[Dict[str, Any]]:
    """
    使用向量检索方法补充候选池
    
    Args:
        user_profile: 增强用户画像
        exclude_program_ids: 要排除的专业ID集合
        limit: 需要返回的候选数量 
        similarity_threshold: 相似度阈值，低于此值的专业将被过滤
        
    Returns:
        向量检索的候选池
    """
    async for session in get_db():
        target_regions = user_profile.target_regions
        target_degree = user_profile.target_degree
        target_major_direction = user_profile.target_major_direction
        
        # 构建ORM查询
        query = (
            select(
                Program.id,
                Program.program_name_cn,
                Program.program_name_en,
                Program.program_direction,
                Program.faculty,
                Program.program_objectives,
                Program.degree,
                Program.program_duration,
                Program.program_tuition,
                Program.gpa_requirements,
                Program.embedding,
                School.id.label("school_id"),
                School.school_name,
                School.tier.label("school_tier"),
                Region.id.label("region_id"),
                Region.region_name
            )
            .select_from(
                program_school
                .join(Program, program_school.c.program_id == Program.id)
                .join(School, program_school.c.school_id == School.id)
                .join(Region, School.region_id == Region.id)
            )
            .where(
                and_(
                    Program.degree == target_degree,
                    Region.region_name.in_(target_regions),
                    Program.embedding.isnot(None),
                    ~Program.id.in_(exclude_program_ids) if exclude_program_ids else True
                )
            )
        )
        
        result = await session.execute(query)
        rows = result.fetchall()
        
        # 如果没有结果，直接返回空列表
        if not rows:
            return []
        
        # 将结果转换为字典列表，并提取嵌入向量
        programs = []
        program_vectors = []
        
        for row in rows:
            program_dict = {
                "id": row.id,
                "program_name_cn": row.program_name_cn,
                "program_name_en": row.program_name_en,
                "program_direction": row.program_direction,
                "faculty": row.faculty,
                "program_objectives": row.program_objectives,
                "degree": row.degree,
                "program_duration": row.program_duration,
                "program_tuition": row.program_tuition,
                "gpa_requirements": row.gpa_requirements,
                "school_id": row.school_id,
                "school_name": row.school_name,
                "school_tier": row.school_tier,
                "region_id": row.region_id,
                "region_name": row.region_name
            }
            
            # 解析嵌入向量
            try:
                # 检查嵌入向量的类型，处理不同的存储格式
                if isinstance(row.embedding, str):
                    # 如果是字符串，使用json.loads解析
                    embedding = json.loads(row.embedding)
                elif isinstance(row.embedding, (list, tuple)):
                    # 如果已经是列表或元组，直接使用
                    embedding = row.embedding
                else:
                    # 其他类型，跳过此专业
                    print(f"专业 ID {row.id} 的嵌入向量格式不支持: {type(row.embedding)}")
                    continue
                
                embedding_vector = np.array(embedding)
                
                # 添加到列表
                programs.append(program_dict)
                program_vectors.append(embedding_vector)
            except Exception as e:
                print(f"处理专业 ID {row.id} 的嵌入向量时出错: {e}")
                continue
        
        # 为目标专业方向生成嵌入向量
        try:
            target_embedding = embed_text(target_major_direction)
            
            # 计算相似度
            if program_vectors:
                similarities = cosine_similarity([target_embedding], program_vectors)[0]
                
                # 创建(索引,相似度)元组列表并排序
                indexed_sims = [(i, sim) for i, sim in enumerate(similarities)]
                indexed_sims.sort(key=lambda x: x[1], reverse=True)
                
                # 过滤低于阈值的结果，并限制数量
                results = []
                for idx, sim in indexed_sims:
                    if sim >= similarity_threshold and len(results) < limit:
                        program = programs[idx].copy()
                        program["vector_similarity"] = float(sim)
                        program["match_source"] = "vector_filtering"  # 标记来源
                        results.append(program)
                
                return results
        except Exception as e:
            print(f"向量检索过程出错: {e}")
        
        return [] 