from typing import List, Dict, Any, Set
import json
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
from sqlalchemy import select, and_, or_

from app.db.database import get_db
from app.ai_selection.db.models import (
    AISelectionProgram as Program, 
    AISelectionCase as Case
)
from app.ai_selection.schemas.user import EnhancedUserProfile
from app.ai_selection.config import CANDIDATE_POOL_SIZE
from app.ai_selection.utils.rag import embed_text

async def generate_candidate_pool(user_profile: EnhancedUserProfile) -> List[Dict[str, Any]]:
    """
    基于用户画像生成初步候选池
    
    Args:
        user_profile: 增强用户画像
        
    Returns:
        候选池列表，包含学校和专业信息
    """
    # 首先使用ORM硬筛选生成候选池
    candidate_pool = await _orm_based_filtering(user_profile)
    
    # 检查候选池大小，如果不足预期，则使用向量检索补充
    min_expected_size = min(CANDIDATE_POOL_SIZE, 10)  # 至少期望10个结果或配置的候选池大小
    
    if len(candidate_pool) < min_expected_size:
        # 获取已有候选池的专业ID
        existing_program_ids = {program["id"] for program in candidate_pool}
        
        # 使用向量检索补充候选池，提高相似度阈值要求
        vector_results = await _vector_based_filtering(
            user_profile, 
            existing_program_ids, 
            min_expected_size - len(candidate_pool),
            similarity_threshold=0.7  # 提高阈值从0.5到0.7，要求更高相似度
        )
        
        # 合并结果
        candidate_pool.extend(vector_results)
    
    return candidate_pool

async def _orm_based_filtering(user_profile: EnhancedUserProfile) -> List[Dict[str, Any]]:
    """
    使用ORM硬筛选条件生成候选池，采用更精确的专业方向匹配策略
    
    Args:
        user_profile: 增强用户画像
        
    Returns:
        ORM筛选的候选池
    """
    async for session in get_db():
        # 构建查询参数
        target_regions = user_profile.target_regions
        target_degree = user_profile.target_degree
        target_major_direction = user_profile.target_major_direction.lower()
        undergraduate_major = user_profile.undergraduate_major.lower()
        
        # 第一层：精确匹配策略
        exact_matches = await _get_exact_matches(
            session, target_regions, target_degree, target_major_direction
        )
        
        # 第二层：如果精确匹配结果不足，进行模糊匹配
        if len(exact_matches) < CANDIDATE_POOL_SIZE // 2:  # 少于期望结果的一半
            fuzzy_matches = await _get_fuzzy_matches(
                session, target_regions, target_degree, target_major_direction, 
                {p["id"] for p in exact_matches}
            )
            exact_matches.extend(fuzzy_matches)
        
        # 第三层：如果仍然不足，考虑相关专业匹配
        if len(exact_matches) < CANDIDATE_POOL_SIZE // 3:  # 少于期望结果的三分之一
            related_matches = await _get_related_matches(
                session, target_regions, target_degree, undergraduate_major, target_major_direction,
                {p["id"] for p in exact_matches}, user_profile.inferred_interest_domain
            )
            exact_matches.extend(related_matches)
        
        return exact_matches[:CANDIDATE_POOL_SIZE]

async def _get_exact_matches(
    session, target_regions: List[str], target_degree: str, target_major_direction: str
) -> List[Dict[str, Any]]:
    """
    第一层：精确匹配 - 专业名称中包含用户目标专业方向的关键词
    """
    # 提取目标专业方向的核心关键词
    core_keywords = _extract_core_keywords(target_major_direction)
    
    # 构建精确匹配条件
    exact_conditions = []
    for keyword in core_keywords:
        exact_conditions.append(
            or_(
                Program.program_name_cn.like(f"%{keyword}%"),
                Program.program_name_en.ilike(f"%{keyword}%"),  # 忽略大小写
                Program.program_direction.like(f"%{keyword}%")
            )
        )
    
    if not exact_conditions:
        return []
    
    query = (
        select(
            Program.id,
            Program.program_name_cn,
            Program.program_name_en,
            Program.program_direction,
            Program.faculty,
            Program.program_objectives,
            Program.degree,
            Program.program_duration,
            Program.program_tuition,
            Program.gpa_requirements,
            Program.school_name_cn,
            Program.school_name_en,
            Program.school_qs_rank,
            Program.school_region
        )
        .where(
            and_(
                Program.degree == target_degree,
                Program.school_region.in_(target_regions),
                or_(*exact_conditions)  # 任一关键词匹配即可
            )
        )
        .limit(CANDIDATE_POOL_SIZE)
    )
    
    result = await session.execute(query)
    rows = result.fetchall()
    
    candidates = []
    for row in rows:
        candidate = _create_candidate_dict(row, "exact_match")
        candidates.append(candidate)
    
    return candidates

async def _get_fuzzy_matches(
    session, target_regions: List[str], target_degree: str, 
    target_major_direction: str, exclude_ids: Set[int]
) -> List[Dict[str, Any]]:
    """
    第二层：模糊匹配 - 基于学科分类和专业方向匹配
    """
    # 基于目标专业方向推断学科领域，使用更精确的映射
    discipline_keywords = _get_discipline_keywords(target_major_direction)
    
    if not discipline_keywords:
        return []
    
    # 构建模糊匹配条件
    fuzzy_conditions = []
    for keyword in discipline_keywords:
        fuzzy_conditions.append(Program.program_direction.like(f"%{keyword}%"))
    
    exclude_condition = []
    if exclude_ids:
        exclude_condition.append(~Program.id.in_(exclude_ids))
    
    query = (
        select(
            Program.id,
            Program.program_name_cn,
            Program.program_name_en,
            Program.program_direction,
            Program.faculty,
            Program.program_objectives,
            Program.degree,
            Program.program_duration,
            Program.program_tuition,
            Program.gpa_requirements,
            Program.school_name_cn,
            Program.school_name_en,
            Program.school_qs_rank,
            Program.school_region
        )
        .where(
            and_(
                Program.degree == target_degree,
                Program.school_region.in_(target_regions),
                or_(*fuzzy_conditions),
                *exclude_condition
            )
        )
        .limit(CANDIDATE_POOL_SIZE // 2)
    )
    
    result = await session.execute(query)
    rows = result.fetchall()
    
    candidates = []
    for row in rows:
        candidate = _create_candidate_dict(row, "fuzzy_match")
        candidates.append(candidate)
    
    return candidates

async def _get_related_matches(
    session, target_regions: List[str], target_degree: str, 
    undergraduate_major: str, target_major_direction: str,
    exclude_ids: Set[int], inferred_interest_domain: str = None
) -> List[Dict[str, Any]]:
    """
    第三层：相关匹配 - 基于本科专业和推断兴趣领域的相关专业
    """
    # 获取与本科专业相关的专业方向
    related_disciplines = _get_related_disciplines(undergraduate_major, inferred_interest_domain)
    
    if not related_disciplines:
        return []
    
    # 构建相关匹配条件
    related_conditions = []
    for discipline in related_disciplines:
        related_conditions.append(Program.program_direction.like(f"%{discipline}%"))
    
    exclude_condition = []
    if exclude_ids:
        exclude_condition.append(~Program.id.in_(exclude_ids))
    
    query = (
        select(
            Program.id,
            Program.program_name_cn,
            Program.program_name_en,
            Program.program_direction,
            Program.faculty,
            Program.program_objectives,
            Program.degree,
            Program.program_duration,
            Program.program_tuition,
            Program.gpa_requirements,
            Program.school_name_cn,
            Program.school_name_en,
            Program.school_qs_rank,
            Program.school_region
        )
        .where(
            and_(
                Program.degree == target_degree,
                Program.school_region.in_(target_regions),
                or_(*related_conditions),
                *exclude_condition
            )
        )
        .limit(CANDIDATE_POOL_SIZE // 3)
    )
    
    result = await session.execute(query)
    rows = result.fetchall()
    
    candidates = []
    for row in rows:
        candidate = _create_candidate_dict(row, "related_match")
        candidates.append(candidate)
    
    return candidates

def _extract_core_keywords(target_major_direction: str) -> List[str]:
    """
    从目标专业方向中提取核心关键词
    """
    # 定义核心专业关键词映射
    keyword_mapping = {
        "人工智能": ["人工智能", "AI", "artificial intelligence", "machine learning", "机器学习"],
        "机器学习": ["机器学习", "machine learning", "AI", "人工智能", "数据科学"],
        "数据科学": ["数据科学", "data science", "大数据", "数据分析", "analytics"],
        "计算机科学": ["计算机科学", "computer science", "计算机", "软件工程"],
        "软件工程": ["软件工程", "software engineering", "软件开发", "程序设计"],
        "电子工程": ["电子工程", "electronic engineering", "电子", "微电子", "集成电路"],
        "电气工程": ["电气工程", "electrical engineering", "电气", "电力"],
        "通信工程": ["通信工程", "communication", "通信", "信息工程"],
        "金融": ["金融", "finance", "投资", "银行", "证券"],
        "经济": ["经济", "economics", "经济学", "宏观经济", "微观经济"],
        "管理": ["管理", "management", "工商管理", "企业管理"],
        "数学": ["数学", "mathematics", "应用数学", "统计", "statistics"],
        "物理": ["物理", "physics", "应用物理", "理论物理"],
        "化学": ["化学", "chemistry", "应用化学", "材料化学"],
        "生物": ["生物", "biology", "生物科学", "生物技术", "biotechnology"],
        "材料": ["材料", "materials", "材料科学", "材料工程"],
        "土木": ["土木", "civil engineering", "建筑工程", "结构工程"],
        "机械": ["机械", "mechanical", "机械工程", "自动化"]
    }
    
    keywords = []
    target_lower = target_major_direction.lower()
    
    # 直接匹配
    for key, values in keyword_mapping.items():
        if key in target_major_direction:
            keywords.extend(values)
            break
    
    # 如果没有直接匹配，尝试部分匹配
    if not keywords:
        for key, values in keyword_mapping.items():
            if any(part in target_major_direction for part in key.split()):
                keywords.extend(values[:2])  # 只取前两个最相关的关键词
    
    # 如果仍然没有匹配，返回原始输入的分词
    if not keywords:
        # 简单分词：按空格、顿号等分割
        import re
        parts = re.split(r'[、，,\s]+', target_major_direction)
        keywords = [part.strip() for part in parts if len(part.strip()) > 1]
    
    return list(set(keywords))  # 去重

def _get_discipline_keywords(target_major_direction: str) -> List[str]:
    """
    基于目标专业方向获取学科关键词
    """
    discipline_mapping = {
        # 计算机类
        "计算机": ["计算机科学", "软件工程", "信息技术"],
        "人工智能": ["计算机科学", "机器学习", "数据科学"],
        "软件": ["计算机科学", "软件工程"],
        "数据": ["数据科学", "计算机科学", "统计学"],
        
        # 工程类
        "电子": ["电子工程", "电气工程", "通信工程"],
        "电气": ["电气工程", "电子工程", "自动化"],
        "机械": ["机械工程", "自动化", "制造工程"],
        "土木": ["土木工程", "建筑工程", "结构工程"],
        
        # 理学类
        "数学": ["数学", "统计学", "应用数学"],
        "物理": ["物理学", "应用物理", "材料物理"],
        "化学": ["化学", "材料化学", "应用化学"],
        
        # 商科类
        "金融": ["金融", "经济学", "投资学"],
        "经济": ["经济学", "金融", "国际经济"],
        "管理": ["管理学", "工商管理", "企业管理"],
        
        # 生物类
        "生物": ["生物科学", "生物技术", "生物工程"]
    }
    
    keywords = []
    for key, values in discipline_mapping.items():
        if key in target_major_direction:
            keywords.extend(values)
    
    return list(set(keywords)) if keywords else []

def _get_related_disciplines(undergraduate_major: str, inferred_interest_domain: str = None) -> List[str]:
    """
    基于本科专业和推断兴趣领域获取相关学科
    """
    # 定义专业间的相关性映射
    related_mapping = {
        # 计算机类专业的相关领域
        "计算机": ["计算机科学", "软件工程", "人工智能", "数据科学", "信息技术"],
        "软件": ["计算机科学", "软件工程", "信息技术"],
        "信息": ["计算机科学", "信息技术", "数据科学"],
        
        # 电子类专业的相关领域  
        "电子": ["电子工程", "电气工程", "通信工程", "微电子"],
        "电气": ["电气工程", "电子工程", "自动化"],
        "通信": ["通信工程", "电子工程", "信息工程"],
        
        # 数学类专业的相关领域
        "数学": ["数学", "统计学", "数据科学", "金融数学"],
        "统计": ["统计学", "数学", "数据科学"],
        
        # 商科类专业的相关领域
        "金融": ["金融", "经济学", "投资学", "风险管理"],
        "经济": ["经济学", "金融", "国际贸易"],
        "管理": ["管理学", "工商管理", "金融"],
        "会计": ["金融", "经济学", "管理学"],
        
        # 工程类专业的相关领域
        "机械": ["机械工程", "自动化", "制造工程"],
        "土木": ["土木工程", "建筑工程", "环境工程"],
        "材料": ["材料科学", "化学工程", "物理学"],
        
        # 理学类专业的相关领域
        "物理": ["物理学", "材料科学", "工程物理"],
        "化学": ["化学", "材料科学", "化学工程"],
        "生物": ["生物科学", "生物技术", "生物医学"]
    }
    
    related_disciplines = []
    
    # 基于本科专业匹配
    for key, values in related_mapping.items():
        if key in undergraduate_major:
            related_disciplines.extend(values)
    
    # 基于推断兴趣领域补充
    if inferred_interest_domain:
        for key, values in related_mapping.items():
            if key in inferred_interest_domain.lower():
                related_disciplines.extend(values)
    
    return list(set(related_disciplines)) if related_disciplines else []

def _create_candidate_dict(row, match_source: str) -> Dict[str, Any]:
    """
    创建候选项目字典
    """
    return {
        "id": row.id,
        "program_name_cn": row.program_name_cn,
        "program_name_en": row.program_name_en,
        "program_direction": row.program_direction,
        "faculty": row.faculty,
        "program_objectives": row.program_objectives,
        "degree": row.degree,
        "program_duration": row.program_duration,
        "program_tuition": row.program_tuition,
        "gpa_requirements": row.gpa_requirements,
        # 学校信息
        "school_name": row.school_name_cn,
        "school_name_cn": row.school_name_cn,
        "school_name_en": row.school_name_en,
        "school_qs_rank": row.school_qs_rank,
        "school_region": row.school_region,
        "region_name": row.school_region,
        # 为了兼容性，推断学校层级
        "school_tier": _infer_school_tier_from_qs_rank(row.school_qs_rank),
        "match_source": match_source  # 标记匹配来源
    }

async def _vector_based_filtering(
    user_profile: EnhancedUserProfile,
    exclude_program_ids: Set[int],
    limit: int = 10,
    similarity_threshold: float = 0.5
) -> List[Dict[str, Any]]:
    """
    使用向量检索方法补充候选池
    
    Args:
        user_profile: 增强用户画像
        exclude_program_ids: 要排除的专业ID集合
        limit: 需要返回的候选数量 
        similarity_threshold: 相似度阈值，低于此值的专业将被过滤
        
    Returns:
        向量检索的候选池
    """
    async for session in get_db():
        target_regions = user_profile.target_regions
        target_degree = user_profile.target_degree
        target_major_direction = user_profile.target_major_direction
        
        # 构建ORM查询 - 直接查询programs表
        query = (
            select(
                Program.id,
                Program.program_name_cn,
                Program.program_name_en,
                Program.program_direction,
                Program.faculty,
                Program.program_objectives,
                Program.degree,
                Program.program_duration,
                Program.program_tuition,
                Program.gpa_requirements,
                Program.embedding,
                # 学校信息
                Program.school_name_cn,
                Program.school_name_en,
                Program.school_qs_rank,
                Program.school_region
            )
            .where(
                and_(
                    Program.degree == target_degree,
                    Program.school_region.in_(target_regions),
                    Program.embedding.isnot(None),
                    ~Program.id.in_(exclude_program_ids) if exclude_program_ids else True
                )
            )
        )
        
        result = await session.execute(query)
        rows = result.fetchall()
        
        # 如果没有结果，直接返回空列表
        if not rows:
            return []
        
        # 将结果转换为字典列表，并提取嵌入向量
        programs = []
        program_vectors = []
        
        for row in rows:
            program_dict = {
                "id": row.id,
                "program_name_cn": row.program_name_cn,
                "program_name_en": row.program_name_en,
                "program_direction": row.program_direction,
                "faculty": row.faculty,
                "program_objectives": row.program_objectives,
                "degree": row.degree,
                "program_duration": row.program_duration,
                "program_tuition": row.program_tuition,
                "gpa_requirements": row.gpa_requirements,
                # 学校信息
                "school_name": row.school_name_cn,
                "school_name_cn": row.school_name_cn,
                "school_name_en": row.school_name_en,
                "school_qs_rank": row.school_qs_rank,
                "school_region": row.school_region,
                "region_name": row.school_region,
                # 为了兼容性，推断学校层级
                "school_tier": _infer_school_tier_from_qs_rank(row.school_qs_rank)
            }
            
            # 解析嵌入向量
            try:
                # 检查嵌入向量的类型，处理不同的存储格式
                if isinstance(row.embedding, str):
                    # 如果是字符串，使用json.loads解析
                    embedding = json.loads(row.embedding)
                elif isinstance(row.embedding, (list, tuple)):
                    # 如果已经是列表或元组，直接使用
                    embedding = row.embedding
                else:
                    # 其他类型，跳过此专业
                    print(f"专业 ID {row.id} 的嵌入向量格式不支持: {type(row.embedding)}")
                    continue
                
                embedding_vector = np.array(embedding)
                
                # 添加到列表
                programs.append(program_dict)
                program_vectors.append(embedding_vector)
            except Exception as e:
                print(f"处理专业 ID {row.id} 的嵌入向量时出错: {e}")
                continue
        
        # 为目标专业方向生成嵌入向量
        try:
            target_embedding = embed_text(target_major_direction)
            
            # 计算相似度
            if program_vectors:
                similarities = cosine_similarity([target_embedding], program_vectors)[0]
                
                # 创建(索引,相似度)元组列表并排序
                indexed_sims = [(i, sim) for i, sim in enumerate(similarities)]
                indexed_sims.sort(key=lambda x: x[1], reverse=True)
                
                # 过滤低于阈值的结果，并限制数量
                results = []
                for idx, sim in indexed_sims:
                    if sim >= similarity_threshold and len(results) < limit:
                        program = programs[idx].copy()
                        program["vector_similarity"] = float(sim)
                        program["match_source"] = "vector_filtering"  # 标记来源
                        results.append(program)
                
                return results
        except Exception as e:
            print(f"向量检索过程出错: {e}")
        
        return []

def _infer_school_tier_from_qs_rank(qs_rank: str) -> str:
    """
    根据QS排名推断学校层级
    
    Args:
        qs_rank: QS排名字符串
        
    Returns:
        学校层级 (tier1, tier2, tier3)
    """
    if not qs_rank:
        return "tier3"  # 默认层级
    
    try:
        rank = int(qs_rank)
        if rank <= 50:
            return "tier1"
        elif rank <= 200:
            return "tier2"
        else:
            return "tier3"
    except (ValueError, TypeError):
        return "tier3"  # 如果无法解析排名，返回默认层级 