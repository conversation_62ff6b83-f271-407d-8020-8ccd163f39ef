from typing import Dict, Any

from app.ai_selection.schemas.user import UserInput, EnhancedUserProfile
from app.ai_selection.utils.llm import enhance_user_profile, extract_key_entities_from_experience

async def build_user_profile(user_input: UserInput) -> EnhancedUserProfile:
    """
    构建增强用户画像
    
    Args:
        user_input: 用户输入信息
        
    Returns:
        增强后的用户画像
    """
    # 将用户输入转换为字典
    user_dict = user_input.dict()
    
    # 使用LLM模拟函数增强用户画像
    enhanced_profile_dict = enhance_user_profile(
        user_dict,
        user_input.key_experiences or ""
    )
    
    # 创建增强用户画像模型实例
    enhanced_profile = EnhancedUserProfile(**enhanced_profile_dict)
    
    return enhanced_profile 