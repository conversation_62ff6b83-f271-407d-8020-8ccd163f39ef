from typing import List, Dict, Any
import time

from app.ai_selection.schemas.user import EnhancedUserProfile
from app.ai_selection.schemas.recommendation import RecommendationItem, RecommendationScore, RecommendationResponse
from app.ai_selection.utils.llm import generate_recommendation_reason
from app.ai_selection.config import WEIGHTS, FINAL_RECOMMENDATIONS

async def rank_and_generate_recommendations(
    candidate_pool: List[Dict[str, Any]],
    user_profile: EnhancedUserProfile,
    similar_cases: List[Dict[str, Any]]
) -> RecommendationResponse:
    """
    对候选池进行排序，生成最终推荐结果
    
    Args:
        candidate_pool: 带有评分的候选池
        user_profile: 用户画像
        similar_cases: 相似案例列表
        
    Returns:
        推荐结果响应
    """
    # 计算每个候选项目的总得分
    for candidate in candidate_pool:
        total_match = (
            WEIGHTS["school_tier_match"] * candidate.get("school_tier_match", 0) +
            WEIGHTS["program_direction_match"] * candidate.get("program_direction_match", 0) +
            WEIGHTS["experience_match"] * candidate.get("experience_match", 0) +
            WEIGHTS["academic_performance_match"] * candidate.get("academic_performance_match", 0)
        )
        candidate["total_match"] = total_match
    
    # 按总得分降序排序候选池
    sorted_candidates = sorted(candidate_pool, key=lambda x: x.get("total_match", 0), reverse=True)
    
    # 取前N个候选项目
    top_candidates = sorted_candidates[:FINAL_RECOMMENDATIONS]
    
    # 构建推荐项目列表
    recommendations = []
    for i, candidate in enumerate(top_candidates):
        # 获取相关信息
        school_info = {
            "id": candidate.get("school_id"),
            "name": candidate.get("school_name"),
            "tier": candidate.get("school_tier")
        }
        
        program_info = {
            "id": candidate.get("id"),
            "name": candidate.get("program_name_cn"),
            "discipline": candidate.get("program_direction")
        }
        
        scores = {
            "school_tier_match": candidate.get("school_tier_match", 0),
            "program_direction_match": candidate.get("program_direction_match", 0),
            "experience_match": candidate.get("experience_match", 0),
            "academic_performance_match": candidate.get("academic_performance_match", 0),
            "total_match": candidate.get("total_match", 0)
        }
        
        # 获取匹配的案例数量
        matching_cases = candidate.get("matching_cases", [])
        matching_cases_count = len(matching_cases) if matching_cases else 0
        
        # 生成推荐理由
        recommendation_reason = generate_recommendation_reason(
            user_profile.dict(),
            school_info,
            program_info,
            scores,
            similar_cases
        )
        
        # 创建推荐项目
        recommendation = RecommendationItem(
            rank=i + 1,
            school_id=candidate.get("school_id"),
            school_name=candidate.get("school_name"),
            school_tier=candidate.get("school_tier"),
            region_id=candidate.get("region_id"),
            region_name=candidate.get("region_name"),
            program_id=candidate.get("id"),
            program_name_cn=candidate.get("program_name_cn"),
            program_name_en=candidate.get("program_name_en"),
            program_direction=candidate.get("program_direction"),
            scores=RecommendationScore(**scores),
            recommendation_reason=recommendation_reason,
            matching_cases_count=matching_cases_count
        )
        
        recommendations.append(recommendation)
    
    # 构建用户画像摘要
    user_profile_summary = {
        "undergraduate_school": user_profile.undergraduate_school,
        "undergraduate_school_tier": user_profile.undergraduate_school_tier,
        "undergraduate_major": user_profile.undergraduate_major,
        "gpa": user_profile.gpa,
        "target_major_direction": user_profile.target_major_direction,
        "academic_potential": user_profile.academic_potential,
        "extracted_skills": user_profile.extracted_skills
    }
    
    # 构建元数据
    metadata = {
        "timestamp": time.time(),
        "candidate_pool_size": len(candidate_pool),
        "similar_cases_count": len(similar_cases)
    }
    
    # 创建推荐响应
    recommendation_response = RecommendationResponse(
        recommendations=recommendations,
        user_profile_summary=user_profile_summary,
        metadata=metadata
    )
    
    return recommendation_response 