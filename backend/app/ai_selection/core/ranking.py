from typing import List, Dict, Any
import time
import asyncio

from app.ai_selection.schemas.user import EnhancedUserProfile
from app.ai_selection.schemas.recommendation import RecommendationItem, RecommendationScore, RecommendationResponse
from app.ai_selection.utils.llm import generate_recommendation_reason
from app.ai_selection.config import WEIGHTS, FINAL_RECOMMENDATIONS

async def rank_and_generate_recommendations(
    candidate_pool: List[Dict[str, Any]],
    user_profile: EnhancedUserProfile,
    similar_cases: List[Dict[str, Any]]
) -> RecommendationResponse:
    """
    对候选池进行排序，生成最终推荐结果 - 异步并发优化版本
    
    Args:
        candidate_pool: 带有评分的候选池
        user_profile: 用户画像
        similar_cases: 相似案例列表
        
    Returns:
        推荐结果响应
    """
    print(f"开始排序和生成推荐结果，候选池大小: {len(candidate_pool)}")
    
    # 计算每个候选项目的总得分
    for candidate in candidate_pool:
        total_match = (
            WEIGHTS["school_tier_match"] * candidate.get("school_tier_match", 0) +
            WEIGHTS["program_direction_match"] * candidate.get("program_direction_match", 0) +
            WEIGHTS["experience_match"] * candidate.get("experience_match", 0) +
            WEIGHTS["academic_performance_match"] * candidate.get("academic_performance_match", 0)
        )
        candidate["total_match"] = total_match
    
    # 按总得分降序排序候选池
    sorted_candidates = sorted(candidate_pool, key=lambda x: x.get("total_match", 0), reverse=True)
    
    # 取前N个候选项目
    top_candidates = sorted_candidates[:FINAL_RECOMMENDATIONS]
    
    print(f"选择前 {len(top_candidates)} 个候选项目，开始并发生成推荐理由...")
    
    # 构建推荐项目列表 - 异步并发生成推荐理由
    recommendation_tasks = []
    for i, candidate in enumerate(top_candidates):
        task = _generate_single_recommendation(
            i, candidate, user_profile, similar_cases
        )
        recommendation_tasks.append(task)
    
    # 并发执行所有推荐理由生成任务
    recommendations = await asyncio.gather(*recommendation_tasks, return_exceptions=True)
    
    # 处理可能的异常
    final_recommendations = []
    for i, recommendation in enumerate(recommendations):
        if isinstance(recommendation, Exception):
            print(f"生成推荐 {i} 时出错: {recommendation}")
            # 创建带有默认推荐理由的推荐项目
            candidate = top_candidates[i]
            default_recommendation = _create_default_recommendation(i, candidate)
            final_recommendations.append(default_recommendation)
        else:
            final_recommendations.append(recommendation)
    
    print(f"推荐理由生成完成，成功生成 {len(final_recommendations)} 个推荐")
    
    # 构建用户画像摘要
    user_profile_summary = {
        "undergraduate_school": user_profile.undergraduate_school,
        "undergraduate_school_tier": user_profile.undergraduate_school_tier,
        "undergraduate_major": user_profile.undergraduate_major,
        "gpa": user_profile.gpa,
        "target_major_direction": user_profile.target_major_direction,
        "academic_potential": user_profile.academic_potential,
        "extracted_skills": user_profile.extracted_skills
    }
    
    # 构建元数据
    metadata = {
        "timestamp": time.time(),
        "candidate_pool_size": len(candidate_pool),
        "similar_cases_count": len(similar_cases)
    }
    
    # 创建推荐响应
    recommendation_response = RecommendationResponse(
        recommendations=final_recommendations,
        user_profile_summary=user_profile_summary,
        metadata=metadata
    )
    
    return recommendation_response

async def _generate_single_recommendation(
    index: int,
    candidate: Dict[str, Any],
    user_profile: EnhancedUserProfile,
    similar_cases: List[Dict[str, Any]]
) -> RecommendationItem:
    """
    异步生成单个推荐项目
    
    Args:
        index: 排名索引
        candidate: 候选专业
        user_profile: 用户画像
        similar_cases: 相似案例列表
        
    Returns:
        推荐项目
    """
    try:
        # 获取相关信息
        school_info = {
            "name": candidate.get("school_name_cn") or candidate.get("school_name"),
            "name_cn": candidate.get("school_name_cn"),
            "name_en": candidate.get("school_name_en"),
            "qs_rank": candidate.get("school_qs_rank"),
            "tier": candidate.get("school_tier")
        }
        
        program_info = {
            "id": candidate.get("id"),
            "name": candidate.get("program_name_cn"),
            "name_en": candidate.get("program_name_en"),
            "discipline": candidate.get("program_direction")
        }
        
        scores = {
            "school_tier_match": candidate.get("school_tier_match", 0),
            "program_direction_match": candidate.get("program_direction_match", 0),
            "experience_match": candidate.get("experience_match", 0),
            "academic_performance_match": candidate.get("academic_performance_match", 0),
            "total_match": candidate.get("total_match", 0)
        }
        
        # 获取匹配的案例数量
        matching_cases = candidate.get("matching_cases", [])
        matching_cases_count = len(matching_cases) if matching_cases else 0
        
        # 异步生成推荐理由
        recommendation_reason = generate_recommendation_reason(
            user_profile.dict(),
            school_info,
            program_info,
            scores,
            similar_cases
        )
        
        # 创建推荐项目
        recommendation = RecommendationItem(
            rank=index + 1,
            school_name=school_info["name"],
            school_tier=candidate.get("school_tier"),
            region_name=candidate.get("school_region") or candidate.get("region_name"),
            program_id=candidate.get("id"),
            program_name_cn=candidate.get("program_name_cn"),
            program_name_en=candidate.get("program_name_en"),
            program_direction=candidate.get("program_direction"),
            scores=RecommendationScore(**scores),
            recommendation_reason=recommendation_reason,
            matching_cases_count=matching_cases_count
        )
        
        return recommendation
        
    except Exception as e:
        print(f"生成推荐项目 {index} 时出错: {e}")
        # 返回默认推荐项目
        return _create_default_recommendation(index, candidate)

def _create_default_recommendation(
    index: int,
    candidate: Dict[str, Any]
) -> RecommendationItem:
    """
    创建默认推荐项目（当生成失败时使用）
    """
    scores = {
        "school_tier_match": candidate.get("school_tier_match", 0),
        "program_direction_match": candidate.get("program_direction_match", 0),
        "experience_match": candidate.get("experience_match", 0),
        "academic_performance_match": candidate.get("academic_performance_match", 0),
        "total_match": candidate.get("total_match", 0)
    }
    
    # 生成简单的推荐理由
    school_name = candidate.get("school_name_cn") or candidate.get("school_name")
    program_name = candidate.get("program_name_cn")
    default_reason = f"推荐{school_name}的{program_name}专业。该专业与您的背景有一定匹配度，建议进一步了解。"
    
    return RecommendationItem(
        rank=index + 1,
        school_name=school_name,
        school_tier=candidate.get("school_tier", "tier3"),
        region_name=candidate.get("school_region") or candidate.get("region_name", "未知"),
        program_id=candidate.get("id", 0),
        program_name_cn=candidate.get("program_name_cn", ""),
        program_name_en=candidate.get("program_name_en", ""),
        program_direction=candidate.get("program_direction", ""),
        scores=RecommendationScore(**scores),
        recommendation_reason=default_reason,
        matching_cases_count=0
    ) 