from typing import List, Dict, Any, <PERSON><PERSON>
import json
import numpy as np
import asyncio
from sklearn.metrics.pairwise import cosine_similarity
from sqlalchemy import select, and_

from app.ai_selection.schemas.user import EnhancedUserProfile
from app.ai_selection.utils.rag import match_program_with_experience, embed_text
from app.ai_selection.utils.llm import process_text_with_api
from app.db.database import get_db
from app.ai_selection.db.models import (
    AISelectionProgram as Program, 
    AISelectionCase as Case
)

async def match_programs_with_user_background(
    candidate_pool: List[Dict[str, Any]],
    user_profile: EnhancedUserProfile,
    cases: List[Dict[str, Any]]
) -> List[Dict[str, Any]]:
    """
    评估专业与用户背景的匹配度 - 异步并发优化版本
    
    Args:
        candidate_pool: 候选池
        user_profile: 用户画像
        cases: 相关案例列表
        
    Returns:
        更新后的候选池，包含专业匹配分数
    """
    # 如果候选池为空，直接返回
    if not candidate_pool:
        return candidate_pool
    
    print(f"开始并发处理 {len(candidate_pool)} 个候选专业...")
    
    # 创建所有异步任务
    tasks = []
    
    # 为每个候选专业创建匹配任务
    for i, candidate in enumerate(candidate_pool):
        task = _process_single_candidate(
            candidate, user_profile, cases, i
        )
        tasks.append(task)
    
    # 并发执行所有任务
    print("执行并发匹配...")
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # 处理结果，更新候选池
    updated_candidates = []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            print(f"候选专业 {i} 处理失败: {result}")
            # 如果某个专业处理失败，使用默认值
            candidate = candidate_pool[i]
            candidate.update({
                "program_direction_match": 0.5,
                "experience_match": 0.5,
                "experience_match_reason": "处理过程中出现错误，使用默认评分",
                "academic_performance_match": 0.5
            })
            updated_candidates.append(candidate)
        else:
            updated_candidates.append(result)
    
    print(f"并发处理完成，成功处理 {len(updated_candidates)} 个候选专业")
    return updated_candidates

async def _process_single_candidate(
    candidate: Dict[str, Any],
    user_profile: EnhancedUserProfile,
    cases: List[Dict[str, Any]],
    index: int
) -> Dict[str, Any]:
    """
    处理单个候选专业的所有匹配评估 - 异步版本
    
    Args:
        candidate: 候选专业
        user_profile: 用户画像
        cases: 案例列表
        index: 候选专业索引（用于调试）
        
    Returns:
        更新后的候选专业信息
    """
    try:
        # 创建所有匹配任务
        direction_task = _calculate_program_direction_match(candidate, user_profile)
        
        experience_task = _calculate_experience_match_async(
            candidate, user_profile, cases
        )
        
        academic_task = _calculate_academic_performance_match_async(
            candidate, user_profile, cases
        )
        
        # 并发执行所有匹配评估
        direction_match, (experience_match, experience_reason), academic_match = await asyncio.gather(
            direction_task,
            experience_task, 
            academic_task
        )
        
        # 更新候选专业信息
        candidate.update({
            "program_direction_match": direction_match,
            "experience_match": experience_match,
            "experience_match_reason": experience_reason,
            "academic_performance_match": academic_match
        })
        
        return candidate
        
    except Exception as e:
        print(f"处理候选专业 {index} 时出错: {e}")
        # 返回带有默认值的候选专业
        candidate.update({
            "program_direction_match": 0.5,
            "experience_match": 0.5,
            "experience_match_reason": f"处理出错: {str(e)}",
            "academic_performance_match": 0.5
        })
        return candidate

async def _calculate_experience_match_async(
    candidate: Dict[str, Any],
    user_profile: EnhancedUserProfile,
    cases: List[Dict[str, Any]]
) -> Tuple[float, str]:
    """
    异步版本的经历匹配计算
    """
    if user_profile.key_experiences:
        # 使用现有的经历匹配函数（它内部会处理异步API调用）
        experience_match, match_reason = match_program_with_experience(
            candidate,
            user_profile.key_experiences,
            cases
        )
        return experience_match, match_reason
    else:
        return 0.5, "未提供经历信息，无法进行详细匹配分析。"

async def _calculate_academic_performance_match_async(
    candidate: Dict[str, Any],
    user_profile: EnhancedUserProfile,
    cases: List[Dict[str, Any]]
) -> float:
    """
    异步版本的学术表现匹配计算
    """
    # 这个函数主要是计算逻辑，不涉及API调用，所以可以直接调用同步版本
    return _calculate_academic_performance_match(candidate, user_profile, cases)

async def _calculate_program_direction_match(
    program_info: Dict[str, Any],
    user_profile: EnhancedUserProfile
) -> float:
    """
    计算专业方向与用户目标的匹配度
    
    Args:
        program_info: 专业信息
        user_profile: 用户画像
        
    Returns:
        专业方向匹配分数
    """
    # 使用LLM评估专业方向与用户目标的匹配度
    prompt = f"""
    请你作为留学选校专家，评估学生的目标专业方向与某个具体专业项目的匹配程度。
    
    学生信息:
    本科专业: {user_profile.undergraduate_major}
    目标专业方向: {user_profile.target_major_direction}
    推断的兴趣领域: {user_profile.inferred_interest_domain or "未知"}
    
    专业项目信息:
    专业名称: {program_info.get('program_name_cn', '')}
    英文名称: {program_info.get('program_name_en', '')}
    学科分类: {program_info.get('program_direction', '')}
    专业描述: {program_info.get('program_objectives', '')}
    
    请评估两者的匹配程度，给出一个0到1之间的分数，其中0表示完全不匹配，1表示完全匹配。
    只需返回一个数字，不要有其他任何文字。
    """
    
    try:
        # 调用LLM API
        response = process_text_with_api(prompt)
        
        # 解析返回的分数
        try:
            score = float(response.strip())
            # 确保分数在0-1范围内
            return max(0.0, min(1.0, score))
        except ValueError:
            # 如果无法解析为浮点数，使用备选方法
            print(f"无法解析LLM返回的匹配分数: {response}")
            return await _fallback_program_direction_match(program_info, user_profile)
    
    except Exception as e:
        # 如果API调用失败，使用备选方法
        print(f"专业方向匹配评估API调用失败: {e}")
        return await _fallback_program_direction_match(program_info, user_profile)

async def _fallback_program_direction_match(
    program_info: Dict[str, Any],
    user_profile: EnhancedUserProfile
) -> float:
    """备选的专业方向匹配度计算方法"""
    # 使用关键词匹配
    return _keyword_based_match(program_info, user_profile)

def _keyword_based_match(
    program_info: Dict[str, Any],
    user_profile: EnhancedUserProfile
) -> float:
    """关键词匹配的备选方法"""
    target_direction = user_profile.target_major_direction.lower()
    program_name = program_info.get("program_name_cn", "").lower()
    program_name_en = program_info.get("program_name_en", "").lower()
    program_discipline = program_info.get("program_direction", "").lower()
    program_description = program_info.get("program_objectives", "").lower()
    
    # 专业名称精确匹配
    if target_direction in program_name or target_direction in program_name_en:
        return 1.0
    
    # 学科分类匹配
    if target_direction in program_discipline:
        return 0.9
    
    # 关键词匹配
    # 将目标方向分解为关键词
    keywords = target_direction.split()
    matched_keywords = sum(1 for kw in keywords if kw in program_name 
                         or kw in program_name_en 
                         or kw in program_discipline 
                         or kw in program_description)
    
    if matched_keywords > 0:
        return min(0.8, 0.4 + matched_keywords * 0.2)  # 最高0.8
    
    # 学科相关性
    # 这里需要定义不同学科之间的相关性
    major_related_disciplines = {
        "计算机": ["计算机科学", "软件工程", "人工智能", "数据科学"],
        "电子": ["电子工程", "电气工程", "通信工程"],
        "数学": ["数学", "统计学", "应用数学"],
        "商业": ["商业", "管理", "经济", "金融", "会计"]
    }
    
    # 提取用户兴趣领域
    user_domain = None
    for domain, disciplines in major_related_disciplines.items():
        if any(d.lower() in target_direction for d in disciplines):
            user_domain = domain
            break
    
    # 提取专业所属领域
    program_domain = None
    for domain, disciplines in major_related_disciplines.items():
        if any(d.lower() in program_discipline for d in disciplines):
            program_domain = domain
            break
    
    # 如果两者属于同一领域
    if user_domain and program_domain and user_domain == program_domain:
        return 0.7
    
    # 默认返回一个较低的匹配度
    return 0.3

def _calculate_academic_performance_match(
    program_info: Dict[str, Any],
    user_profile: EnhancedUserProfile,
    cases: List[Dict[str, Any]]
) -> float:
    """
    评估用户学术表现与该专业的匹配度
    
    Args:
        program_info: 专业信息
        user_profile: 用户画像
        cases: 案例列表
        
    Returns:
        学术表现匹配分数
    """
    # 获取用户GPA
    user_gpa = user_profile.gpa
    
    # 获取专业的GPA要求
    program_gpa_req = program_info.get("gpa_requirements", 0)
    
    # 如果没有明确的GPA要求，查看成功案例的平均GPA
    if program_gpa_req == 0 and cases:
        program_id = program_info.get("id")
        matching_cases = [case for case in cases if case.get("offer_program_id") == program_id]  # 更新字段名
        
        if matching_cases:
            avg_gpa = sum(case.get("gpa", 0) for case in matching_cases) / len(matching_cases)
            program_gpa_req = avg_gpa
        else:
            # 如果没有匹配案例，使用默认值
            program_gpa_req = 80  # 假设80分是一个常见的基准线
    
    # 计算GPA匹配度
    if user_gpa >= program_gpa_req * 1.1:  # 显著高于要求
        gpa_match = 1.0
    elif user_gpa >= program_gpa_req:  # 达到或略高于要求
        gpa_match = 0.8
    elif user_gpa >= program_gpa_req * 0.95:  # 略低于要求
        gpa_match = 0.6
    elif user_gpa >= program_gpa_req * 0.9:  # 显著低于要求
        gpa_match = 0.4
    else:  # 远低于要求
        gpa_match = 0.2
    
    # 考虑用户学术潜力
    academic_potential = user_profile.academic_potential
    potential_bonus = 0.0
    
    if academic_potential == "高":
        potential_bonus = 0.2
    elif academic_potential == "中等偏上":
        potential_bonus = 0.1
    
    # 计算最终学术匹配分数
    academic_match = min(1.0, gpa_match + potential_bonus)
    
    return academic_match 