from typing import List, Dict, Any, <PERSON><PERSON>
import json
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
from sqlalchemy import select, and_

from app.db.database import get_db
from app.ai_selection.db.models import (
    AISelectionProgram as Program, 
    AISelectionCase as Case
)
from app.ai_selection.schemas.user import EnhancedUserProfile
from app.ai_selection.utils.rag import embed_text
from app.ai_selection.config import GPA_TOLERANCE, MIN_CASE_THRESHOLD

async def identify_reachable_schools(user_profile: EnhancedUserProfile) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
    """
    识别用户可达的院校圈
    
    Args:
        user_profile: 增强用户画像
        
    Returns:
        可达院校列表和支持案例列表的元组
    """
    async for session in get_db():
        # 提取核心用户背景
        tier = user_profile.undergraduate_school_tier
        gpa = user_profile.gpa
        major = user_profile.undergraduate_major
        
        # 基于院校层级的硬匹配查询 - 更新字段名称
        query1 = (
            select(
                Case.id,
                Case.student_name,
                Case.undergraduate_school,
                Case.undergraduate_school_tier,
                Case.undergraduate_major,
                Case.gpa,
                Case.offer_region,  # target_region -> offer_region
                Case.offer_degree,  # target_degree -> offer_degree
                Case.offer_major_direction,  # target_major_direction -> offer_major_direction
                Case.key_experiences,
                Case.language_score,
                Case.offer_program_id,  # target_program_id -> offer_program_id
                Program.school_name_cn.label("offer_school_name"),  # 从program表获取学校信息
                Program.school_qs_rank.label("offer_school_qs_rank"),
                Program.program_name_cn.label("offer_program_name")
            )
            .join(Program, Case.offer_program_id == Program.id)  # 直接join program表
            .where(
                and_(
                    Case.undergraduate_school_tier == tier,
                    Case.gpa.between(gpa * (1 - GPA_TOLERANCE), gpa * (1 + GPA_TOLERANCE))
                )
            )
        )
        
        result1 = await session.execute(query1)
        rows1 = result1.fetchall()
        
        # 基于具体本科院校的硬匹配查询（如果适用）
        query2 = (
            select(
                Case.id,
                Case.student_name,
                Case.undergraduate_school,
                Case.undergraduate_school_tier,
                Case.undergraduate_major,
                Case.gpa,
                Case.offer_region,
                Case.offer_degree,
                Case.offer_major_direction,
                Case.key_experiences,
                Case.language_score,
                Case.offer_program_id,
                Program.school_name_cn.label("offer_school_name"),
                Program.school_qs_rank.label("offer_school_qs_rank"),
                Program.program_name_cn.label("offer_program_name")
            )
            .join(Program, Case.offer_program_id == Program.id)
            .where(
                and_(
                    Case.undergraduate_school == user_profile.undergraduate_school,
                    Case.gpa.between(gpa * (1 - GPA_TOLERANCE), gpa * (1 + GPA_TOLERANCE))
                )
            )
        )
        
        result2 = await session.execute(query2)
        rows2 = result2.fetchall()
        
        # 合并查询结果
        cases_hard_match = []
        for row in list(rows1) + list(rows2):
            case_dict = {
                "id": row.id,
                "student_name": row.student_name,
                "undergraduate_school": row.undergraduate_school,
                "undergraduate_school_tier": row.undergraduate_school_tier,
                "undergraduate_major": row.undergraduate_major,
                "gpa": row.gpa,
                "offer_region": row.offer_region,  # 更新字段名
                "offer_degree": row.offer_degree,  # 更新字段名
                "offer_major_direction": row.offer_major_direction,  # 更新字段名
                "key_experiences": row.key_experiences,
                "language_score": json.loads(row.language_score) if isinstance(row.language_score, str) else row.language_score,
                "offer_program_id": row.offer_program_id,  # 更新字段名
                "offer_school_name": row.offer_school_name,  # 从program表获取
                "offer_school_qs_rank": row.offer_school_qs_rank,  # 从program表获取
                "offer_program_name": row.offer_program_name
            }
            cases_hard_match.append(case_dict)
        
        # 如果硬匹配结果太少，使用向量检索补充（简化版本）
        if len(cases_hard_match) < MIN_CASE_THRESHOLD:
            # 这里可以添加向量检索逻辑，暂时跳过复杂实现
            pass
        
        # 统计各学校出现频次 - 基于学校名称而不是ID
        school_counts = {}
        for case in cases_hard_match:
            school_name = case["offer_school_name"]
            school_qs_rank = case["offer_school_qs_rank"]
            
            if school_name not in school_counts:
                school_counts[school_name] = {
                    "name": school_name,
                    "qs_rank": school_qs_rank,
                    "tier": _infer_school_tier_from_qs_rank(school_qs_rank),
                    "count": 0,
                    "cases": []
                }
            
            school_counts[school_name]["count"] += 1
            school_counts[school_name]["cases"].append(case["id"])
        
        # 转换为列表并按频次排序
        reachable_schools = list(school_counts.values())
        reachable_schools.sort(key=lambda x: x["count"], reverse=True)
        
        return reachable_schools, cases_hard_match

async def calculate_school_match_scores(
    candidate_pool: List[Dict[str, Any]], 
    reachable_schools: List[Dict[str, Any]]
) -> List[Dict[str, Any]]:
    """
    计算每个候选学校的院校层级匹配分数
    
    Args:
        candidate_pool: 候选池
        reachable_schools: 可达院校列表
        
    Returns:
        带有匹配分数的候选池
    """
    # 创建学校名称到可达性的映射
    reachable_map = {school["name"]: school for school in reachable_schools}
    
    # 为候选池中的每个项目计算匹配分数
    for candidate in candidate_pool:
        school_name = candidate.get("school_name_cn") or candidate.get("school_name")
        
        # 如果学校在可达学校列表中，计算匹配分数
        if school_name in reachable_map:
            reachable_info = reachable_map[school_name]
            # 基于案例数量计算匹配分数
            count = reachable_info["count"]
            # 一个简单的算法：案例越多，分数越高，最高为1.0
            school_tier_match = min(1.0, count / 5)  # 假设5个案例是满分
            candidate["school_tier_match"] = school_tier_match
            candidate["matching_cases"] = reachable_info["cases"]
        else:
            # 如果学校不在可达列表中，根据层级判断
            # 这里需要一些启发式规则，例如：
            # - tier1学生可以申请tier2和tier3学校
            # - tier2学生可以申请tier3学校
            # - tier3学生难以申请tier1和tier2学校
            candidate_tier = candidate["school_tier"]
            
            # 找出可达学校的主要层级
            if reachable_schools:
                main_tier = reachable_schools[0]["tier"]
                
                if main_tier == "tier1":
                    if candidate_tier == "tier1":
                        candidate["school_tier_match"] = 0.8  # 可达学校主要是tier1，申请tier1有一定难度
                    elif candidate_tier == "tier2":
                        candidate["school_tier_match"] = 0.9  # 可达学校主要是tier1，申请tier2较容易
                    else:  # tier3
                        candidate["school_tier_match"] = 1.0  # 可达学校主要是tier1，申请tier3很容易
                elif main_tier == "tier2":
                    if candidate_tier == "tier1":
                        candidate["school_tier_match"] = 0.5  # 可达学校主要是tier2，申请tier1有较大难度
                    elif candidate_tier == "tier2":
                        candidate["school_tier_match"] = 0.8  # 可达学校主要是tier2，申请tier2有一定难度
                    else:  # tier3
                        candidate["school_tier_match"] = 0.9  # 可达学校主要是tier2，申请tier3较容易
                else:  # tier3
                    if candidate_tier == "tier1":
                        candidate["school_tier_match"] = 0.3  # 可达学校主要是tier3，申请tier1难度很大
                    elif candidate_tier == "tier2":
                        candidate["school_tier_match"] = 0.5  # 可达学校主要是tier3，申请tier2有较大难度
                    else:  # tier3
                        candidate["school_tier_match"] = 0.7  # 可达学校主要是tier3，申请tier3有一定难度
            else:
                # 如果没有可达学校信息，给一个默认分数
                candidate["school_tier_match"] = 0.5
            
            candidate["matching_cases"] = []
    
    return candidate_pool

def _infer_school_tier_from_qs_rank(qs_rank: str) -> str:
    """
    根据QS排名推断学校层级
    
    Args:
        qs_rank: QS排名字符串
        
    Returns:
        学校层级 (tier1, tier2, tier3)
    """
    if not qs_rank:
        return "tier3"  # 默认层级
    
    try:
        rank = int(qs_rank)
        if rank <= 50:
            return "tier1"
        elif rank <= 200:
            return "tier2"
        else:
            return "tier3"
    except (ValueError, TypeError):
        return "tier3"  # 如果无法解析排名，返回默认层级 