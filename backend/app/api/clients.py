from fastapi import APIRouter, Depends, HTTPException, status, File, UploadFile, BackgroundTasks, Path, Query
from fastapi.responses import StreamingResponse
from sqlalchemy.future import select
from sqlalchemy import update, delete
from typing import List, Dict, Any, Optional
import os
import requests
import json
import logging
from pydantic import BaseModel, Field, validator
import json_repair

from app.core.dependencies import CurrentUser, DBSession
from app.models.client import (
    Client, Education, Academic, Work, Activity, Award, Skill, LanguageScore, Thought,
    BackgroundCustomModule, ThoughtCustomModule
)
from app.schemas.client import (
    ClientCreate, ClientUpdate, ClientResponse, ClientDetailResponse,
    EducationCreate, EducationUpdate, EducationResponse,
    AcademicCreate, AcademicUpdate, AcademicResponse,
    WorkCreate, WorkUpdate, WorkResponse,
    ActivityCreate, ActivityUpdate, ActivityResponse,
    AwardCreate, AwardUpdate, AwardResponse,
    SkillCreate, SkillUpdate, SkillResponse,
    LanguageScoreCreate, LanguageScoreUpdate, LanguageScoreResponse,
    ThoughtCreate, ThoughtUpdate, ThoughtResponse,
    CustomModuleCreate, CustomModuleUpdate, CustomModuleResponse
)

# 创建模块级别的 logger
logger = logging.getLogger("api")

# 创建路由器
router = APIRouter(prefix="/clients", tags=["客户"])

# Dify API 配置
API_KEY = os.getenv("DIFY_API_KEY", "app-TceoIfQZMejrh11iyybg0qvc")
DIFY_BASE = "https://api.dify.ai/v1"

def upload_to_dify(file: UploadFile, user: Optional[str] = None):
    """共用的上传逻辑，返回 Dify 返回的 file_id"""
    files = {'file': (file.filename, file.file, file.content_type)}
    data = {'user': user} if user else {}
    resp = requests.post(
        f"{DIFY_BASE}/files/upload",
        headers={"Authorization": f"Bearer {API_KEY}"},
        files=files,
        data=data
    )
    if resp.status_code != 201:
        try:
            detail = resp.json()
        except requests.exceptions.JSONDecodeError:
            detail = resp.text
        raise RuntimeError(f"Dify Upload failed: {resp.status_code} - {detail}")
    return resp.json()["id"], resp.json()["created_by"], resp.json()["extension"]

@router.post("/files/upload", status_code=status.HTTP_201_CREATED)
async def upload_file(
    db: DBSession,
    file: UploadFile = File(...),
    user: Optional[str] = None,
    # current_user: CurrentUser = None,
):
    """
    上传文件到Dify

    Args:
        file: 上传的文件
        user: 用户标识
        current_user: 当前用户
        db: 数据库会话

    Returns:
        Dict: 包含文件ID和创建者信息
    """
    if not file:
        raise HTTPException(status_code=400, detail="缺少 'file' 字段")

    try:
        file_id, created_by, extension = upload_to_dify(file, user)
    except RuntimeError as e:
        raise HTTPException(status_code=502, detail=f"与 Dify 通信失败: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

    return {"upload_file_id": file_id, "created_by": created_by, "extension": extension}

def get_file_type(extension: str) -> str:
    """
    根据文件扩展名确定文件类型

    Args:
        extension: 文件扩展名（不包含点号）

    Returns:
        str: 文件类型 ('document', 'image', 'audio', 'video', 'custom')
    """
    extension = extension.upper()

    # 文档类型
    document_types = {'TXT', 'MD', 'MARKDOWN', 'PDF', 'HTML', 'XLSX', 'XLS',
                     'DOCX', 'CSV', 'EML', 'MSG', 'PPTX', 'PPT', 'XML', 'EPUB'}
    # 图片类型
    image_types = {'JPG', 'JPEG', 'PNG', 'GIF', 'WEBP', 'SVG'}
    # 音频类型
    audio_types = {'MP3', 'M4A', 'WAV', 'WEBM', 'AMR'}
    # 视频类型
    video_types = {'MP4', 'MOV', 'MPEG', 'MPGA'}

    if extension in document_types:
        return 'document'
    elif extension in image_types:
        return 'image'
    elif extension in audio_types:
        return 'audio'
    elif extension in video_types:
        return 'video'
    else:
        return 'custom'

class WorkflowRequest(BaseModel):
    upload_file_ids: List[str]
    user: str
    extensions: List[str]  # 文件扩展名列表，与 upload_file_ids 一一对应

    @validator('extensions')
    def validate_extensions(cls, v, values):
        if 'upload_file_ids' in values and len(v) != len(values['upload_file_ids']):
            raise ValueError('extensions 列表长度必须与 upload_file_ids 列表长度相同')
        return v

@router.post("/workflows/run", status_code=status.HTTP_200_OK)
async def run_workflow(
    db: DBSession,
    workflow_request: WorkflowRequest,
    # current_user: CurrentUser = None,
):
    """
    运行工作流处理上传的文件，并可选择将提取的信息保存到数据库

    Args:
        workflow_request: 工作流请求数据，包含文件ID列表和对应的扩展名列表
        current_user: 当前用户
        db: 数据库会话

    Returns:
        Dict: 工作流处理结果，如果选择保存到数据库，则包含创建的客户ID
    """
    upload_file_ids = workflow_request.upload_file_ids
    user = workflow_request.user
    extensions = workflow_request.extensions

    if not upload_file_ids:
        raise HTTPException(status_code=400, detail="必须提供 'upload_file_ids' 列表")

    raw_doc_list = []
    for file_id, extension in zip(upload_file_ids, extensions):
        file_type = get_file_type(extension)
        raw_doc_list.append({
            "transfer_method": "local_file",
            "upload_file_id": file_id,
            "type": file_type
        })

    body = {
        "inputs": {
            "raw_doc": raw_doc_list
        },
        "response_mode": "streaming",
        "user": user
    }

    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }

    try:
        resp = requests.post(
            f"{DIFY_BASE}/workflows/run",
            headers=headers,
            json=body,
        )
        resp.raise_for_status()

        logger.info("开始接收流式响应...")

        for line in resp.iter_lines():
            if line:
                decoded_line = line.decode('utf-8')
                if decoded_line.startswith('data: '):
                    json_str = decoded_line.split(": ", 1)[1]
                    try:
                        dify_response = json.loads(json_str)
                        if dify_response.get("event") == "workflow_finished":
                            logger.info("建档工作流结束...")

                            # 解析Dify返回的结果
                            if not dify_response.get("data", {}).get("outputs", {}).get("text"):
                                raise HTTPException(
                                    status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                                    detail="Dify工作流未返回有效的学生信息"
                                )
                            try:
                                # 使用json_repair.loads解析JSON（因为JSON的格式可能invalid），并处理可能的错误
                                parsed_data = json_repair.loads(dify_response["data"]["outputs"]["text"])
                                logger.info(f"成功解析JSON数据，数据类型: {type(parsed_data)}")

                                # 根据数据类型进行处理
                                if isinstance(parsed_data, dict):
                                    # 如果是字典类型，直接使用
                                    client_data = parsed_data
                                    logger.info("解析结果为字典类型，直接使用")
                                elif isinstance(parsed_data, list):
                                    # 如果是列表类型，检查是否为空
                                    if not parsed_data:
                                        raise ValueError("解析结果为空列表")

                                    # 取第一个元素
                                    client_data = parsed_data[1]
                                    logger.info(f"解析结果为列表类型，使用第一个元素")

                                    # 确保取出的元素是字典类型
                                    if not isinstance(client_data, dict):
                                        raise ValueError(f"列表中的第一个元素不是字典类型: {type(client_data)}")
                                else:
                                    # 其他类型，抛出异常
                                    raise ValueError(f"解析结果类型不支持: {type(parsed_data)}")

                            except Exception as e:
                                logger.error(f"JSON解析或处理失败: {e}")
                                raise HTTPException(
                                    status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                                    detail=f"无法解析或处理Dify返回的学生信息: {str(e)}"
                                )
                    except Exception as e:
                        print(f"处理Dify响应时发生错误: {e}")
                        raise HTTPException(status_code=500, detail=f"处理Dify响应时发生错误: {e}")

        # 创建客户基本信息
        client_base_data = {
            "name": client_data.get("name", "未命名客户"),
            "gender": client_data.get("gender"),
            "phone": client_data.get("phone"),
            "email": client_data.get("email"),
            "location": client_data.get("location"),
            "address": client_data.get("address"),
            "id_card": client_data.get("id_card"),
            "passport": client_data.get("passport"),
            "id_card_issuer": client_data.get("id_card_issuer"),
            "id_card_validity": client_data.get("id_card_validity"),
            "passport_issue_place": client_data.get("passport_issue_place"),
            "passport_issue_date": client_data.get("passport_issue_date"),
            "passport_expiry": client_data.get("passport_expiry"),
            "service_type": client_data.get("service_type", "undergraduate"),
            "is_archived": False,  # 确保新创建的客户归档状态为False
        }

        # 创建客户记录
        client = Client(**client_base_data)
        db.add(client)
        await db.commit()
        await db.refresh(client)

        # 保存教育经历
        if education_list := client_data.get("education", []):
            for edu_data in education_list:
                education = Education(
                    client_id=client.id,
                    school=edu_data.get("school", ""),
                    major=edu_data.get("major"),
                    degree=edu_data.get("degree"),
                    gpa=edu_data.get("gpa"),
                    start_date=edu_data.get("start_date"),
                    end_date=edu_data.get("end_date"),
                    description=edu_data.get("description")
                )
                db.add(education)

        # 保存学术经历
        if academic_list := client_data.get("academic", []):
            for acad_data in academic_list:
                academic = Academic(
                    client_id=client.id,
                    title=acad_data.get("title", ""),
                    type=acad_data.get("type"),
                    date=acad_data.get("date"),
                    description=acad_data.get("description")
                )
                db.add(academic)

        # 保存工作经历
        if work_list := client_data.get("work", []):
            for work_data in work_list:
                work = Work(
                    client_id=client.id,
                    company=work_data.get("company", ""),
                    position=work_data.get("position"),
                    start_date=work_data.get("start_date"),
                    end_date=work_data.get("end_date"),
                    description=work_data.get("description")
                )
                db.add(work)

        # 保存活动经历
        if activity_list := client_data.get("activities", []):
            for act_data in activity_list:
                activity = Activity(
                    client_id=client.id,
                    name=act_data.get("name", ""),
                    role=act_data.get("role"),
                    start_date=act_data.get("start_date"),
                    end_date=act_data.get("end_date"),
                    description=act_data.get("description")
                )
                db.add(activity)

        # 保存奖项
        if award_list := client_data.get("awards", []):
            for award_data in award_list:
                award = Award(
                    client_id=client.id,
                    name=award_data.get("name", ""),
                    level=award_data.get("level"),
                    date=award_data.get("date"),
                    description=award_data.get("description")
                )
                db.add(award)

        # 保存技能
        if skill_list := client_data.get("skills", []):
            for skill_data in skill_list:
                skill = Skill(
                    client_id=client.id,
                    type=skill_data.get("type", ""),
                    description=skill_data.get("description")
                )
                db.add(skill)

        # 保存语言成绩
        if language_score_list := client_data.get("language_scores", []):
            for ls_data in language_score_list:
                language_score = LanguageScore(
                    client_id=client.id,
                    type=ls_data.get("type", ""),
                    score=ls_data.get("score", ""),
                    date=ls_data.get("date"),
                    validity=ls_data.get("validity")
                )
                db.add(language_score)

        # 保存个人想法
        if thoughts_list := client_data.get("thoughts", []):
            for thought_data in thoughts_list:
                thought = Thought(
                    client_id=client.id,
                    target_major=thought_data.get("target_major"),
                    personal_understanding=thought_data.get("personal_understanding"),
                    academic_match=thought_data.get("academic_match"),
                    work_match=thought_data.get("work_match"),
                    future_plan=thought_data.get("future_plan")
                )
                db.add(thought)

        # 提交所有更改
        await db.commit()

        # 返回Dify响应和创建的客户哈希ID
        dify_response["client_id"] = client.id_hashed
        return dify_response

    except requests.exceptions.RequestException as e:
        status_code = e.response.status_code if e.response is not None else 503
        detail = f"调用 Dify 工作流失败: {e}"
        if e.response is not None:
            try:
                detail = e.response.json()
            except requests.exceptions.JSONDecodeError:
                detail = e.response.text
            if e.response.status_code >= 400:
                raise HTTPException(status_code=e.response.status_code, detail=detail)
            else:
                raise HTTPException(status_code=status_code, detail=detail)
        else:
            raise HTTPException(status_code=status_code, detail=detail)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


# 客户信息CRUD相关：
@router.get("/", response_model=List[ClientResponse], status_code=status.HTTP_200_OK)
async def get_clients(
    db: DBSession,
    skip: int = Query(0, description="分页起始位置"),
    limit: int = Query(100, description="每页数量"),
    is_archived: Optional[bool] = Query(None, description="是否已归档（服务完成）"),
    search: Optional[str] = Query(None, description="搜索关键词（模糊匹配客户名称）"),
    # current_user: CurrentUser = None,
):
    """
    获取客户列表

    Args:
        skip: 分页起始位置
        limit: 每页数量
        is_archived: 是否已归档（服务完成），None表示不筛选
        search: 搜索关键词，模糊匹配客户名称
        current_user: 当前用户
        db: 数据库会话

    Returns:
        List[ClientResponse]: 客户列表数据
    """
    # 构建查询
    query = select(Client)

    # 根据归档状态筛选
    if is_archived is not None:
        query = query.where(Client.is_archived == is_archived)
        
    # 根据搜索关键词筛选
    if search:
        # 使用like进行模糊匹配，使用%%包裹关键词
        query = query.where(Client.name.ilike(f"%{search}%"))

    # 分页
    query = query.offset(skip).limit(limit)

    # 执行查询
    result = await db.execute(query)
    clients = result.scalars().all()

    return clients

@router.post("/", response_model=ClientResponse, status_code=status.HTTP_201_CREATED)
async def create_client(
    db: DBSession,
    client_data: ClientCreate,
    current_user: CurrentUser = None,
):
    """
    创建新客户

    Args:
        client_data: 客户数据
        current_user: 当前用户
        db: 数据库会话

    Returns:
        ClientResponse: 创建的客户信息
    """
    # 创建新客户
    client_dict = client_data.dict()
    # 确保新客户的归档状态为False
    client_dict["is_archived"] = False
    client = Client(**client_dict)

    # 如果有当前用户，设置为客户的顾问
    if current_user:
        client.user_id = current_user.id

    db.add(client)
    await db.commit()
    await db.refresh(client)

    return client

@router.get("/{client_id}", response_model=ClientDetailResponse, status_code=status.HTTP_200_OK)
async def get_client(
    db: DBSession,
    client_id: str = Path(..., description="客户哈希ID"),
    # current_user: CurrentUser = None,
):
    """
    获取单个客户详情

    Args:
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        ClientDetailResponse: 客户详细信息
    """
    # 查询客户（使用哈希ID）
    query = select(Client).where(Client.id_hashed == client_id)
    result = await db.execute(query)
    client = result.scalars().first()

    # 如果客户不存在，返回404错误
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 创建一个包含客户基本信息的字典
    client_data = {
        # "id": client.id,  # 使用哈希ID代替真实ID
        "id_hashed": client.id_hashed,  # 同时提供哈希ID字段
        "name": client.name,
        "gender": client.gender,
        "phone": client.phone,
        "email": client.email,
        "location": client.location,
        "address": client.address,
        "id_card": client.id_card,
        "passport": client.passport,
        "id_card_issuer": client.id_card_issuer,
        "id_card_validity": client.id_card_validity,
        "passport_issue_place": client.passport_issue_place,
        "passport_issue_date": client.passport_issue_date,
        "passport_expiry": client.passport_expiry,
        "service_type": client.service_type,
        "user_id": client.user_id,
        "is_archived": client.is_archived,
        "created_at": client.created_at.isoformat() if client.created_at else None,
        "updated_at": client.updated_at.isoformat() if client.updated_at else None,
    }

    # 显式加载关联数据
    # 获取客户真实ID用于关联查询
    real_client_id = client.id

    # 教育经历
    education_query = select(Education).where(Education.client_id == real_client_id)
    education_result = await db.execute(education_query)
    education_list = education_result.scalars().all()
    client_data["education"] = [edu.to_dict() for edu in education_list]

    # 学术经历
    academic_query = select(Academic).where(Academic.client_id == real_client_id)
    academic_result = await db.execute(academic_query)
    academic_list = academic_result.scalars().all()
    client_data["academic"] = [acad.to_dict() for acad in academic_list]

    # 工作经历
    work_query = select(Work).where(Work.client_id == real_client_id)
    work_result = await db.execute(work_query)
    work_list = work_result.scalars().all()
    client_data["work"] = [w.to_dict() for w in work_list]

    # 活动经历
    activity_query = select(Activity).where(Activity.client_id == real_client_id)
    activity_result = await db.execute(activity_query)
    activity_list = activity_result.scalars().all()
    client_data["activities"] = [act.to_dict() for act in activity_list]

    # 奖项
    award_query = select(Award).where(Award.client_id == real_client_id)
    award_result = await db.execute(award_query)
    award_list = award_result.scalars().all()
    client_data["awards"] = [awd.to_dict() for awd in award_list]

    # 技能
    skill_query = select(Skill).where(Skill.client_id == real_client_id)
    skill_result = await db.execute(skill_query)
    skill_list = skill_result.scalars().all()
    client_data["skills"] = [skl.to_dict() for skl in skill_list]

    # 语言成绩
    language_score_query = select(LanguageScore).where(LanguageScore.client_id == real_client_id)
    language_score_result = await db.execute(language_score_query)
    language_score_list = language_score_result.scalars().all()
    client_data["language_scores"] = [ls.to_dict() for ls in language_score_list]

    # 个人想法
    thought_query = select(Thought).where(Thought.client_id == real_client_id)
    thought_result = await db.execute(thought_query)
    thought_list = thought_result.scalars().all()
    client_data["thoughts"] = [t.to_dict() for t in thought_list]

    # 背景自定义模块
    background_module_query = select(BackgroundCustomModule).where(BackgroundCustomModule.client_id == real_client_id)
    background_module_result = await db.execute(background_module_query)
    background_module_list = background_module_result.scalars().all()
    client_data["background_modules"] = [bm.to_dict() for bm in background_module_list]

    # 想法自定义模块
    thought_module_query = select(ThoughtCustomModule).where(ThoughtCustomModule.client_id == real_client_id)
    thought_module_result = await db.execute(thought_module_query)
    thought_module_list = thought_module_result.scalars().all()
    client_data["thought_modules"] = [tm.to_dict() for tm in thought_module_list]

    # 使用 Pydantic 模型创建响应
    return ClientDetailResponse(**client_data)

@router.put("/{client_id}", response_model=ClientResponse, status_code=status.HTTP_200_OK)
async def update_client(
    db: DBSession,
    client_data: ClientUpdate,
    client_id: str = Path(..., description="客户哈希ID"),
    # current_user: CurrentUser = None,
):
    """
    更新客户信息

    Args:
        client_data: 客户更新数据
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        ClientResponse: 更新后的客户信息
    """
    # 查询客户（使用哈希ID）
    query = select(Client).where(Client.id_hashed == client_id)
    result = await db.execute(query)
    client = result.scalars().first()

    # 如果客户不存在，返回404错误
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 更新客户信息
    update_data = client_data.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(client, key, value)

    await db.commit()
    await db.refresh(client)

    return client

@router.delete("/{client_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_client(
    db: DBSession,
    client_id: str = Path(..., description="客户哈希ID"),
    # current_user: CurrentUser = None,
):
    """
    删除客户

    Args:
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话
    """
    # 查询客户（使用哈希ID）
    query = select(Client).where(Client.id_hashed == client_id)
    result = await db.execute(query)
    client = result.scalars().first()

    # 如果客户不存在，返回404错误
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 删除客户
    await db.delete(client)
    await db.commit()

    return None

@router.patch("/{client_id}/archive", response_model=ClientResponse, status_code=status.HTTP_200_OK)
async def toggle_client_archive_status(
    db: DBSession,
    client_id: str = Path(..., description="客户哈希ID"),
    # current_user: CurrentUser = None,
):
    """
    切换客户归档状态

    Args:
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        ClientResponse: 更新后的客户信息
    """
    # 查询客户（使用哈希ID）
    query = select(Client).where(Client.id_hashed == client_id)
    result = await db.execute(query)
    client = result.scalars().first()

    # 如果客户不存在，返回404错误
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 切换归档状态
    client.is_archived = not client.is_archived

    await db.commit()
    await db.refresh(client)

    return client

# 教育经历相关API
@router.post("/{client_id}/education", response_model=EducationResponse, status_code=status.HTTP_201_CREATED)
async def add_education(
    db: DBSession,
    education_data: EducationCreate,
    client_id: str = Path(..., description="客户哈希ID"),
    # current_user: CurrentUser = None,
):
    """
    添加教育经历

    Args:
        education_data: 教育经历数据
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        EducationResponse: 创建的教育经历信息
    """
    # 查询客户是否存在（使用哈希ID）
    query = select(Client).where(Client.id_hashed == client_id)
    result = await db.execute(query)
    client = result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 创建教育经历（使用客户真实ID）
    education = Education(**education_data.dict(), client_id=client.id)
    db.add(education)
    await db.commit()
    await db.refresh(education)

    return education

@router.put("/{client_id}/education/{education_id}", response_model=EducationResponse, status_code=status.HTTP_200_OK)
async def update_education(
    db: DBSession,
    education_data: EducationUpdate,
    client_id: str = Path(..., description="客户哈希ID"),
    education_id: int = Path(..., description="教育经历ID"),
    # current_user: CurrentUser = None,
):
    """
    更新教育经历

    Args:
        education_data: 教育经历更新数据
        client_id: 客户哈希ID
        education_id: 教育经历ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        EducationResponse: 更新后的教育经历信息
    """
    # 查询客户是否存在（使用哈希ID）
    client_query = select(Client).where(Client.id_hashed == client_id)
    client_result = await db.execute(client_query)
    client = client_result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询教育经历是否存在（使用客户真实ID）
    education_query = select(Education).where(
        (Education.id == education_id) &
        (Education.client_id == client.id)
    )
    education_result = await db.execute(education_query)
    education = education_result.scalars().first()

    if not education:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="教育经历不存在或不属于该客户"
        )

    # 更新教育经历信息
    update_data = education_data.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(education, key, value)

    await db.commit()
    await db.refresh(education)

    return education

@router.delete("/{client_id}/education/{education_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_education(
    db: DBSession,
    client_id: str = Path(..., description="客户哈希ID"),
    education_id: int = Path(..., description="教育经历ID"),
    # current_user: CurrentUser = None,
):
    """
    删除教育经历

    Args:
        client_id: 客户哈希ID
        education_id: 教育经历ID
        current_user: 当前用户
        db: 数据库会话
    """
    # 查询客户是否存在（使用哈希ID）
    client_query = select(Client).where(Client.id_hashed == client_id)
    client_result = await db.execute(client_query)
    client = client_result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询教育经历是否存在（使用客户真实ID）
    education_query = select(Education).where(
        (Education.id == education_id) &
        (Education.client_id == client.id)
    )
    education_result = await db.execute(education_query)
    education = education_result.scalars().first()

    if not education:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="教育经历不存在或不属于该客户"
        )

    # 删除教育经历
    await db.delete(education)
    await db.commit()

    return None

# 学术经历相关API
@router.post("/{client_id}/academic", response_model=AcademicResponse, status_code=status.HTTP_201_CREATED)
async def add_academic(
    db: DBSession,
    academic_data: AcademicCreate,
    client_id: str = Path(..., description="客户哈希ID"),
    # current_user: CurrentUser = None,
):
    """
    添加学术经历

    Args:
        academic_data: 学术经历数据
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        AcademicResponse: 创建的学术经历信息
    """
    # 查询客户是否存在（使用哈希ID）
    query = select(Client).where(Client.id_hashed == client_id)
    result = await db.execute(query)
    client = result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 创建学术经历（使用客户真实ID）
    academic = Academic(**academic_data.dict(), client_id=client.id)
    db.add(academic)
    await db.commit()
    await db.refresh(academic)

    return academic

@router.put("/{client_id}/academic/{academic_id}", response_model=AcademicResponse, status_code=status.HTTP_200_OK)
async def update_academic(
    db: DBSession,
    academic_data: AcademicUpdate,
    client_id: str = Path(..., description="客户哈希ID"),
    academic_id: int = Path(..., description="学术经历ID"),
    # current_user: CurrentUser = None,
):
    """
    更新学术经历

    Args:
        academic_data: 学术经历更新数据
        client_id: 客户哈希ID
        academic_id: 学术经历ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        AcademicResponse: 更新后的学术经历信息
    """
    # 查询客户是否存在（使用哈希ID）
    client_query = select(Client).where(Client.id_hashed == client_id)
    client_result = await db.execute(client_query)
    client = client_result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询学术经历是否存在（使用客户真实ID）
    academic_query = select(Academic).where(
        (Academic.id == academic_id) &
        (Academic.client_id == client.id)
    )
    academic_result = await db.execute(academic_query)
    academic = academic_result.scalars().first()

    if not academic:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="学术经历不存在或不属于该客户"
        )

    # 更新学术经历信息
    update_data = academic_data.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(academic, key, value)

    await db.commit()
    await db.refresh(academic)

    return academic

@router.delete("/{client_id}/academic/{academic_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_academic(
    db: DBSession,
    client_id: str = Path(..., description="客户哈希ID"),
    academic_id: int = Path(..., description="学术经历ID"),
    # current_user: CurrentUser = None,
):
    """
    删除学术经历

    Args:
        client_id: 客户哈希ID
        academic_id: 学术经历ID
        current_user: 当前用户
        db: 数据库会话
    """
    # 查询客户是否存在（使用哈希ID）
    client_query = select(Client).where(Client.id_hashed == client_id)
    client_result = await db.execute(client_query)
    client = client_result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询学术经历是否存在（使用客户真实ID）
    academic_query = select(Academic).where(
        (Academic.id == academic_id) &
        (Academic.client_id == client.id)
    )
    academic_result = await db.execute(academic_query)
    academic = academic_result.scalars().first()

    if not academic:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="学术经历不存在或不属于该客户"
        )

    # 删除学术经历
    await db.delete(academic)
    await db.commit()

    return None

# 工作经历相关API
@router.post("/{client_id}/work", response_model=WorkResponse, status_code=status.HTTP_201_CREATED)
async def add_work(
    db: DBSession,
    work_data: WorkCreate,
    client_id: str = Path(..., description="客户哈希ID"),
    # current_user: CurrentUser = None,
):
    """
    添加工作经历

    Args:
        work_data: 工作经历数据
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        WorkResponse: 创建的工作经历信息
    """
    # 查询客户是否存在（使用哈希ID）
    query = select(Client).where(Client.id_hashed == client_id)
    result = await db.execute(query)
    client = result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 创建工作经历（使用客户真实ID）
    work = Work(**work_data.dict(), client_id=client.id)
    db.add(work)
    await db.commit()
    await db.refresh(work)

    return work

@router.put("/{client_id}/work/{work_id}", response_model=WorkResponse, status_code=status.HTTP_200_OK)
async def update_work(
    db: DBSession,
    work_data: WorkUpdate,
    client_id: str = Path(..., description="客户哈希ID"),
    work_id: int = Path(..., description="工作经历ID"),
    # current_user: CurrentUser = None,
):
    """
    更新工作经历

    Args:
        work_data: 工作经历更新数据
        client_id: 客户哈希ID
        work_id: 工作经历ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        WorkResponse: 更新后的工作经历信息
    """
    # 查询客户是否存在（使用哈希ID）
    client_query = select(Client).where(Client.id_hashed == client_id)
    client_result = await db.execute(client_query)
    client = client_result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询工作经历是否存在（使用客户真实ID）
    work_query = select(Work).where(
        (Work.id == work_id) &
        (Work.client_id == client.id)
    )
    work_result = await db.execute(work_query)
    work = work_result.scalars().first()

    if not work:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="工作经历不存在或不属于该客户"
        )

    # 更新工作经历信息
    update_data = work_data.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(work, key, value)

    await db.commit()
    await db.refresh(work)

    return work

@router.delete("/{client_id}/work/{work_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_work(
    db: DBSession,
    client_id: str = Path(..., description="客户哈希ID"),
    work_id: int = Path(..., description="工作经历ID"),
    # current_user: CurrentUser = None,
):
    """
    删除工作经历

    Args:
        client_id: 客户哈希ID
        work_id: 工作经历ID
        current_user: 当前用户
        db: 数据库会话
    """
    # 查询客户是否存在（使用哈希ID）
    client_query = select(Client).where(Client.id_hashed == client_id)
    client_result = await db.execute(client_query)
    client = client_result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询工作经历是否存在（使用客户真实ID）
    work_query = select(Work).where(
        (Work.id == work_id) &
        (Work.client_id == client.id)
    )
    work_result = await db.execute(work_query)
    work = work_result.scalars().first()

    if not work:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="工作经历不存在或不属于该客户"
        )

    # 删除工作经历
    await db.delete(work)
    await db.commit()

    return None

# 活动经历相关API
@router.post("/{client_id}/activities", response_model=ActivityResponse, status_code=status.HTTP_201_CREATED)
async def add_activity(
    db: DBSession,
    activity_data: ActivityCreate,
    client_id: str = Path(..., description="客户哈希ID"),
    # current_user: CurrentUser = None,
):
    """
    添加活动经历

    Args:
        activity_data: 活动经历数据
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        ActivityResponse: 创建的活动经历信息
    """
    # 查询客户是否存在（使用哈希ID）
    query = select(Client).where(Client.id_hashed == client_id)
    result = await db.execute(query)
    client = result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 创建活动经历（使用客户真实ID）
    activity = Activity(**activity_data.dict(), client_id=client.id)
    db.add(activity)
    await db.commit()
    await db.refresh(activity)

    return activity

@router.put("/{client_id}/activities/{activity_id}", response_model=ActivityResponse, status_code=status.HTTP_200_OK)
async def update_activity(
    db: DBSession,
    activity_data: ActivityUpdate,
    client_id: str = Path(..., description="客户哈希ID"),
    activity_id: int = Path(..., description="活动经历ID"),
    # current_user: CurrentUser = None,
):
    """
    更新活动经历

    Args:
        activity_data: 活动经历更新数据
        client_id: 客户哈希ID
        activity_id: 活动经历ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        ActivityResponse: 更新后的活动经历信息
    """
    # 查询客户是否存在（使用哈希ID）
    client_query = select(Client).where(Client.id_hashed == client_id)
    client_result = await db.execute(client_query)
    client = client_result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询活动经历是否存在（使用客户真实ID）
    activity_query = select(Activity).where(
        (Activity.id == activity_id) &
        (Activity.client_id == client.id)
    )
    activity_result = await db.execute(activity_query)
    activity = activity_result.scalars().first()

    if not activity:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="活动经历不存在或不属于该客户"
        )

    # 更新活动经历信息
    update_data = activity_data.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(activity, key, value)

    await db.commit()
    await db.refresh(activity)

    return activity

@router.delete("/{client_id}/activities/{activity_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_activity(
    db: DBSession,
    client_id: str = Path(..., description="客户哈希ID"),
    activity_id: int = Path(..., description="活动经历ID"),
    # current_user: CurrentUser = None,
):
    """
    删除活动经历

    Args:
        client_id: 客户哈希ID
        activity_id: 活动经历ID
        current_user: 当前用户
        db: 数据库会话
    """
    # 查询客户是否存在（使用哈希ID）
    client_query = select(Client).where(Client.id_hashed == client_id)
    client_result = await db.execute(client_query)
    client = client_result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询活动经历是否存在（使用客户真实ID）
    activity_query = select(Activity).where(
        (Activity.id == activity_id) &
        (Activity.client_id == client.id)
    )
    activity_result = await db.execute(activity_query)
    activity = activity_result.scalars().first()

    if not activity:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="活动经历不存在或不属于该客户"
        )

    # 删除活动经历
    await db.delete(activity)
    await db.commit()

    return None

# 奖项相关API
@router.post("/{client_id}/awards", response_model=AwardResponse, status_code=status.HTTP_201_CREATED)
async def add_award(
    db: DBSession,
    award_data: AwardCreate,
    client_id: str = Path(..., description="客户哈希ID"),
    # current_user: CurrentUser = None,
):
    """
    添加奖项

    Args:
        award_data: 奖项数据
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        AwardResponse: 创建的奖项信息
    """
    # 查询客户是否存在（使用哈希ID）
    query = select(Client).where(Client.id_hashed == client_id)
    result = await db.execute(query)
    client = result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 创建奖项（使用客户真实ID）
    award = Award(**award_data.dict(), client_id=client.id)
    db.add(award)
    await db.commit()
    await db.refresh(award)

    return award

@router.put("/{client_id}/awards/{award_id}", response_model=AwardResponse, status_code=status.HTTP_200_OK)
async def update_award(
    db: DBSession,
    award_data: AwardUpdate,
    client_id: str = Path(..., description="客户哈希ID"),
    award_id: int = Path(..., description="奖项ID"),
    # current_user: CurrentUser = None,
):
    """
    更新奖项

    Args:
        award_data: 奖项更新数据
        client_id: 客户哈希ID
        award_id: 奖项ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        AwardResponse: 更新后的奖项信息
    """
    # 查询客户是否存在（使用哈希ID）
    client_query = select(Client).where(Client.id_hashed == client_id)
    client_result = await db.execute(client_query)
    client = client_result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询奖项是否存在（使用客户真实ID）
    award_query = select(Award).where(
        (Award.id == award_id) &
        (Award.client_id == client.id)
    )
    award_result = await db.execute(award_query)
    award = award_result.scalars().first()

    if not award:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="奖项不存在或不属于该客户"
        )

    # 更新奖项信息
    update_data = award_data.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(award, key, value)

    await db.commit()
    await db.refresh(award)

    return award

@router.delete("/{client_id}/awards/{award_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_award(
    db: DBSession,
    client_id: str = Path(..., description="客户哈希ID"),
    award_id: int = Path(..., description="奖项ID"),
    # current_user: CurrentUser = None,
):
    """
    删除奖项

    Args:
        client_id: 客户哈希ID
        award_id: 奖项ID
        current_user: 当前用户
        db: 数据库会话
    """
    # 查询客户是否存在（使用哈希ID）
    client_query = select(Client).where(Client.id_hashed == client_id)
    client_result = await db.execute(client_query)
    client = client_result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询奖项是否存在（使用客户真实ID）
    award_query = select(Award).where(
        (Award.id == award_id) &
        (Award.client_id == client.id)
    )
    award_result = await db.execute(award_query)
    award = award_result.scalars().first()

    if not award:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="奖项不存在或不属于该客户"
        )

    # 删除奖项
    await db.delete(award)
    await db.commit()

    return None

# 技能相关API
@router.post("/{client_id}/skills", response_model=SkillResponse, status_code=status.HTTP_201_CREATED)
async def add_skill(
    db: DBSession,
    skill_data: SkillCreate,
    client_id: str = Path(..., description="客户哈希ID"),
    # current_user: CurrentUser = None,
):
    """
    添加技能

    Args:
        skill_data: 技能数据
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        SkillResponse: 创建的技能信息
    """
    # 查询客户是否存在（使用哈希ID）
    query = select(Client).where(Client.id_hashed == client_id)
    result = await db.execute(query)
    client = result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 创建技能（使用客户真实ID）
    skill = Skill(**skill_data.dict(), client_id=client.id)
    db.add(skill)
    await db.commit()
    await db.refresh(skill)

    return skill

@router.put("/{client_id}/skills/{skill_id}", response_model=SkillResponse, status_code=status.HTTP_200_OK)
async def update_skill(
    db: DBSession,
    skill_data: SkillUpdate,
    client_id: str = Path(..., description="客户哈希ID"),
    skill_id: int = Path(..., description="技能ID"),
    # current_user: CurrentUser = None,
):
    """
    更新技能

    Args:
        skill_data: 技能更新数据
        client_id: 客户哈希ID
        skill_id: 技能ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        SkillResponse: 更新后的技能信息
    """
    # 查询客户是否存在（使用哈希ID）
    client_query = select(Client).where(Client.id_hashed == client_id)
    client_result = await db.execute(client_query)
    client = client_result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询技能是否存在（使用客户真实ID）
    skill_query = select(Skill).where(
        (Skill.id == skill_id) &
        (Skill.client_id == client.id)
    )
    skill_result = await db.execute(skill_query)
    skill = skill_result.scalars().first()

    if not skill:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="技能不存在或不属于该客户"
        )

    # 更新技能信息
    update_data = skill_data.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(skill, key, value)

    await db.commit()
    await db.refresh(skill)

    return skill

@router.delete("/{client_id}/skills/{skill_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_skill(
    db: DBSession,
    client_id: str = Path(..., description="客户哈希ID"),
    skill_id: int = Path(..., description="技能ID"),
    # current_user: CurrentUser = None,
):
    """
    删除技能

    Args:
        client_id: 客户哈希ID
        skill_id: 技能ID
        current_user: 当前用户
        db: 数据库会话
    """
    # 查询客户是否存在（使用哈希ID）
    client_query = select(Client).where(Client.id_hashed == client_id)
    client_result = await db.execute(client_query)
    client = client_result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询技能是否存在（使用客户真实ID）
    skill_query = select(Skill).where(
        (Skill.id == skill_id) &
        (Skill.client_id == client.id)
    )
    skill_result = await db.execute(skill_query)
    skill = skill_result.scalars().first()

    if not skill:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="技能不存在或不属于该客户"
        )

    # 删除技能
    await db.delete(skill)
    await db.commit()

    return None

# 语言成绩相关API
@router.post("/{client_id}/language-scores", response_model=LanguageScoreResponse, status_code=status.HTTP_201_CREATED)
async def add_language_score(
    db: DBSession,
    language_score_data: LanguageScoreCreate,
    client_id: str = Path(..., description="客户哈希ID"),
    # current_user: CurrentUser = None,
):
    """
    添加语言成绩

    Args:
        language_score_data: 语言成绩数据
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        LanguageScoreResponse: 创建的语言成绩信息
    """
    # 查询客户是否存在（使用哈希ID）
    query = select(Client).where(Client.id_hashed == client_id)
    result = await db.execute(query)
    client = result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 创建语言成绩（使用客户真实ID）
    language_score = LanguageScore(**language_score_data.dict(), client_id=client.id)
    db.add(language_score)
    await db.commit()
    await db.refresh(language_score)

    return language_score

@router.put("/{client_id}/language-scores/{language_score_id}", response_model=LanguageScoreResponse, status_code=status.HTTP_200_OK)
async def update_language_score(
    db: DBSession,
    language_score_data: LanguageScoreUpdate,
    client_id: str = Path(..., description="客户哈希ID"),
    language_score_id: int = Path(..., description="语言成绩ID"),
    # current_user: CurrentUser = None,
):
    """
    更新语言成绩

    Args:
        language_score_data: 语言成绩更新数据
        client_id: 客户哈希ID
        language_score_id: 语言成绩ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        LanguageScoreResponse: 更新后的语言成绩信息
    """
    # 查询客户是否存在（使用哈希ID）
    client_query = select(Client).where(Client.id_hashed == client_id)
    client_result = await db.execute(client_query)
    client = client_result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询语言成绩是否存在（使用客户真实ID）
    language_score_query = select(LanguageScore).where(
        (LanguageScore.id == language_score_id) &
        (LanguageScore.client_id == client.id)
    )
    language_score_result = await db.execute(language_score_query)
    language_score = language_score_result.scalars().first()

    if not language_score:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="语言成绩不存在或不属于该客户"
        )

    # 更新语言成绩信息
    update_data = language_score_data.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(language_score, key, value)

    await db.commit()
    await db.refresh(language_score)

    return language_score

@router.delete("/{client_id}/language-scores/{language_score_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_language_score(
    db: DBSession,
    client_id: str = Path(..., description="客户哈希ID"),
    language_score_id: int = Path(..., description="语言成绩ID"),
    # current_user: CurrentUser = None,
):
    """
    删除语言成绩

    Args:
        client_id: 客户哈希ID
        language_score_id: 语言成绩ID
        current_user: 当前用户
        db: 数据库会话
    """
    # 查询客户是否存在（使用哈希ID）
    client_query = select(Client).where(Client.id_hashed == client_id)
    client_result = await db.execute(client_query)
    client = client_result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询语言成绩是否存在（使用客户真实ID）
    language_score_query = select(LanguageScore).where(
        (LanguageScore.id == language_score_id) &
        (LanguageScore.client_id == client.id)
    )
    language_score_result = await db.execute(language_score_query)
    language_score = language_score_result.scalars().first()

    if not language_score:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="语言成绩不存在或不属于该客户"
        )

    # 删除语言成绩
    await db.delete(language_score)
    await db.commit()

    return None

# 个人想法相关API
@router.post("/{client_id}/thoughts", response_model=ThoughtResponse, status_code=status.HTTP_201_CREATED)
async def add_thought(
    db: DBSession,
    thought_data: ThoughtCreate,
    client_id: str = Path(..., description="客户哈希ID"),
    # current_user: CurrentUser = None,
):
    """
    添加个人想法

    Args:
        thought_data: 个人想法数据
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        ThoughtResponse: 创建的个人想法信息
    """
    # 查询客户是否存在（使用哈希ID）
    query = select(Client).where(Client.id_hashed == client_id)
    result = await db.execute(query)
    client = result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询是否已存在个人想法记录（使用客户真实ID）
    thought_query = select(Thought).where(Thought.client_id == client.id)
    thought_result = await db.execute(thought_query)
    existing_thought = thought_result.scalars().first()

    if existing_thought:
        # 如果已存在，则更新现有记录
        update_data = thought_data.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(existing_thought, key, value)
        await db.commit()
        await db.refresh(existing_thought)
        return existing_thought
    else:
        # 如果不存在，则创建新记录（使用客户真实ID）
        thought = Thought(**thought_data.dict(), client_id=client.id)
        db.add(thought)
        await db.commit()
        await db.refresh(thought)
        return thought

@router.put("/{client_id}/thoughts", response_model=ThoughtResponse, status_code=status.HTTP_200_OK)
async def update_thought(
    db: DBSession,
    thought_data: ThoughtUpdate,
    client_id: str = Path(..., description="客户哈希ID"),
    # current_user: CurrentUser = None,
):
    """
    更新个人想法

    Args:
        thought_data: 个人想法更新数据
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        ThoughtResponse: 更新后的个人想法信息
    """
    # 查询客户是否存在（使用哈希ID）
    client_query = select(Client).where(Client.id_hashed == client_id)
    client_result = await db.execute(client_query)
    client = client_result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询个人想法是否存在（使用客户真实ID）
    thought_query = select(Thought).where(Thought.client_id == client.id)
    thought_result = await db.execute(thought_query)
    thought = thought_result.scalars().first()

    if not thought:
        # 如果不存在，则创建新记录（使用客户真实ID）
        thought = Thought(**thought_data.dict(), client_id=client.id)
        db.add(thought)
    else:
        # 如果存在，则更新现有记录
        update_data = thought_data.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(thought, key, value)

    await db.commit()
    await db.refresh(thought)

    return thought

@router.delete("/{client_id}/thoughts", status_code=status.HTTP_204_NO_CONTENT)
async def delete_thought(
    db: DBSession,
    client_id: str = Path(..., description="客户哈希ID"),
    # current_user: CurrentUser = None,
):
    """
    删除个人想法

    Args:
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话
    """
    # 查询客户是否存在（使用哈希ID）
    client_query = select(Client).where(Client.id_hashed == client_id)
    client_result = await db.execute(client_query)
    client = client_result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询个人想法是否存在（使用客户真实ID）
    thought_query = select(Thought).where(Thought.client_id == client.id)
    thought_result = await db.execute(thought_query)
    thought = thought_result.scalars().first()

    if not thought:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="个人想法不存在或不属于该客户"
        )

    # 删除个人想法
    await db.delete(thought)
    await db.commit()

    return None

# 背景自定义模块相关API
@router.post("/{client_id}/background-modules", response_model=CustomModuleResponse, status_code=status.HTTP_201_CREATED)
async def add_background_module(
    db: DBSession,
    module_data: CustomModuleCreate,
    client_id: str = Path(..., description="客户哈希ID"),
    # current_user: CurrentUser = None,
):
    """
    添加背景自定义模块

    Args:
        module_data: 背景模块数据
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        CustomModuleResponse: 创建的背景模块信息
    """
    # 查询客户是否存在（使用哈希ID）
    query = select(Client).where(Client.id_hashed == client_id)
    result = await db.execute(query)
    client = result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 创建背景模块（使用客户真实ID）
    background_module = BackgroundCustomModule(**module_data.dict(), client_id=client.id)
    db.add(background_module)
    await db.commit()
    await db.refresh(background_module)

    return background_module

@router.put("/{client_id}/background-modules/{module_id}", response_model=CustomModuleResponse, status_code=status.HTTP_200_OK)
async def update_background_module(
    db: DBSession,
    module_data: CustomModuleUpdate,
    client_id: str = Path(..., description="客户哈希ID"),
    module_id: int = Path(..., description="模块ID"),
    # current_user: CurrentUser = None,
):
    """
    更新背景自定义模块

    Args:
        module_data: 背景模块更新数据
        client_id: 客户哈希ID
        module_id: 模块ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        CustomModuleResponse: 更新后的背景模块信息
    """
    # 查询客户是否存在（使用哈希ID）
    client_query = select(Client).where(Client.id_hashed == client_id)
    client_result = await db.execute(client_query)
    client = client_result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询背景模块是否存在（使用客户真实ID）
    module_query = select(BackgroundCustomModule).where(
        (BackgroundCustomModule.id == module_id) &
        (BackgroundCustomModule.client_id == client.id)
    )
    module_result = await db.execute(module_query)
    background_module = module_result.scalars().first()

    if not background_module:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="背景模块不存在或不属于该客户"
        )

    # 更新背景模块信息
    update_data = module_data.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(background_module, key, value)

    await db.commit()
    await db.refresh(background_module)

    return background_module

@router.delete("/{client_id}/background-modules/{module_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_background_module(
    db: DBSession,
    client_id: str = Path(..., description="客户哈希ID"),
    module_id: int = Path(..., description="模块ID"),
    # current_user: CurrentUser = None,
):
    """
    删除背景自定义模块

    Args:
        client_id: 客户哈希ID
        module_id: 模块ID
        current_user: 当前用户
        db: 数据库会话
    """
    # 查询客户是否存在（使用哈希ID）
    client_query = select(Client).where(Client.id_hashed == client_id)
    client_result = await db.execute(client_query)
    client = client_result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询背景模块是否存在（使用客户真实ID）
    module_query = select(BackgroundCustomModule).where(
        (BackgroundCustomModule.id == module_id) &
        (BackgroundCustomModule.client_id == client.id)
    )
    module_result = await db.execute(module_query)
    background_module = module_result.scalars().first()

    if not background_module:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="背景模块不存在或不属于该客户"
        )

    # 删除背景模块
    await db.delete(background_module)
    await db.commit()

    return None

# 想法自定义模块相关API
@router.post("/{client_id}/thought-modules", response_model=CustomModuleResponse, status_code=status.HTTP_201_CREATED)
async def add_thought_module(
    db: DBSession,
    module_data: CustomModuleCreate,
    client_id: str = Path(..., description="客户哈希ID"),
    # current_user: CurrentUser = None,
):
    """
    添加想法自定义模块

    Args:
        module_data: 想法模块数据
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        CustomModuleResponse: 创建的想法模块信息
    """
    # 查询客户是否存在（使用哈希ID）
    query = select(Client).where(Client.id_hashed == client_id)
    result = await db.execute(query)
    client = result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 创建想法模块（使用客户真实ID）
    thought_module = ThoughtCustomModule(**module_data.dict(), client_id=client.id)
    db.add(thought_module)
    await db.commit()
    await db.refresh(thought_module)

    return thought_module

@router.put("/{client_id}/thought-modules/{module_id}", response_model=CustomModuleResponse, status_code=status.HTTP_200_OK)
async def update_thought_module(
    db: DBSession,
    module_data: CustomModuleUpdate,
    client_id: str = Path(..., description="客户哈希ID"),
    module_id: int = Path(..., description="模块ID"),
    # current_user: CurrentUser = None,
):
    """
    更新想法自定义模块

    Args:
        module_data: 想法模块更新数据
        client_id: 客户哈希ID
        module_id: 模块ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        CustomModuleResponse: 更新后的想法模块信息
    """
    # 查询客户是否存在（使用哈希ID）
    client_query = select(Client).where(Client.id_hashed == client_id)
    client_result = await db.execute(client_query)
    client = client_result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询想法模块是否存在（使用客户真实ID）
    module_query = select(ThoughtCustomModule).where(
        (ThoughtCustomModule.id == module_id) &
        (ThoughtCustomModule.client_id == client.id)
    )
    module_result = await db.execute(module_query)
    thought_module = module_result.scalars().first()

    if not thought_module:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="想法模块不存在或不属于该客户"
        )

    # 更新想法模块信息
    update_data = module_data.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(thought_module, key, value)

    await db.commit()
    await db.refresh(thought_module)

    return thought_module

@router.delete("/{client_id}/thought-modules/{module_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_thought_module(
    db: DBSession,
    client_id: str = Path(..., description="客户哈希ID"),
    module_id: int = Path(..., description="模块ID"),
    # current_user: CurrentUser = None,
):
    """
    删除想法自定义模块

    Args:
        client_id: 客户哈希ID
        module_id: 模块ID
        current_user: 当前用户
        db: 数据库会话
    """
    # 查询客户是否存在（使用哈希ID）
    client_query = select(Client).where(Client.id_hashed == client_id)
    client_result = await db.execute(client_query)
    client = client_result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 查询想法模块是否存在（使用客户真实ID）
    module_query = select(ThoughtCustomModule).where(
        (ThoughtCustomModule.id == module_id) &
        (ThoughtCustomModule.client_id == client.id)
    )
    module_result = await db.execute(module_query)
    thought_module = module_result.scalars().first()

    if not thought_module:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="想法模块不存在或不属于该客户"
        )

    # 删除想法模块
    await db.delete(thought_module)
    await db.commit()

    return None