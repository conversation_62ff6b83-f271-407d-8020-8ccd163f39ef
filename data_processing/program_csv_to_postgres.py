import os
import pandas as pd
import argparse
from sqlalchemy import Column, Integer, String, Text, create_engine, text, Float
from sqlalchemy.orm import sessionmaker, declarative_base
from sqlalchemy_utils import database_exists, create_database
from pathlib import Path

# 声明SQLAlchemy基类
Base = declarative_base()

class AISelectionProgram(Base):
    """
    专业数据库模型类，对应 PostgreSQL 数据库中的 programs 表
    存储专业信息数据
    """
    __tablename__ = "ai_selection_programs"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="ID，自增主键")
    
    # 学校信息
    school_name_cn = Column(String(200), nullable=False, index=True, comment="学校中文名")
    school_name_en = Column(String(200), nullable=True, index=True, comment="学校英文名")
    school_qs_name = Column(String(200), nullable=True, comment="学校QS英文名")
    school_qs_rank = Column(String(100), nullable=True, comment="学校QS排名") # 排名字符串和int类型混杂...
    school_region = Column(String(100), nullable=True, comment="学校所在地区")

    # 专业信息
    program_code = Column(Integer, nullable=True, comment="专业代码")
    degree = Column(String(50), nullable=True, comment="申请学位类型")
    program_name_cn = Column(String(200), nullable=False, index=True, comment="专业中文名")
    program_name_en = Column(String(200), nullable=True, index=True, comment="专业英文名")
    program_category = Column(String(100), nullable=True, comment="专业大类")
    program_direction = Column(String(200), nullable=True, comment="专业方向")
    faculty = Column(String(200), nullable=True, comment="所在学院")
    
    
    enrollment_time = Column(String(100), nullable=True, comment="入学时间")
    program_duration = Column(String(100), nullable=True, comment="项目时长")
    program_tuition = Column(String(100), nullable=True, comment="项目学费")
    application_time = Column(Text, nullable=True, comment="申请时间")
    application_requirements = Column(Text, nullable=True, comment="申请要求")
    gpa_requirements = Column(Float, nullable=True, comment="绩点要求")
    language_requirements = Column(Text, nullable=True, comment="语言要求")

    program_objectives = Column(Text, nullable=True, comment="培养目标")
    courses = Column(Text, nullable=True, comment="课程设置")

    # 其他信息
    program_website = Column(String(500), nullable=True, comment="项目官网")
    other_cost = Column(String(100), nullable=True, comment="年开销预估值")
    degree_evaluation = Column(Text, nullable=True, comment="留服认证")

def get_database_url(user="postgres", password="admin123", host="localhost", 
                      port="5432", db_name="tunshuedu_ai_selection_db"):
    """构建数据库连接URL"""
    return f"postgresql://{user}:{password}@{host}:{port}/{db_name}"

def create_tables(engine):
    """创建数据库表"""
    Base.metadata.create_all(engine)
    print("表创建成功！")

def import_csv_to_db(csv_path, engine):
    """导入CSV数据到数据库"""
    try:
        # 读取CSV文件
        df = pd.read_csv(csv_path)
        print(f"成功读取CSV文件，包含 {len(df)} 行数据")
        
        # 重命名列以匹配数据库表结构
        column_mapping = {
            '学校中文名': 'school_name_cn',
            '学校英文名': 'school_name_en',
            '专业中文名': 'program_name_cn',
            '专业英文名': 'program_name_en',
            '专业大类': 'program_category',
            '专业方向': 'program_direction',
            '所在学院': 'faculty',
            '入学时间': 'enrollment_time',
            '项目时长': 'program_duration',
            '项目官网': 'program_website',
            '培养目标': 'program_objectives',
            '申请要求': 'application_requirements',
            '语言要求': 'language_requirements',
            '申请时间': 'application_time',
            '课程设置': 'courses',
            '专业代码': 'program_code',
            '项目学费': 'program_tuition',
            '学校英文名(QS25)': 'school_qs_name',
            '学校排名(QS25)': 'school_qs_rank',
            '学校所在地区': 'school_region',
            # 新增字段映射
            '申请学位类型': 'degree',
            '绩点要求': 'gpa_requirements',
            '年开销预估值': 'other_cost',
            '留服认证': 'degree_evaluation',
        }
        
        # 应用列名映射
        df = df.rename(columns=column_mapping)
        
        # 创建数据库会话
        Session = sessionmaker(bind=engine)
        session = Session()
        
        try:
            # 如果表已存在，先清空表
            # 使用text()包装SQL语句
            truncate_sql = text(f"TRUNCATE TABLE {AISelectionProgram.__tablename__} RESTART IDENTITY CASCADE")
            session.execute(truncate_sql)
            session.commit()
            print(f"表 {AISelectionProgram.__tablename__} 已清空，准备导入新数据")
            
            # 将DataFrame中的数据转换为字典列表，然后批量插入
            # 分批处理，每次处理1000条记录
            batch_size = 1000
            total_records = len(df)
            
            for i in range(0, total_records, batch_size):
                end = min(i + batch_size, total_records)
                batch_df = df.iloc[i:end]
                
                # 将DataFrame转换为字典列表
                records = batch_df.to_dict(orient='records')
                
                # 批量插入数据库
                session.bulk_insert_mappings(AISelectionProgram, records)
                session.commit()
                
                print(f"成功导入记录 {i+1} 到 {end} (共 {total_records} 条)")
                
            print(f"所有数据导入完成！总共导入了 {total_records} 条记录")
        
        except Exception as e:
            session.rollback()
            print(f"导入数据时发生错误: {str(e)}")
            raise
        finally:
            session.close()
            
    except Exception as e:
        print(f"处理CSV文件时发生错误: {str(e)}")
        raise

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='将CSV数据导入PostgreSQL数据库')
    parser.add_argument('--csv', type=str, default='专业数据库.csv',
                        help='CSV文件路径 (默认: 专业数据库.csv)')
    parser.add_argument('--db-user', type=str, default='postgres',
                        help='数据库用户名 (默认: postgres)')
    parser.add_argument('--db-password', type=str, default='admin123',
                        help='数据库密码 (默认: admin123)')
    parser.add_argument('--db-host', type=str, default='localhost',
                        help='数据库主机 (默认: localhost)')
    parser.add_argument('--db-port', type=str, default='5432',
                        help='数据库端口 (默认: 5432)')
    parser.add_argument('--db-name', type=str, default='tunshuedu_ai_selection_db',
                        help='数据库名称 (默认: tunshuedu_ai_selection_db)')
    
    args = parser.parse_args()
    
    # 构建数据库连接URL
    db_url = get_database_url(
        user=args.db_user,
        password=args.db_password,
        host=args.db_host,
        port=args.db_port,
        db_name=args.db_name
    )
    
    # 创建数据库引擎，指定UTF-8编码
    engine = create_engine(
        db_url,
        connect_args={
            "client_encoding": "utf8",  # 指定客户端编码为UTF-8
        },
    )
    
    # 如果数据库不存在，创建UTF-8编码的数据库
    if not database_exists(engine.url):
        # 创建数据库，指定UTF-8编码
        create_database(engine.url, encoding='utf8') # 此处其实可以不指定编码设置，关键是前面连接参数: client_encoding: utf8 的设置 （没用啊...还是得手动创建...)
        print(f"数据库 {args.db_name} 已创建（UTF-8编码）")
    
    # 创建表
    create_tables(engine)
    
    # 确定CSV文件路径
    script_dir = Path(__file__).parent
    csv_path = script_dir / args.csv
    
    if not csv_path.exists():
        print(f"错误: CSV文件 {csv_path} 不存在!")
        return
    
    # 导入数据
    import_csv_to_db(csv_path, engine)
    print("数据导入过程完成!")

if __name__ == "__main__":
    main() 
    # python program_csv_to_postgres.py --db-password="123456789" 
    # python program_csv_to_postgres.py --db-password="123456789" --db-name="tunshuedu_db"