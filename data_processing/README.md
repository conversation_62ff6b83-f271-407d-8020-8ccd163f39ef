# 数据处理工具

本目录包含数据处理和数据库操作相关的脚本。

## 数据处理流程

### 1. Jupyter Notebook 数据处理
在 `program_txt_to_database.ipynb` 中进行原始数据处理，包括：
- 从JSON格式txt文件提取原始数据
- 缺失字段补充（如学费信息提取）
- 学校名称规范化和QS排名匹配
- 地区名称英文转中文
- 新增自定义列（申请学位类型、绩点要求、年开销预估值、留服认证）
- 数据清理和质量检查

### 2. 专业数据库导入工具 (program_csv_to_postgres.py)

这个脚本用于将处理后的专业库CSV数据导入到PostgreSQL数据库中。

### 功能

- 将专业库CSV数据导入到PostgreSQL数据库的 `programs` 表中
- 自动创建数据库表结构
- 支持批量导入大量数据
- 如果表已存在，会先清空表再导入新数据

### 依赖安装

在使用脚本前，请确保安装了以下Python依赖：

```bash
pip install pandas sqlalchemy sqlalchemy-utils psycopg2-binary
```

### 使用方法

```bash
python program_csv_to_postgres.py [参数]
```

#### 可选参数

- `--csv`: CSV文件路径 (默认: `专业数据库.csv`)
- `--db-user`: 数据库用户名 (默认: `postgres`)
- `--db-password`: 数据库密码 (默认: `admin123`)
- `--db-host`: 数据库主机地址 (默认: `localhost`)
- `--db-port`: 数据库端口 (默认: `5432`)
- `--db-name`: 数据库名称 (默认: `tunshuedu_ai_selection_db`)

#### 示例

使用默认参数导入数据:

```bash
python program_csv_to_postgres.py
```

指定CSV文件路径和数据库连接信息:

```bash
python program_csv_to_postgres.py --csv="专业数据库.csv" --db-user="youruser" --db-password="yourpass" --db-host="yourhost"
```

### 数据表结构 (programs)

导入后的数据表结构如下:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | Integer | 主键 (自增) |
| school_name_cn | String(200) | 学校中文名 |
| school_name_en | String(200) | 学校英文名 |
| school_qs_name | String(200) | 学校QS英文名 |
| school_qs_rank | String(100) | 学校QS排名 |
| school_region | String(100) | 学校所在地区 |
| program_code | Integer | 专业代码 |
| degree | String(50) | 申请学位类型 |
| program_name_cn | String(200) | 专业中文名 |
| program_name_en | String(200) | 专业英文名 |
| program_category | String(100) | 专业大类 |
| program_direction | String(200) | 专业方向 |
| faculty | String(200) | 所在学院 |
| enrollment_time | String(100) | 入学时间 |
| program_duration | String(100) | 项目时长 |
| program_tuition | String(100) | 项目学费 |
| application_time | Text | 申请时间 |
| application_requirements | Text | 申请要求 |
| gpa_requirements | Float | 绩点要求 |
| language_requirements | Text | 语言要求 |
| program_objectives | Text | 培养目标 |
| courses | Text | 课程设置 |
| program_website | String(500) | 项目官网 |
| other_cost | String(100) | 年开销预估值 |
| degree_evaluation | Text | 留服认证 |

### 注意事项

- 脚本会自动创建表结构，如果表已存在会先清空表
- 导入过程使用批处理方式，每次处理1000条记录，减少内存占用
- 确保PostgreSQL数据库服务已启动且可以访问 

