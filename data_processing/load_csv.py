#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专业数据库CSV文件加载和处理脚本

该脚本用于加载和处理专业数据库.csv文件，提供基本的数据分析功能。
同时支持从PostgreSQL数据库查询数据。
"""

import pandas as pd
import numpy as np
import sys
import os
from pathlib import Path
from sqlalchemy import create_engine
import psycopg2

def load_csv_data(file_path="专业数据库.csv"):
    """
    加载CSV文件
    
    Args:
        file_path (str): CSV文件路径
        
    Returns:
        pd.DataFrame: 加载的数据框
    """
    try:
        # 确保文件存在
        if not os.path.exists(file_path):
            print(f"错误: 文件 {file_path} 不存在")
            return None
            
        # 加载CSV文件
        print(f"正在加载文件: {file_path}")
        df = pd.read_csv(file_path, encoding='utf-8')
        
        print(f"成功加载数据，共 {len(df)} 行，{len(df.columns)} 列")
        return df
        
    except Exception as e:
        print(f"加载文件时出错: {e}")
        return None

def analyze_data(df):
    """
    分析数据基本信息
    
    Args:
        df (pd.DataFrame): 数据框
    """
    if df is None:
        print("数据为空，无法分析")
        return
        
    print("\n=== 数据基本信息 ===")
    print(f"数据形状: {df.shape}")
    print(f"列名: {list(df.columns)}")
    
    print("\n=== 数据类型 ===")
    print(df.dtypes)
    
    print("\n=== 缺失值统计 ===")
    missing_data = df.isnull().sum()
    print(missing_data[missing_data > 0])
    
    print("\n=== 前5行数据 ===")
    print(df.head())

def analyze_schools(df):
    """
    分析学校相关信息
    
    Args:
        df (pd.DataFrame): 数据框
    """
    if df is None:
        return
        
    print("\n=== 学校分析 ===")
    
    # 学校数量统计
    unique_schools = df['学校中文名'].nunique()
    print(f"总共有 {unique_schools} 所不同的学校")
    
    # 各学校专业数量
    school_counts = df['学校中文名'].value_counts()
    print(f"\n各学校专业数量前10名:")
    print(school_counts.head(10))
    
    # QS排名分析
    if '学校排名(QS25)' in df.columns:
        print(f"\nQS排名统计:")
        qs_rankings = df['学校排名(QS25)'].dropna()
        print(f"有排名的学校数量: {len(qs_rankings)}")
        print(f"排名范围: {qs_rankings.min()} - {qs_rankings.max()}")

def analyze_majors(df):
    """
    分析专业相关信息
    
    Args:
        df (pd.DataFrame): 数据框
    """
    if df is None:
        return
        
    print("\n=== 专业分析 ===")
    
    # 专业大类统计
    if '专业大类' in df.columns:
        major_categories = df['专业大类'].value_counts()
        print(f"专业大类分布:")
        print(major_categories)
    
    # 学位类型统计
    if '申请学位类型' in df.columns:
        degree_types = df['申请学位类型'].value_counts()
        print(f"\n学位类型分布:")
        print(degree_types)
    
    # 项目时长统计
    if '项目时长' in df.columns:
        duration = df['项目时长'].value_counts()
        print(f"\n项目时长分布:")
        print(duration)

def analyze_costs(df):
    """
    分析费用相关信息
    
    Args:
        df (pd.DataFrame): 数据框
    """
    if df is None:
        return
        
    print("\n=== 费用分析 ===")
    
    # 学费分析
    if '项目学费' in df.columns:
        # 提取数字部分进行分析
        fees = df['项目学费'].dropna()
        print(f"学费信息样本:")
        print(fees.head(10))
    
    # 年开销预估
    if '年开销预估值' in df.columns:
        costs = df['年开销预估值'].value_counts()
        print(f"\n年开销预估分布:")
        print(costs)

def load_from_database():
    """
    从PostgreSQL数据库加载数据

    Returns:
        pd.DataFrame: 从数据库加载的数据框
    """
    try:
        # 数据库连接参数
        db_url = "postgresql://postgres:admin123@localhost:5432/tunshuedu_db"

        # 创建数据库引擎
        engine = create_engine(db_url)

        # 查询数据
        query = """
        SELECT
            school_name_cn as "学校中文名",
            school_name_en as "学校英文名",
            program_name_cn as "专业中文名",
            program_name_en as "专业英文名",
            program_category as "专业大类",
            program_direction as "专业方向",
            faculty as "所在学院",
            enrollment_time as "入学时间",
            program_duration as "项目时长",
            program_tuition as "项目学费",
            school_qs_rank as "学校排名(QS25)",
            school_region as "学校所在地区",
            degree as "申请学位类型",
            gpa_requirements as "绩点要求",
            other_cost as "年开销预估值"
        FROM ai_selection_programs
        """

        print("正在从数据库加载数据...")
        df = pd.read_sql_query(query, engine)
        print(f"成功从数据库加载数据，共 {len(df)} 行，{len(df.columns)} 列")

        return df

    except Exception as e:
        print(f"从数据库加载数据时出错: {e}")
        return None

def main():
    """
    主函数
    """
    print("=== 专业数据库分析工具 ===")

    # 选择数据源
    print("\n数据源选择:")
    print("1. 从CSV文件加载")
    print("2. 从PostgreSQL数据库加载")

    source_choice = input("请选择数据源 (1-2): ").strip()

    if source_choice == '1':
        df = load_csv_data()
    elif source_choice == '2':
        df = load_from_database()
    else:
        print("无效选择，默认使用CSV文件")
        df = load_csv_data()

    if df is not None:
        # 基本数据分析
        analyze_data(df)

        # 学校分析
        analyze_schools(df)

        # 专业分析
        analyze_majors(df)

        # 费用分析
        analyze_costs(df)

        print("\n=== 分析完成 ===")

        # 提供交互式查询选项
        while True:
            print("\n可用操作:")
            print("1. 查看特定学校的专业")
            print("2. 查看特定专业大类")
            print("3. 查看特定地区的学校")
            print("4. 查看QS排名前50的学校")
            print("5. 退出")

            choice = input("请选择操作 (1-5): ").strip()

            if choice == '1':
                school_name = input("请输入学校名称: ").strip()
                school_data = df[df['学校中文名'].str.contains(school_name, na=False)]
                if not school_data.empty:
                    print(f"\n{school_name} 的专业:")
                    print(school_data[['专业中文名', '专业大类', '项目时长', '项目学费']].to_string())
                else:
                    print("未找到相关学校")

            elif choice == '2':
                category = input("请输入专业大类: ").strip()
                category_data = df[df['专业大类'].str.contains(category, na=False)]
                if not category_data.empty:
                    print(f"\n{category} 类专业:")
                    print(category_data[['学校中文名', '专业中文名', '项目时长']].head(20).to_string())
                    if len(category_data) > 20:
                        print(f"... 还有 {len(category_data) - 20} 个专业")
                else:
                    print("未找到相关专业大类")

            elif choice == '3':
                region = input("请输入地区: ").strip()
                region_data = df[df['学校所在地区'].str.contains(region, na=False)]
                if not region_data.empty:
                    print(f"\n{region} 地区的学校:")
                    schools = region_data['学校中文名'].unique()
                    for school in schools:
                        count = len(region_data[region_data['学校中文名'] == school])
                        print(f"{school}: {count} 个专业")
                else:
                    print("未找到相关地区")

            elif choice == '4':
                # 查看QS排名前50的学校
                if '学校排名(QS25)' in df.columns:
                    # 转换排名为数字类型进行排序
                    df_copy = df.copy()
                    df_copy['排名数字'] = pd.to_numeric(df_copy['学校排名(QS25)'], errors='coerce')
                    top50 = df_copy[df_copy['排名数字'] <= 50].dropna(subset=['排名数字'])

                    if not top50.empty:
                        print("\nQS排名前50的学校及其专业数量:")
                        school_counts = top50.groupby(['学校中文名', '学校排名(QS25)']).size().reset_index(name='专业数量')
                        school_counts = school_counts.sort_values('学校排名(QS25)')
                        for _, row in school_counts.iterrows():
                            print(f"排名{row['学校排名(QS25)']} - {row['学校中文名']}: {row['专业数量']} 个专业")
                    else:
                        print("未找到QS排名前50的学校数据")
                else:
                    print("数据中没有QS排名信息")

            elif choice == '5':
                print("退出程序")
                break
            else:
                print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
