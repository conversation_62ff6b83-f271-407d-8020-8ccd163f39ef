{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import re\n", "import json\n", "import pandas as pd\n", "from pathlib import Path\n", "\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import wordninja\n", "from thefuzz import fuzz, process"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 数据检查和清洗"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### JSON文本数据转CSV"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["def extract_json_to_csv():\n", "    # 定义数据文件夹路径\n", "    data_folder = Path(\"专业库(结构化)\")\n", "    \n", "    # 存储所有提取的数据\n", "    data_list = []\n", "    \n", "    # 统计文件总数和处理进度\n", "    total_files = len([f for f in os.listdir(data_folder) if f.endswith('.txt')])\n", "    processed_files = 0\n", "    \n", "    # 记录处理错误的文件\n", "    error_files = []\n", "    \n", "    print(f\"开始处理，共发现 {total_files} 个txt文件\")\n", "    \n", "    # 遍历文件夹中的所有txt文件\n", "    for filename in os.listdir(data_folder):\n", "        if filename.endswith('.txt'):\n", "            file_path = data_folder / filename\n", "            \n", "            try:\n", "                # 读取文件内容\n", "                with open(file_path, 'r', encoding='utf-8') as file:\n", "                    content = file.read()\n", "                \n", "                # 解析JSON\n", "                data = json.loads(content)\n", "                \n", "                # 将数据添加到列表\n", "                data_list.append(data)\n", "                \n", "            except Exception as e:\n", "                # 记录处理失败的文件\n", "                error_files.append((filename, str(e)))\n", "            \n", "            # 更新处理进度\n", "            processed_files += 1\n", "            if processed_files % 100 == 0:\n", "                print(f\"已处理 {processed_files}/{total_files} 个文件\")\n", "    \n", "    # 如果有数据，则创建DataFrame并导出为CSV\n", "    if data_list:\n", "        # 创建DataFrame\n", "        df = pd.DataFrame(data_list)\n", "        \n", "        # 将专业代码列转换为整数类型(先处理可能的空值或无效值)\n", "        df['专业代码'] = pd.to_numeric(df['专业代码'], errors='coerce').fillna(0).astype(int)\n", "        \n", "        # 导出为CSV\n", "        output_file = \"专业数据库（未清理）.csv\"\n", "        df.to_csv(output_file, index=False, encoding='utf-8-sig')\n", "        \n", "        print(f\"\\n数据提取完成! 共处理 {processed_files} 个文件，成功导入 {len(data_list)} 条记录\")\n", "        print(f\"数据已保存至: {output_file}\")\n", "    else:\n", "        print(\"未提取到任何有效数据\")\n", "    \n", "    # 输出错误信息\n", "    if error_files:\n", "        print(f\"\\n处理过程中有 {len(error_files)} 个文件出错:\")\n", "        for file, error in error_files[:10]:  # 只显示前10个错误\n", "            print(f\"  - {file}: {error}\")\n", "        if len(error_files) > 10:\n", "            print(f\"  ... 等共 {len(error_files)} 个错误文件\")"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["开始处理，共发现 11012 个txt文件\n", "已处理 100/11012 个文件\n", "已处理 200/11012 个文件\n", "已处理 300/11012 个文件\n", "已处理 400/11012 个文件\n", "已处理 500/11012 个文件\n", "已处理 600/11012 个文件\n", "已处理 700/11012 个文件\n", "已处理 800/11012 个文件\n", "已处理 900/11012 个文件\n", "已处理 1000/11012 个文件\n", "已处理 1100/11012 个文件\n", "已处理 1200/11012 个文件\n", "已处理 1300/11012 个文件\n", "已处理 1400/11012 个文件\n", "已处理 1500/11012 个文件\n", "已处理 1600/11012 个文件\n", "已处理 1700/11012 个文件\n", "已处理 1800/11012 个文件\n", "已处理 1900/11012 个文件\n", "已处理 2000/11012 个文件\n", "已处理 2100/11012 个文件\n", "已处理 2200/11012 个文件\n", "已处理 2300/11012 个文件\n", "已处理 2400/11012 个文件\n", "已处理 2500/11012 个文件\n", "已处理 2600/11012 个文件\n", "已处理 2700/11012 个文件\n", "已处理 2800/11012 个文件\n", "已处理 2900/11012 个文件\n", "已处理 3000/11012 个文件\n", "已处理 3100/11012 个文件\n", "已处理 3200/11012 个文件\n", "已处理 3300/11012 个文件\n", "已处理 3400/11012 个文件\n", "已处理 3500/11012 个文件\n", "已处理 3600/11012 个文件\n", "已处理 3700/11012 个文件\n", "已处理 3800/11012 个文件\n", "已处理 3900/11012 个文件\n", "已处理 4000/11012 个文件\n", "已处理 4100/11012 个文件\n", "已处理 4200/11012 个文件\n", "已处理 4300/11012 个文件\n", "已处理 4400/11012 个文件\n", "已处理 4500/11012 个文件\n", "已处理 4600/11012 个文件\n", "已处理 4700/11012 个文件\n", "已处理 4800/11012 个文件\n", "已处理 4900/11012 个文件\n", "已处理 5000/11012 个文件\n", "已处理 5100/11012 个文件\n", "已处理 5200/11012 个文件\n", "已处理 5300/11012 个文件\n", "已处理 5400/11012 个文件\n", "已处理 5500/11012 个文件\n", "已处理 5600/11012 个文件\n", "已处理 5700/11012 个文件\n", "已处理 5800/11012 个文件\n", "已处理 5900/11012 个文件\n", "已处理 6000/11012 个文件\n", "已处理 6100/11012 个文件\n", "已处理 6200/11012 个文件\n", "已处理 6300/11012 个文件\n", "已处理 6400/11012 个文件\n", "已处理 6500/11012 个文件\n", "已处理 6600/11012 个文件\n", "已处理 6700/11012 个文件\n", "已处理 6800/11012 个文件\n", "已处理 6900/11012 个文件\n", "已处理 7000/11012 个文件\n", "已处理 7100/11012 个文件\n", "已处理 7200/11012 个文件\n", "已处理 7300/11012 个文件\n", "已处理 7400/11012 个文件\n", "已处理 7500/11012 个文件\n", "已处理 7600/11012 个文件\n", "已处理 7700/11012 个文件\n", "已处理 7800/11012 个文件\n", "已处理 7900/11012 个文件\n", "已处理 8000/11012 个文件\n", "已处理 8100/11012 个文件\n", "已处理 8200/11012 个文件\n", "已处理 8300/11012 个文件\n", "已处理 8400/11012 个文件\n", "已处理 8500/11012 个文件\n", "已处理 8600/11012 个文件\n", "已处理 8700/11012 个文件\n", "已处理 8800/11012 个文件\n", "已处理 8900/11012 个文件\n", "已处理 9000/11012 个文件\n", "已处理 9100/11012 个文件\n", "已处理 9200/11012 个文件\n", "已处理 9300/11012 个文件\n", "已处理 9400/11012 个文件\n", "已处理 9500/11012 个文件\n", "已处理 9600/11012 个文件\n", "已处理 9700/11012 个文件\n", "已处理 9800/11012 个文件\n", "已处理 9900/11012 个文件\n", "已处理 10000/11012 个文件\n", "已处理 10100/11012 个文件\n", "已处理 10200/11012 个文件\n", "已处理 10300/11012 个文件\n", "已处理 10400/11012 个文件\n", "已处理 10500/11012 个文件\n", "已处理 10600/11012 个文件\n", "已处理 10700/11012 个文件\n", "已处理 10800/11012 个文件\n", "已处理 10900/11012 个文件\n", "已处理 11000/11012 个文件\n", "\n", "数据提取完成! 共处理 11012 个文件，成功导入 11012 条记录\n", "数据已保存至: 专业库数据（未清理）.csv\n"]}], "source": ["extract_json_to_csv() "]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["数据集大小: 11012行, 16列\n", "\n", "数据类型:\n", "学校中文名    object\n", "学校英文名    object\n", "专业中文名    object\n", "专业英文名    object\n", "专业大类     object\n", "专业方向     object\n", "所在学院     object\n", "入学时间     object\n", "项目时长     object\n", "项目官网     object\n", "培养目标     object\n", "申请要求     object\n", "语言要求     object\n", "申请时间     object\n", "课程设置     object\n", "专业代码      int64\n", "dtype: object\n"]}], "source": ["# 读取CSV文件\n", "df = pd.read_csv(\"专业数据库（未清理）.csv\")\n", "\n", "# 基本信息\n", "print(f\"数据集大小: {df.shape[0]}行, {df.shape[1]}列\")\n", "\n", "# 检查数据类型\n", "print(\"\\n数据类型:\")\n", "print(df.dtypes)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 补充抽取学费字段"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["开始提取项目学费信息...\n", "已处理 2000 个文件...\n", "已处理 4000 个文件...\n", "已处理 6000 个文件...\n", "已处理 8000 个文件...\n", "已处理 10000 个文件...\n", "\n", "处理完成！\n", "总文件数: 11012\n", "包含学费信息的文件数: 10968\n", "未找到学费信息的文件数: 44\n"]}], "source": ["# 定义数据文件夹路径\n", "data_folder = Path(\"专业库(清理后)\")\n", "\n", "# 存储提取结果的列表\n", "tuition_data = []\n", "\n", "# 正则表达式：匹配\"项目学费\"和\"项目官网\"之间的内容\n", "tuition_pattern = re.compile(r'项目学费(.*?)项目官网', re.DOTALL)\n", "\n", "# 从文件名提取专业代码的正则表达式\n", "code_pattern = re.compile(r'majr_(\\d+)\\.txt')\n", "\n", "# 统计信息\n", "total_files = 0\n", "files_with_tuition = 0\n", "files_without_tuition = 0\n", "error_files = []\n", "\n", "print(\"开始提取项目学费信息...\")\n", "\n", "# 遍历文件夹中的所有txt文件\n", "for filename in os.listdir(data_folder):\n", "    if filename.endswith('.txt') and filename.startswith('majr_'):\n", "        total_files += 1\n", "        file_path = data_folder / filename\n", "        \n", "        try:\n", "            # 从文件名提取专业代码\n", "            code_match = code_pattern.match(filename)\n", "            if code_match:\n", "                major_code = int(code_match.group(1))\n", "            else:\n", "                print(f\"无法从文件名 {filename} 提取专业代码\")\n", "                continue\n", "            \n", "            # 读取文件内容\n", "            with open(file_path, 'r', encoding='utf-8') as file:\n", "                content = file.read()\n", "            \n", "            # 提取学费信息\n", "            tuition_match = tuition_pattern.search(content)\n", "            if tuition_match:\n", "                tuition_info = tuition_match.group(1).strip()\n", "                tuition_data.append({\n", "                    '专业代码': major_code,\n", "                    '项目学费': tuition_info\n", "                })\n", "                files_with_tuition += 1\n", "            else:\n", "                # 记录没有找到学费信息的文件\n", "                tuition_data.append({\n", "                    '专业代码': major_code,\n", "                    '项目学费': None\n", "                })\n", "                files_without_tuition += 1\n", "                \n", "        except Exception as e:\n", "            error_files.append((filename, str(e)))\n", "            \n", "        # 每处理2000个文件输出一次进度\n", "        if total_files % 2000 == 0:\n", "            print(f\"已处理 {total_files} 个文件...\")\n", "\n", "# 创建DataFrame\n", "df_tuition = pd.DataFrame(tuition_data)\n", "\n", "# 输出统计信息\n", "print(f\"\\n处理完成！\")\n", "print(f\"总文件数: {total_files}\")\n", "print(f\"包含学费信息的文件数: {files_with_tuition}\")\n", "print(f\"未找到学费信息的文件数: {files_without_tuition}\")\n", "\n", "if error_files:\n", "    print(f\"\\n处理出错的文件数: {len(error_files)}\")\n", "    for file, error in error_files[:5]:  # 只显示前5个错误\n", "        print(f\"  - {file}: {error}\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["被完全清空的记录数: 0\n"]}], "source": ["# 定义需要去除的关键词列表\n", "keywords_to_remove = ['面试形式', '项目规模', '平均', '就业比例', '男女比例', '国际学生']\n", "\n", "# 创建df_tuition的副本，避免修改原数据\n", "df_tuition_clean = df_tuition.copy()\n", "\n", "# 定义清理函数\n", "def clean_tuition_info(tuition_str):\n", "    if pd.isna(tuition_str) or tuition_str is None:\n", "        return None\n", "    \n", "    # 转换为字符串\n", "    tuition_str = str(tuition_str)\n", "    \n", "    # 1. 去除关键词及其之后的所有内容\n", "    for keyword in keywords_to_remove:\n", "        # 查找关键词的位置\n", "        pos = tuition_str.find(keyword)\n", "        if pos != -1:\n", "            # 截取关键词之前的内容\n", "            tuition_str = tuition_str[:pos]\n", "    \n", "    # 2. 去除所有引号（中英文）和逗号\n", "    # 去除各种引号：双引号、单引号、中文引号\n", "    tuition_str = tuition_str.replace('\"', '')  # 英文双引号\n", "    tuition_str = tuition_str.replace(\"'\", '')  # 英文单引号\n", "    tuition_str = tuition_str.replace('\"', '')  # 中文左双引号\n", "    tuition_str = tuition_str.replace('\"', '')  # 中文右双引号\n", "    tuition_str = tuition_str.replace(''', '')  # 中文左单引号\n", "    tuition_str = tuition_str.replace(''', '')  # 中文右单引号\n", "    tuition_str = tuition_str.replace(',', '')  # 英文逗号\n", "    tuition_str = tuition_str.replace('，', '')  # 中文逗号\n", "    \n", "    # 3. 去除首尾空格\n", "    tuition_str = tuition_str.strip()\n", "    \n", "    # 如果清理后为空字符串，返回None\n", "    if not tuition_str:\n", "        return None\n", "    \n", "    return tuition_str\n", "\n", "# 应用清理函数\n", "df_tuition_clean['项目学费'] = df_tuition_clean['项目学费'].apply(clean_tuition_info)\n", "\n", "print(f\"被完全清空的记录数: {(df_tuition['项目学费'].notna() & df_tuition_clean['项目学费'].isna()).sum()}\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "结果已保存至: 专业学费信息.csv\n"]}], "source": ["# 保存结果到CSV文件\n", "# output_file = \"专业学费信息.csv\"\n", "# df_tuition_clean.to_csv(output_file, index=False, encoding='utf-8-sig')\n", "# print(f\"\\n结果已保存至: {output_file}\")"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["合并后df_merged的形状: (11012, 17)\n", "\n", "成功匹配到学费信息的记录数: 10968\n", "未匹配到学费信息的记录数: 44\n"]}], "source": ["# 读取专业学费信息\n", "df_tuition_clean = pd.read_csv(\"专业学费信息.csv\")\n", "\n", "# 假设你的主数据框是df，进行合并\n", "# 使用左连接，保留df中的所有记录\n", "df= df.merge(\n", "    df_tuition_clean[['专业代码', '项目学费']],  # 只选择需要的列\n", "    on='专业代码',  # 根据专业代码进行合并\n", "    how='left',  # 左连接，保留df中的所有记录\n", "    suffixes=('', '_new')  # 如果有重复列名，添加后缀\n", ")\n", "\n", "# 检查合并结果\n", "print(f\"合并后df_merged的形状: {df.shape}\")\n", "\n", "# 统计合并情况\n", "merged_count = df['项目学费'].notna().sum()\n", "print(f\"\\n成功匹配到学费信息的记录数: {merged_count}\")\n", "print(f\"未匹配到学费信息的记录数: {df['项目学费'].isna().sum()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### “未提供”值的处理和空值检查\n", "LLM在抽取时会把一些不存在的字段填为“未提供"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "含有'未提供'值的字段统计:\n", "所在学院: 2090行 (18.98%)\n", "入学时间: 15行 (0.14%)\n", "项目时长: 68行 (0.62%)\n", "项目官网: 4行 (0.04%)\n", "培养目标: 2006行 (18.22%)\n", "申请要求: 287行 (2.61%)\n", "语言要求: 31行 (0.28%)\n", "申请时间: 586行 (5.32%)\n"]}], "source": ["# 查看\"未提供\"值的情况\n", "print(\"\\n含有'未提供'值的字段统计:\")\n", "for col in df.columns:\n", "    if df[col].dtype == 'object':  # 只检查文本类型的列\n", "        undefined_count = df[col].str.contains('未提供', na=False).sum()\n", "        if undefined_count > 0:\n", "            undefined_percent = (undefined_count / len(df)) * 100\n", "            print(f\"{col}: {undefined_count}行 ({undefined_percent:.2f}%)\")\n"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "替换后，各字段中仍值为'未提供'的记录数：\n", "所有'未提供'值已成功替换为空值\n"]}], "source": ["# 替换所有字段中值为\"未提供\"的为空值\n", "for col in df.columns:\n", "    if df[col].dtype == 'object':  # 只处理文本类型的列\n", "        df[col] = df[col].replace('未提供', None)\n", "\n", "# 替换后检查结果\n", "undefined_counts_after = {}\n", "for col in df.columns:\n", "    if df[col].dtype == 'object':\n", "        undefined_count = df[col].eq('未提供').sum()\n", "        if undefined_count > 0:\n", "            undefined_counts_after[col] = undefined_count\n", "\n", "print(\"\\n替换后，各字段中仍值为'未提供'的记录数：\")\n", "if undefined_counts_after:\n", "    for col, count in undefined_counts_after.items():\n", "        print(f\"{col}: {count}行\")\n", "else:\n", "    print(\"所有'未提供'值已成功替换为空值\")"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "各字段空值数量:\n", "学校中文名       0\n", "学校英文名       0\n", "专业中文名       0\n", "专业英文名       0\n", "专业大类        0\n", "专业方向        0\n", "所在学院     2090\n", "入学时间       15\n", "项目时长       68\n", "项目官网        4\n", "培养目标     2006\n", "申请要求      287\n", "语言要求       25\n", "申请时间      582\n", "课程设置     1319\n", "专业代码        0\n", "项目学费       44\n", "dtype: int64\n"]}], "source": ["# 检查空值情况\n", "null_counts = df.isnull().sum()\n", "print(\"\\n各字段空值数量:\")\n", "print(null_counts)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 部分美校“课程设置”中混入“顾问解析”的清理"]}, {"cell_type": "markdown", "metadata": {}, "source": ["原始网页本来section part4是课程设置，但这些美校的part4是顾问解析，包括了课程设置、录取、就业等方面的信息...\n"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["课程设置中包含'顾问解析'的记录数量: 784行\n", "\n", "部分包含'顾问解析'的课程设置内容示例:\n", "示例1: 顾问解析哥伦比亚大学商业分析项目前身是工学院运筹学项目下的一个分支，于2018年秋季独立出来，招收第一届学生。课程设置：哥大MSBA的课程主要涉及建模技术和数据科学工具，以帮助企业利用数据做出更好的决...\n", "示例2: 顾问解析加州大学洛杉矶分校商业分析项目隶属于Anderson商学院，课程涵盖了SQL/R/Python等BA所需的工作技能，也涉及了广泛的BA应用领域，包括但不限于精准营销、运营分析、Healthca...\n", "示例3: 顾问解析麻省理工大学商业分析项目隶属于Sloan管理学院，致力于“用数据创造价值”，专为想要从事DataScience工作的学生，亦或是想要寻求职业晋升的工程师和数学家等量身打造。课程设置：课程是技术...\n", "\n", "包含'顾问解析'的记录的学校分布(前10所):\n", "学校中文名\n", "哥伦比亚大学        44\n", "南加州大学         41\n", "纽约大学          38\n", "康奈尔大学         30\n", "波士顿大学         27\n", "约翰霍普金斯大学      26\n", "杜克大学          25\n", "宾夕法尼亚大学       23\n", "卡内基梅隆大学       22\n", "密歇根大学安娜堡分校    22\n", "Name: count, dtype: int64\n"]}], "source": ["# 查找\"课程设置\"字段包含\"顾问解析\"的行\n", "advisor_analysis_rows = df[df['课程设置'].str.contains('顾问解析', na=False)]\n", "\n", "# 统计这些行的数量\n", "print(f\"课程设置中包含'顾问解析'的记录数量: {len(advisor_analysis_rows)}行\")\n", "\n", "# 查看部分匹配行的内容示例\n", "print(\"\\n部分包含'顾问解析'的课程设置内容示例:\")\n", "for i, content in enumerate(advisor_analysis_rows['课程设置'].head(3)):\n", "    print(f\"示例{i+1}: {content[:100]}...\") # 只显示前100个字符\n", "\n", "# 显示这些记录的学校和专业分布\n", "print(\"\\n包含'顾问解析'的记录的学校分布(前10所):\")\n", "print(advisor_analysis_rows['学校中文名'].value_counts().head(10))"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["课程设置列中包含'课程设置'的行数: 463行\n", "课程设置列中包含'课程设置：'的行数: 406行\n", "课程设置列中包含'课程设置:'的行数: 1行\n", "仅包含'课程设置'但不包含后续冒号的行数: 56行\n", "\n", "在包含'顾问解析'的记录中的比例:\n", "包含'课程设置'的比例: 59.06%\n", "\n", "存在'课程设置'出现多次的行数: 62行\n"]}], "source": ["# 确保advisor_analysis_rows变量仍然可用，如果之前的会话已关闭，需要重新定义\n", "if 'advisor_analysis_rows' not in locals():\n", "    advisor_analysis_rows = df[df['课程设置'].str.contains('顾问解析', na=False)].copy()\n", "    print(f\"重新获取包含'顾问解析'的记录，共{len(advisor_analysis_rows)}行\")\n", "\n", "# 检测包含\"课程设置\"的行数\n", "contains_course_setup = advisor_analysis_rows[advisor_analysis_rows['课程设置'].str.contains('课程设置', na=False)]\n", "print(f\"课程设置列中包含'课程设置'的行数: {len(contains_course_setup)}行\")\n", "\n", "# 检测包含\"课程设置：\"的行数（中文冒号）\n", "contains_course_setup_cn_colon = advisor_analysis_rows[advisor_analysis_rows['课程设置'].str.contains('课程设置：', na=False)]\n", "print(f\"课程设置列中包含'课程设置：'的行数: {len(contains_course_setup_cn_colon)}行\")\n", "\n", "# 检测包含\"课程设置:\"的行数（英文冒号）\n", "contains_course_setup_en_colon = advisor_analysis_rows[advisor_analysis_rows['课程设置'].str.contains('课程设置:', na=False)]\n", "print(f\"课程设置列中包含'课程设置:'的行数: {len(contains_course_setup_en_colon)}行\")\n", "\n", "# 查看包含\"课程设置\"字符串但不包含\"课程设置：\"或\"课程设置:\"的记录\n", "contains_only_text = contains_course_setup[\n", "    ~contains_course_setup['课程设置'].str.contains('课程设置：', na=False) & \n", "    ~contains_course_setup['课程设置'].str.contains('课程设置:', na=False)\n", "]\n", "print(f\"仅包含'课程设置'但不包含后续冒号的行数: {len(contains_only_text)}行\")\n", "\n", "# 查看所有三种情况的比例\n", "print(\"\\n在包含'顾问解析'的记录中的比例:\")\n", "print(f\"包含'课程设置'的比例: {len(contains_course_setup) / len(advisor_analysis_rows) * 100:.2f}%\")\n", "# print(f\"包含'课程设置：'的比例: {len(contains_course_setup_cn_colon) / len(advisor_analysis_rows) * 100:.2f}%\")\n", "# print(f\"包含'课程设置:'的比例: {len(contains_course_setup_en_colon) / len(advisor_analysis_rows) * 100:.2f}%\")\n", "\n", "\n", "# 查看是否存在\"课程设置\"出现多次的情况\n", "multiple_occurrences = []\n", "for idx, row in contains_course_setup.iterrows():\n", "    text = row['课程设置']\n", "    count = text.count('课程设置')\n", "    if count > 1:\n", "        multiple_occurrences.append((idx, count))\n", "\n", "if multiple_occurrences:\n", "    print(f\"\\n存在'课程设置'出现多次的行数: {len(multiple_occurrences)}行\")\n", "else:\n", "    print(\"\\n没有'课程设置'出现多次的情况\")"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["替换后，课程设置中仍包含'顾问解析'的记录数量: 0行\n", "替换后，课程设置为空值的记录总数: 2103行\n", "其中因包含'顾问解析'而被替换的记录数: 784行\n"]}], "source": ["# # 替换这些行的\"课程设置\"为空值\n", "# df.loc[df['课程设置'].str.contains('顾问解析', na=False), '课程设置'] = None\n", "\n", "# # 确认替换结果\n", "# remaining_advisor = df[df['课程设置'].str.contains('顾问解析', na=False)]\n", "# print(f\"替换后，课程设置中仍包含'顾问解析'的记录数量: {len(remaining_advisor)}行\")\n", "\n", "# # 查看替换后的空值统计\n", "# null_count_after = df['课程设置'].isnull().sum()\n", "# print(f\"替换后，课程设置为空值的记录总数: {null_count_after}行\")\n", "# print(f\"其中因包含'顾问解析'而被替换的记录数: {len(advisor_analysis_rows)}行\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["先只去掉“顾问解析”几个字吧"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["'顾问解析'出现次数分布:\n", "顾问解析_出现次数\n", "1    784\n", "Name: count, dtype: int64\n", "\n", "'顾问解析'重复出现的行数: 0行\n", "\n", "分析'顾问解析'的位置:\n", "'顾问解析'出现在开头的行数: 784行 (100.00%)\n", "'顾问解析'出现在结尾的行数: 0行 (0.00%)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_47404\\2453314970.py:7: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  advisor_analysis_rows['顾问解析_出现次数'] = advisor_analysis_rows['课程设置'].apply(lambda x: x.count('顾问解析') if isinstance(x, str) else 0)\n"]}], "source": ["# 确保advisor_analysis_rows变量仍然可用，如果不可用则重新获取\n", "if 'advisor_analysis_rows' not in locals():\n", "    advisor_analysis_rows = df[df['课程设置'].str.contains('顾问解析', na=False)].copy()\n", "    print(f\"重新获取包含'顾问解析'的记录，共{len(advisor_analysis_rows)}行\")\n", "\n", "# 检查\"顾问解析\"在每行中出现的次数\n", "advisor_analysis_rows['顾问解析_出现次数'] = advisor_analysis_rows['课程设置'].apply(lambda x: x.count('顾问解析') if isinstance(x, str) else 0)\n", "\n", "# 统计\"顾问解析\"出现次数的分布\n", "occurrences_count = advisor_analysis_rows['顾问解析_出现次数'].value_counts().sort_index()\n", "print(\"'顾问解析'出现次数分布:\")\n", "print(occurrences_count)\n", "\n", "# 列出重复出现\"顾问解析\"的行\n", "multiple_occurrences = advisor_analysis_rows[advisor_analysis_rows['顾问解析_出现次数'] > 1]\n", "print(f\"\\n'顾问解析'重复出现的行数: {len(multiple_occurrences)}行\")\n", "\n", "# 显示部分重复出现\"顾问解析\"的示例\n", "if len(multiple_occurrences) > 0:\n", "    print(\"\\n部分'顾问解析'重复出现的示例:\")\n", "    for i, (idx, row) in enumerate(multiple_occurrences.head(3).iterrows()):\n", "        print(f\"示例{i+1}: '顾问解析'出现{row['顾问解析_出现次数']}次\")\n", "        print(f\"学校: {row['学校中文名']}, 专业: {row['专业中文名']}\")\n", "        print(row['课程设置'][:200] + \"...\" if len(row['课程设置']) > 200 else row['课程设置'])\n", "        print(\"-\" * 50)\n", "\n", "# 分析\"顾问解析\"在文本中的位置\n", "print(\"\\n分析'顾问解析'的位置:\")\n", "\n", "# 检查\"顾问解析\"是否出现在开头\n", "starts_with_advisor = advisor_analysis_rows[advisor_analysis_rows['课程设置'].str.startswith('顾问解析')]\n", "print(f\"'顾问解析'出现在开头的行数: {len(starts_with_advisor)}行 ({len(starts_with_advisor)/len(advisor_analysis_rows)*100:.2f}%)\")\n", "\n", "# 检查\"顾问解析\"是否出现在结尾\n", "ends_with_advisor = advisor_analysis_rows[advisor_analysis_rows['课程设置'].str.endswith('顾问解析')]\n", "print(f\"'顾问解析'出现在结尾的行数: {len(ends_with_advisor)}行 ({len(ends_with_advisor)/len(advisor_analysis_rows)*100:.2f}%)\")\n"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["清洗后仍包含'顾问解析'的行数: 0行\n", "\n", "将清洗结果应用回原始数据...\n", "更新后，原始DataFrame中仍包含'顾问解析'的行数: 0行\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_47404\\3895606321.py:3: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  advisor_analysis_rows['原始课程设置'] = advisor_analysis_rows['课程设置'].copy()\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_47404\\3895606321.py:6: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  advisor_analysis_rows['课程设置_清洗后'] = advisor_analysis_rows['课程设置'].str.replace('顾问解析', '')\n"]}], "source": ["\n", "# 从\"课程设置\"列中去除\"顾问解析\"字符串\n", "# 先创建原始文本的副本\n", "advisor_analysis_rows['原始课程设置'] = advisor_analysis_rows['课程设置'].copy()\n", "\n", "# 去除\"顾问解析\"字符串\n", "advisor_analysis_rows['课程设置_清洗后'] = advisor_analysis_rows['课程设置'].str.replace('顾问解析', '')\n", "\n", "# 检查清洗后还有多少行包含\"顾问解析\"\n", "still_contains_advisor = advisor_analysis_rows[advisor_analysis_rows['课程设置_清洗后'].str.contains('顾问解析', na=False)]\n", "print(f\"清洗后仍包含'顾问解析'的行数: {len(still_contains_advisor)}行\")\n", "\n", "# 如果你想将清洗后的结果应用回原始DataFrame\n", "print(\"\\n将清洗结果应用回原始数据...\")\n", "for idx, row in advisor_analysis_rows.iterrows():\n", "    # 在原始DataFrame中找到对应行并更新\n", "    if idx in df.index and '顾问解析' in str(df.loc[idx, '课程设置']):\n", "        df.loc[idx, '课程设置'] = row['课程设置_清洗后']\n", "\n", "# 确认更新结果\n", "updated_rows = df[df['课程设置'].str.contains('顾问解析', na=False)]\n", "print(f\"更新后，原始DataFrame中仍包含'顾问解析'的行数: {len(updated_rows)}行\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 官方网址清理..."]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['学校中文名', '学校英文名', '专业中文名', '专业英文名', '专业大类', '专业方向', '所在学院', '入学时间',\n", "       '项目时长', '项目官网', '培养目标', '申请要求', '语言要求', '申请时间', '课程设置', '专业代码', '项目学费'],\n", "      dtype='object')"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["项目官网中含有中文的记录数: 62\n", "占总记录数的比例: 0.56%\n", "\n", "含有中文的项目官网示例:\n", "--------------------------------------------------------------------------------\n", "专业代码: 51047\n", "学校: 利兹大学 - 结构工程（工程）理学硕士\n", "原网址: https://courses.leeds.ac.uk/f094/structural-engineering-msc-eng-点击前往\n", "----------------------------------------\n", "专业代码: 61672\n", "学校: 纽卡斯尔大学 - 建筑学硕士\n", "原网址: https://www.ncl.ac.uk/postgraduate/degrees/5843f/点击前往\n", "----------------------------------------\n", "专业代码: 62758\n", "学校: 牛津大学 - 英语世界文学研究硕士\n", "原网址: https://www.ox.ac.uk/admissions/graduate/courses/mst-world-literatures-english点击前往\n", "----------------------------------------\n", "专业代码: 63060\n", "学校: 澳门大学 - 欧盟法，国际法和比较法（英文）法学硕士\n", "原网址: https://fll.um.edu.mo/programme/master-and-postgraduate/master-and-postgraduate-of-law-in-european-union-law-english-international-and-comparative-law-english/点击前往\n", "----------------------------------------\n", "专业代码: 63063\n", "学校: 澳门大学 - 政法学（葡文）法学硕士\n", "原网址: https://fll.um.edu.mo/programme/master-and-postgraduate/master-and-postgraduate-of-law-in-portuguese-language/点击前往\n", "----------------------------------------\n", "专业代码: 65493\n", "学校: 纽卡斯尔大学（英国） - 数据科学理学硕士\n", "原网址: https://www.ncl.ac.uk/postgraduate/degrees/5395f/点击前往\n", "----------------------------------------\n", "专业代码: 65685\n", "学校: 佐治亚理工学院 - 定量和计算金融硕士\n", "原网址: https://grad.gatech.edu/degree-programs/quantitative-and-computational-finance点击前往\n", "----------------------------------------\n", "专业代码: 65692\n", "学校: 福特汉姆大学 - 定量金融理学硕士\n", "原网址: https://www.fordham.edu/info/24010/quantitative_finance点击前往\n", "----------------------------------------\n", "专业代码: 65738\n", "学校: 卡内基梅隆大学 - 计算数据科学硕士\n", "原网址: https://mcds.cs.cmu.edu/learn-us-admission#:~:text=Applications%20for%20the%20MCDS%20program%20will%20open%20on,Thursday%2C%20November%2019%2C%202020%20点击前往\n", "----------------------------------------\n", "专业代码: 65778\n", "学校: 加州大学洛杉矶分校 - 计算机科学硕士\n", "原网址: https://www.cs.ucla.edu/graduate-admissions/#点击前往\n", "----------------------------------------\n", "专业代码: 66352\n", "学校: 纽卡斯尔大学（英国） - 数字商务理学硕士\n", "原网址: https://www.ncl.ac.uk/postgraduate/degrees/5124f/点击前往\n", "----------------------------------------\n", "专业代码: 66362\n", "学校: 纽卡斯尔大学 - 工商管理硕士\n", "原网址: https://www.ncl.ac.uk/postgraduate/degrees/5823f/点击前往\n", "----------------------------------------\n", "专业代码: 66770\n", "学校: 南安普顿大学 - 电影研究文学硕士\n", "原网址: https://www.southampton.ac.uk/courses/film-studies-masters-ma点击前往\n", "----------------------------------------\n", "专业代码: 66800\n", "学校: 牛津大学 - 伊斯兰艺术与建筑研究硕士\n", "原网址: https://www.ox.ac.uk/admissions/graduate/courses/mst-islamic-art-and-architecture点击前往\n", "----------------------------------------\n", "专业代码: 66818\n", "学校: 牛津大学 - 可持续发展、企业与环境理学硕士\n", "原网址: https://www.ox.ac.uk/admissions/graduate/courses/msc-sustainability-enterprise-environment点击前往\n", "----------------------------------------\n", "专业代码: 66896\n", "学校: 剑桥大学 - 世界历史哲学硕士\n", "原网址: https://www.postgraduate.study.cam.ac.uk/courses/directory/hihimpwhs点击前往\n", "----------------------------------------\n", "专业代码: 71585\n", "学校: 香港都会大学 - 工商管理硕士（中文）\n", "原网址: https://www.hkmu.edu.hk/ba/tc/mbacf/点击前往\n", "----------------------------------------\n", "专业代码: 71661\n", "学校: 雪城大学 - 高级媒体管理硕士\n", "原网址: https://newhouse.syracuse.edu/academics/advanced-media-management/masters点击前往\n", "----------------------------------------\n", "专业代码: 71669\n", "学校: 斯坦福大学 - 教育学硕士：国际比较教育与国际教育政策分析\n", "原网址: https://ed.stanford.edu/ice点击前往\n", "----------------------------------------\n", "专业代码: 71691\n", "学校: 约翰霍普金斯大学 - 教育科学硕士 - 国际教学和全球领导力队列\n", "原网址: https://education.jhu.edu/academics/msed-itgl/点击前往\n", "----------------------------------------\n", "专业代码: 71695\n", "学校: 伊利诺伊大学香槟分校 - 对外英语教学文学硕士\n", "原网址: https://linguistics.illinois.edu/academics/graduate-program/ma-teaching-english-second-language-matesl点击前往\n", "----------------------------------------\n", "专业代码: 71808\n", "学校: 麻省理工学院 - 机械工程理学硕士\n", "原网址: http://gradadmissions.mit.edu/programs/meche点击前往\n", "----------------------------------------\n", "专业代码: 71815\n", "学校: 杜克大学 - 机械工程与材料科学工学硕士\n", "原网址: https://mems.duke.edu/masters/degrees/meng点击前往\n", "----------------------------------------\n", "专业代码: 71870\n", "学校: 麻省理工学院 - 材料科学与工程理学硕士\n", "原网址: https://gradadmissions.mit.edu/programs/dmse点击前往\n", "----------------------------------------\n", "专业代码: 71944\n", "学校: 佐治亚理工学院 - 环境工程理学硕士\n", "原网址: https://grad.gatech.edu/degree-programs/environmental-engineering点击前往\n", "----------------------------------------\n", "专业代码: 71980\n", "学校: 明尼苏达大学双城分校 - 土木与地质工程理学硕士\n", "原网址: https://cse.umn.edu/cege点击前往\n", "----------------------------------------\n", "专业代码: 71987\n", "学校: 马里兰大学帕克分校 - 土木与环境工程工学硕士\n", "原网址: https://mage.umd.edu/civil-environmental点击前往\n", "----------------------------------------\n", "专业代码: 72047\n", "学校: 乔治华盛顿大学 - 计算机科学理学硕士\n", "原网址: https://www.cs.seas.gwu.edu/master-science-computer-science点击前往\n", "----------------------------------------\n", "专业代码: 72108\n", "学校: 布朗大学 - 社会数据分析理学硕士\n", "原网址: https://www.brown.edu/graduateprograms/social-analysis-and-research-scm点击前往\n", "----------------------------------------\n", "专业代码: 72109\n", "学校: 密歇根大学安娜堡分校 - 公共政策硕士\n", "原网址: https://fordschool.umich.edu/mpp-mpa/mpp点击前往\n", "----------------------------------------\n", "专业代码: 72144\n", "学校: 纽约大学 - 营销与零售科学理学硕士\n", "原网址: https://stern.shanghai.nyu.edu/en/program/ms-marketing-and-retail-science点击前往\n", "----------------------------------------\n", "专业代码: 72147\n", "学校: 维克森林大学 - 数学和统计硕士\n", "原网址: https://bulletin.wfu.edu/graduate/programs/degree-programs/mathematics-statistics/mathematics-statistics-ms/#点击前往\n", "----------------------------------------\n", "专业代码: 72396\n", "学校: 纽卡斯尔大学（英国） - 经济学与数据科学理学硕士\n", "原网址: https://www.ncl.ac.uk/postgraduate/degrees/5465f/点击前往\n", "----------------------------------------\n", "专业代码: 72397\n", "学校: 纽卡斯尔大学 - 行为与实验经济学理学硕士\n", "原网址: https://www.ncl.ac.uk/postgraduate/degrees/5466f/点击前往\n", "----------------------------------------\n", "专业代码: 72699\n", "学校: 卡内基梅隆大学 - 人机交互硕士\n", "原网址: https://hcii.cmu.edu/academics/mhci点击前往\n", "----------------------------------------\n", "专业代码: 72729\n", "学校: 南加州大学 - 东亚研究文学硕士\n", "原网址: https://dornsife.usc.edu/eascenter/masters/点击前往\n", "----------------------------------------\n", "专业代码: 72758\n", "学校: 北卡罗来纳大学教堂山分校 - 媒体与传播学文学硕士-理论和研究\n", "原网址: https://hussman.unc.edu/grad/matr点击前往\n", "----------------------------------------\n", "专业代码: 72777\n", "学校: 北卡罗来纳大学教堂山分校 - 社会工作硕士\n", "原网址: https://ssw.unc.edu/academics/msw-degree/点击前往\n", "----------------------------------------\n", "专业代码: 72944\n", "学校: 俄亥俄州立大学 - 消费者科学硕士\n", "原网址: https://ehe.osu.edu/graduate/consumer-sciences点击前往\n", "----------------------------------------\n", "专业代码: 72991\n", "学校: 华盛顿大学 - 计算语言学理学硕士\n", "原网址: https://www.compling.uw.edu/点击前往\n", "----------------------------------------\n", "专业代码: 73394\n", "学校: 伍伦贡大学 - 商学硕士\n", "原网址: https://coursefinder.uow.edu.au/information/index.html?course=master-business点击前往\n", "----------------------------------------\n", "专业代码: 73532\n", "学校: 耶鲁大学 - 环境管理硕士\n", "原网址: https://environment.yale.edu/academics/masters/mem点击前往\n", "----------------------------------------\n", "专业代码: 73565\n", "学校: 杜克大学 - 环境工程硕士\n", "原网址: https://cee.duke.edu/grad/masters/meng-environmental点击前往\n", "----------------------------------------\n", "专业代码: 73646\n", "学校: 加州大学洛杉矶分校 - 结构力学理学硕士\n", "原网址: https://www.cee.ucla.edu/structures/点击前往\n", "----------------------------------------\n", "专业代码: 73667\n", "学校: 乔治城大学 - 全球人类发展硕士\n", "原网址: https://grad.georgetown.edu/admissions/programs/global-human-development点击前往\n", "----------------------------------------\n", "专业代码: 73689\n", "学校: 南加州大学 - 应用数学文学硕士\n", "原网址: https://catalogue.usc.edu/preview_program.php?catoid=8&poid=7537点击前往\n", "----------------------------------------\n", "专业代码: 73731\n", "学校: 塔夫茨大学 - 化学工程理学硕士\n", "原网址: https://engineering.tufts.edu/chbe/prospective-students/masters/ms-chemical-engineering点击前往\n", "----------------------------------------\n", "专业代码: 73741\n", "学校: 加州大学欧文分校 - 土木与环境工程理学硕士\n", "原网址: https://engineering.uci.edu/dept/cee点击前往\n", "----------------------------------------\n", "专业代码: 73830\n", "学校: 佐治亚理工学院 - 物理理学硕士\n", "原网址: http://www.gradadmiss.gatech.edu/phys点击前往\n", "----------------------------------------\n", "专业代码: 73956\n", "学校: 明尼苏达大学双城分校 - 工商管理硕士\n", "原网址: https://onestop2.umn.edu/pcas/viewCatalogProgram.do?programID=7262点击前往\n", "----------------------------------------\n", "专业代码: 73964\n", "学校: 乔治华盛顿大学 - 公共政策硕士\n", "原网址: https://tspppa.gwu.edu/master-public-policy-mpp点击前往\n", "----------------------------------------\n", "专业代码: 73968\n", "学校: 弗吉尼亚大学 - 信息技术管理理学硕士\n", "原网址: https://www.commerce.virginia.edu/ms-mit点击前往\n", "----------------------------------------\n", "专业代码: 74087\n", "学校: 杨百翰大学 - 电子和计算机工程理学硕士\n", "原网址: https://gradstudies.byu.edu/electrical-computer-engineering-ms点击前往\n", "----------------------------------------\n", "专业代码: 74262\n", "学校: 考文垂大学 - 经济学与银行学理学硕士\n", "原网址: https://www.coventry.ac.uk/london/course-structure/pg/economics-banking-msc/点击前往\n", "----------------------------------------\n", "专业代码: 74611\n", "学校: 埃克塞特大学 - 电力与智能电网理学硕士\n", "原网址: https://www.exeter.ac.uk/study/postgraduate/courses/engineering/epsg/点击前往\n", "----------------------------------------\n", "专业代码: 74757\n", "学校: 纽卡斯尔大学（英国） - 商业分析理学硕士\n", "原网址: https://www.ncl.ac.uk/postgraduate/degrees/5492f/点击前往\n", "----------------------------------------\n", "专业代码: 74857\n", "学校: 剑桥大学 - 行星科学与宇宙生命哲学硕士\n", "原网址: https://www.postgraduate.study.cam.ac.uk/courses/directory/pcasmppsl点击前往\n", "----------------------------------------\n", "专业代码: 75164\n", "学校: 牛津大学 - 伊斯兰艺术与建筑哲学硕士\n", "原网址: https://www.ox.ac.uk/admissions/graduate/courses/mphil-islamic-art-and-architecture点击前往\n", "----------------------------------------\n", "专业代码: 75185\n", "学校: 南加州大学 - 地理信息科学与技术硕士\n", "原网址: https://gis.usc.edu/programs/masters-geographic-information-science-and-technology/点击前往\n", "----------------------------------------\n", "专业代码: 75326\n", "学校: 纽卡斯尔大学 - 医学统计理学硕士\n", "原网址: https://www.ncl.ac.uk/postgraduate/degrees/5519f/点击前往\n", "----------------------------------------\n", "专业代码: 75328\n", "学校: 纽卡斯尔大学 - 工程管理理学硕士\n", "原网址: https://www.ncl.ac.uk/postgraduate/degrees/5523f/点击前往\n", "----------------------------------------\n", "专业代码: 76090\n", "学校: 澳门大学 - 教育学哲学硕士（中文教育）\n", "原网址: https://fed.um.edu.mo/master-of-philosophy-in-education/master-of-philosophy-in-education-chinese-education/点击前往\n", "----------------------------------------\n"]}], "source": ["# 定义中文字符的正则表达式\n", "chinese_pattern = re.compile(r'[\\u4e00-\\u9fa5]+')\n", "\n", "# 查找项目官网中含有中文的行\n", "def contains_chinese(text):\n", "    \"\"\"检查文本是否包含中文字符\"\"\"\n", "    if pd.isna(text):\n", "        return False\n", "    return bool(chinese_pattern.search(str(text)))\n", "\n", "# 找出含有中文的网址\n", "chinese_urls_mask = df['项目官网'].apply(contains_chinese)\n", "chinese_urls_df = df[chinese_urls_mask].copy()\n", "\n", "print(f\"项目官网中含有中文的记录数: {chinese_urls_mask.sum()}\")\n", "print(f\"占总记录数的比例: {chinese_urls_mask.sum() / len(df) * 100:.2f}%\")\n", "\n", "# 展示含有中文的网址示例\n", "print(\"\\n含有中文的项目官网示例:\")\n", "print(\"-\" * 80)\n", "for idx, row in chinese_urls_df.iterrows():\n", "    print(f\"专业代码: {row['专业代码']}\")\n", "    print(f\"学校: {row['学校中文名']} - {row['专业中文名']}\")\n", "    print(f\"原网址: {row['项目官网']}\")\n", "    print(\"-\" * 40)"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "清理后仍含有'点击前往'的记录数: 0\n"]}], "source": ["# 去除\"点击前往\"字符串\n", "df['项目官网'] = df['项目官网'].str.replace('点击前往', '', regex=False)\n", "\n", "# 验证清理结果\n", "remaining_click_mask = df['项目官网'].str.contains('点击前往', na=False)\n", "print(f\"\\n清理后仍含有'点击前往'的记录数: {remaining_click_mask.sum()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 学校中英文名规范"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "即将处理 1788 条无空格的学校英文名\n", "\n", "处理后仍无空格的学校英文名数量: 0\n"]}], "source": ["def split_with_word<PERSON><PERSON>(text):\n", "    \"\"\"使用wordninja库分割英文单词\"\"\"\n", "    if pd.isna(text) or not text:\n", "        return text\n", "    \n", "    # wordninja返回单词列表\n", "    words = wordninja.split(text)\n", "    \n", "    return ' '.join(words)\n", "\n", "# # 测试函数\n", "# test_names = [\n", "#     \"TheUniversityofQueensland\",\n", "#     \"TheLondonSchoolofEconomicsandPoliticalScience\",\n", "#     \"UCLUniversityCollegeLondon\",\n", "#     \"NYUNewYorkUniversity\"\n", "# ]\n", "\n", "# print(\"\\n使用wordninja库的分割效果:\")\n", "# for name in test_names:\n", "#     print(f\"原始: {name}\")\n", "#     print(f\"分割后: {split_with_word<PERSON><PERSON>(name)}\")\n", "#     print(\"-\" * 40)\n", "\n", "# 统计处理前的情况\n", "no_space_mask = df['学校英文名'].notna() & (~df['学校英文名'].str.contains(' ', na=False))\n", "print(f\"\\n即将处理 {no_space_mask.sum()} 条无空格的学校英文名\")\n", "\n", "# 备份原始数据（可选）\n", "# df['学校英文名_原始'] = df['学校英文名'].copy()\n", "\n", "# 直接更新学校英文名列\n", "df.loc[no_space_mask, '学校英文名'] = df.loc[no_space_mask, '学校英文名'].apply(split_with_wordninja)\n", "\n", "# 验证处理结果\n", "remaining_no_space = df['学校英文名'].notna() & (~df['学校英文名'].str.contains(' ', na=False))\n", "print(f\"\\n处理后仍无空格的学校英文名数量: {remaining_no_space.sum()}\")"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["# 保存清洗后的数据\n", "# df = pd.read_csv(\"专业数据库（已清洗）.csv\")\n", "# df.to_csv(\"专业数据库（已清洗）.csv\", index=False, encoding='utf-8-sig')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 院校名称匹配"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["df = pd.read_csv(\"专业数据库（已清洗）.csv\")\n", "qsdf = pd.read_excel('2025-QS-Rankings.xlsx')"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["中文名对应多个英文名的情况:\n", "东北大学（美国）: Northeastern University (United States), Northeastern University(United States), Northeastern University(UnitedStates)\n", "伊利诺伊大学香槟分校: University of Illinois at Urbana Champaign, University of Illinois at Urbana-Champaign\n", "伍伦贡大学: University of Wollongong Australia, University of Wollongong\n", "伦敦大学亚非学院: SOAS, University of London, SOAS,University of London, SO AS University of London\n", "伦敦大学城市学院: City, University of London, City University of London\n", "伦敦大学皇家霍洛威学院: Royal Holloway, University of London, Royal Holloway University of London, RoyalHolloway, UniversityofLondon\n", "伦敦大学金史密斯学院: Goldsmiths, University of London, Goldsmiths University of London\n", "加州大学伯克利分校: University of California Berkeley, University of California, Berkeley\n", "加州大学圣地亚哥分校: University of California San Diego, University of California, San Diego\n", "加州大学戴维斯分校: University of California, Davis, University of California Davis\n", "加州大学欧文分校: University of California, Irvine, University of California Irvine\n", "加州大学洛杉矶分校: University of California Los Angeles, University of California, Los Angeles\n", "北卡罗来纳州立大学罗利分校: North Carolina State University—Raleigh, North Carolina State University Raleigh\n", "威斯康星大学麦迪逊分校: University of Wisconsin-Madison, University of Wisconsin Madison\n", "密歇根大学安娜堡分校: University of Michigan, Ann Arbor, University of Michigan Ann Arbor\n", "弗吉尼亚理工大学: Virginia Polytechnic Institute and State University Virginia Tech, Virginia Polytechnic Institute and State University (Virginia Tech)\n", "德州农工大学: Texas A&M University, Texas A M University\n", "德州大学达拉斯分校: University of Texas Dallas, University of Texas-Dallas\n", "新南威尔士大学: The University of New South Wales, The University of NewSouth Wales, The University of NewSouthWales\n", "明尼苏达大学双城分校: University of Minnesota-Twin Cities, University of Minnesota Twin Cities, University of Minnesota-TwinCities\n", "约克大学: University of York, University of York United Kingdom\n", "约克大学（英国）: University of York (United Kingdom), University of York United Kingdom, University of York(United Kingdom), University of York(UK)\n", "纽卡斯尔大学: Newcastle University, Newcastle University (United Kingdom), Newcastle University United Kingdom\n", "纽卡斯尔大学（澳大利亚）: The University of Newcastle, Australia, The University of Newcastle Australia\n", "纽卡斯尔大学（英国）: Newcastle University (United Kingdom), Newcastle University(United Kingdom)\n", "西北大学（美国）: Northwestern University (United States), Northwestern University(United States), Northwestern University(UnitedStates), Northwestern University\n", "香港城市大学: City University of Hong Kong, City University of HongKong\n", "香港大学: The University of Hong Kong, The University of HongKong\n", "马里兰大学帕克分校: University of Maryland, College Park, University of Maryland College Park\n", "\n", "英文名对应多个中文名的情况:\n", "Newcastle University (United Kingdom): 纽卡斯尔大学（英国）, 纽卡斯尔大学\n", "Northwestern University: 西北大学, 西北大学（美国）\n", "University of York United Kingdom: 约克大学（英国）, 约克大学\n"]}], "source": ["# 检查中文名对应多个英文名的情况\n", "one_to_many_zh = df.groupby('学校中文名')['学校英文名'].nunique()\n", "many_zh_names = one_to_many_zh[one_to_many_zh > 1].index.tolist()\n", "\n", "# 检查英文名对应多个中文名的情况\n", "one_to_many_en = df.groupby('学校英文名')['学校中文名'].nunique()\n", "many_en_names = one_to_many_en[one_to_many_en > 1].index.tolist()\n", "\n", "print(\"中文名对应多个英文名的情况:\")\n", "for zh_name in many_zh_names:\n", "    print(f\"{zh_name}: {', '.join(df[df['学校中文名'] == zh_name]['学校英文名'].unique())}\")\n", "\n", "print(\"\\n英文名对应多个中文名的情况:\")\n", "for en_name in many_en_names:\n", "    print(f\"{en_name}: {', '.join(df[df['学校英文名'] == en_name]['学校中文名'].unique())}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 中文名一对多处理"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'东北大学（美国）': 'Northeastern University',\n", " '伊利诺伊大学香槟分校': 'University of Illinois at Urbana-Champaign',\n", " '伍伦贡大学': 'University of Wollongong',\n", " '伦敦大学亚非学院': 'SOAS University of London ',\n", " '伦敦大学城市学院': 'City, University of London',\n", " '伦敦大学皇家霍洛威学院': 'Royal Holloway University of London',\n", " '伦敦大学金史密斯学院': 'Goldsmiths, University of London',\n", " '加州大学伯克利分校': 'University of California, Berkeley (UCB)',\n", " '加州大学圣地亚哥分校': 'University of California, San Diego (UCSD)',\n", " '加州大学戴维斯分校': 'University of California, Davis',\n", " '加州大学欧文分校': 'University of California, Irvine',\n", " '加州大学洛杉矶分校': 'University of California, Los Angeles (UCLA)',\n", " '北卡罗来纳州立大学罗利分校': 'North Carolina State University',\n", " '威斯康星大学麦迪逊分校': 'University of Wisconsin-Madison',\n", " '密歇根大学安娜堡分校': 'University of Michigan-Ann Arbor',\n", " '弗吉尼亚理工大学': 'Virginia Polytechnic Institute and State University',\n", " '德州农工大学': 'Texas A&M University',\n", " '德州大学达拉斯分校': 'University of Texas Dallas',\n", " '新南威尔士大学': 'The University of New South Wales (UNSW Sydney)',\n", " '明尼苏达大学双城分校': 'University of Minnesota Twin Cities',\n", " '约克大学': 'University of York',\n", " '约克大学（英国）': 'University of York',\n", " '纽卡斯尔大学': 'Newcastle University',\n", " '纽卡斯尔大学（澳大利亚）': 'The University of Newcastle, Australia (UON)',\n", " '纽卡斯尔大学（英国）': 'Newcastle University',\n", " '西北大学（美国）': 'Northwestern University',\n", " '香港城市大学': 'City University of Hong Kong',\n", " '香港大学': 'The University of Hong Kong',\n", " '马里兰大学帕克分校': 'University of Maryland, College Park'}"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["# 检查中文名对应多个英文名的情况\n", "one_to_many_zh = df.groupby('学校中文名')['学校英文名'].nunique()\n", "many_zh_names = one_to_many_zh[one_to_many_zh > 1].index.tolist()\n", "\n", "# 将英文名与QS中规范英文名进行匹配\n", "normalized_en_names = {}\n", "\n", "for zh_name in many_zh_names:\n", "    # 获取当前中文名对应的多个英文名\n", "    en_names = df[df['学校中文名'] == zh_name]['学校英文名'].unique().tolist()\n", "    \n", "    # 检查这些英文名中是否有与 qsdf 中完全符合的\n", "    exact_matches = [en_name for en_name in en_names if en_name in qsdf['Name'].values]\n", "    \n", "    if exact_matches:\n", "        # 如果有完全匹配的，则直接记录第一个完全匹配的结果\n", "        normalized_en_names[zh_name] = exact_matches[0]\n", "    else:\n", "        # 否则，使用第一个英文名与 qsdf 进行模糊匹配\n", "        first_en_name = en_names[0]\n", "        best_match, _, _ = process.extractOne(first_en_name, qsdf['Name'])\n", "        normalized_en_names[zh_name] = best_match\n", "\n", "normalized_en_names"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["替换后中文名对应多个英文名的情况:\n"]}], "source": ["# 替换 df 中的英文名\n", "df['学校英文名'] = df.apply(lambda row: normalized_en_names.get(row['学校中文名'], row['学校英文名']), axis=1)\n", "\n", "# 再次核查一对多的情况\n", "one_to_many_zh_after = df.groupby('学校中文名')['学校英文名'].nunique()\n", "many_zh_names_after = one_to_many_zh_after[one_to_many_zh_after > 1].index.tolist()\n", "\n", "print(\"替换后中文名对应多个英文名的情况:\")\n", "for zh_name in many_zh_names_after:\n", "    print(f\"{zh_name}: {', '.join(df[df['学校中文名'] == zh_name]['学校英文名'].unique())}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 英文名一对多处理"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["学校中文名\n", "纽卡斯尔大学        106\n", "纽卡斯尔大学（英国）     23\n", "Name: count, dtype: int64\n", "学校中文名\n", "西北大学（美国）    33\n", "西北大学         5\n", "Name: count, dtype: int64\n", "学校中文名\n", "约克大学（英国）    125\n", "约克大学         63\n", "Name: count, dtype: int64\n"]}], "source": ["# 检查英文名对应多个中文名的情况\n", "one_to_many_en = df.groupby('学校英文名')['学校中文名'].nunique()\n", "many_en_names = one_to_many_en[one_to_many_en > 1].index.tolist()\n", "\n", "print(df[df['学校英文名'] == 'Newcastle University'].学校中文名.value_counts())\n", "print(df[df['学校英文名'] == 'Northwestern University'].学校中文名.value_counts())\n", "print(df[df['学校英文名'] == 'University of York'].学校中文名.value_counts())"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "英文名对应多个中文名的情况:\n"]}], "source": ["# 人工选择合适的规范中文名称\n", "normalized_cn_names = {'Newcastle University':'纽卡斯尔大学（英国）','Northwestern University':'西北大学（美国）', 'University of York':'约克大学（英国）'}\n", "\n", "# 替换 df 中的中文名\n", "df['学校中文名'] = df.apply(lambda row: normalized_cn_names.get(row['学校英文名'], row['学校中文名']), axis=1)\n", "\n", "# 检查英文名对应多个中文名的情况\n", "one_to_many_en_after = df.groupby('学校英文名')['学校中文名'].nunique()\n", "many_en_names_after = one_to_many_en_after[one_to_many_en_after > 1].index.tolist()\n", "\n", "print(\"\\n英文名对应多个中文名的情况:\")\n", "for en_name in many_en_names_after:\n", "    print(f\"{en_name}: {', '.join(df[df['学校英文名'] == en_name]['学校中文名'].unique())}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 其他院校名称与规范名匹配"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'Nanyang Technological University': 'Nanyang Technological University, Singapore (NTU)',\n", " 'National University of Singapore': 'National University of Singapore (NUS)',\n", " 'Lingnan University': 'Jiangnan University',\n", " 'The Chinese University of Hong Kong': 'The University of Hong Kong',\n", " 'HongKong Baptist University': 'Hong Kong Baptist University',\n", " 'The Education University of Hong Kong': 'The University of Hong Kong',\n", " 'University of Exeter': 'The University of Exeter',\n", " 'The London School of Economics and Political Science': 'The London School of Economics and Political Science (LSE)',\n", " 'University College London': 'University of California, Berkeley (UCB)',\n", " 'University of East Anglia': 'University of East Anglia (UEA)',\n", " 'University of Dundee': ' University of Dundee',\n", " 'University of Buckingham': 'University of Birmingham',\n", " 'Australian National University': 'The Australian National University',\n", " 'London Business School': 'The London School of Economics and Political Science (LSE)',\n", " 'Macao Polytechnic University': 'Harvard University',\n", " 'City University of Macau': 'University of Macau',\n", " 'University of Essex': 'Essex, University of',\n", " 'Cranfield University': 'National University of Singapore (NUS)',\n", " 'Massachusetts Institute of Technology': 'Massachusetts Institute of Technology (MIT) ',\n", " '<PERSON>': 'Queen Mary University of London',\n", " 'Santa Clara University': 'National University of Singapore (NUS)',\n", " 'Fordham University': 'Fordham University ',\n", " 'Bentley University': 'National University of Singapore (NUS)',\n", " 'New York University': 'New York University (NYU)',\n", " 'University of North Carolina at Chapel Hill': 'University of North Carolina, Chapel Hill',\n", " 'Bernard <PERSON> College, CUNY': 'Trinity College Dublin, The University of Dublin',\n", " 'Rutgers University-New Brunswick': 'Rutgers University–New Brunswick',\n", " 'HongKong Metropolitan University': 'London Metropolitan University',\n", " 'Hong<PERSON>ong S hueY an University': 'Harvard University',\n", " 'The Hang Seng University of Hong Kong': 'The University of Hong Kong',\n", " 'University of California, Santa Barbara': 'University of California, Santa Barbara (UCSB)',\n", " 'University of California—Riverside': 'University of California, Riverside',\n", " 'Ohio State University': 'The Ohio State University',\n", " 'Rutgers University-Newark': 'Rutgers University–Newark',\n", " 'California Institute of Technology': 'California Institute of Technology (Caltech)',\n", " 'University of Georgia': 'The University of Georgia',\n", " 'Pepperdine University': 'IE University',\n", " 'Brigham Young University-Provo': 'Brigham Young University',\n", " 'University of California Santa Cruz': 'University of California, Santa Cruz',\n", " 'Royal Melbourne Institute of Technology': 'KTH Royal Institute of Technology '}"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["# 创建一个字典来存储需要规范化的新英文名及其对应的规范英文名\n", "all_normalized_names = {}\n", "\n", "# 获取 df 中所有唯一的英文名\n", "all_en_names = df['学校英文名'].unique().tolist()\n", "\n", "for en_name in all_en_names:\n", "    # 检查当前英文名是否在 qsdf 中\n", "    if en_name not in qsdf['Name'].values:\n", "        # 如果不在，则使用模糊匹配找到最相似的规范英文名\n", "        best_match, _, _= process.extractOne(en_name, qsdf['Name'])\n", "        all_normalized_names[en_name] = best_match\n", "\n", "all_normalized_names"]}, {"cell_type": "markdown", "metadata": {}, "source": ["手动检查修改（或许不该改名，而是加一列QS name？毕竟qs也不是唯一规范命名...有些加个简写有些很奇怪...而且还要指明年份...，官网名字和excel表的名字还不一样...）"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# all_normalized_names_revised = { （此处是记录手动修改的原因，修改结果见后）\n", "#     'Nanyang Technological University': 'Nanyang Technological University, Singapore (NTU)',\n", "#     'National University of Singapore': 'National University of Singapore (NUS)',\n", "#     'Lingnan University': 'Jiangnan University',  匹配错误\n", "#     'The Chinese University of Hong Kong': 'The University of Hong Kong', 匹配错误\n", "#     'HongKong Baptist University': 'Hong Kong Baptist University',\n", "#     'The Education University of Hong Kong': 'The University of Hong Kong',  无qs排名，去掉...\n", "#     'University of Exeter': 'The University of Exeter',\n", "#     'The London School of Economics and Political Science': 'The London School of Economics and Political Science (LSE)',\n", "#     'University College London': 'University of California, Berkeley (UCB)', 匹配错误...\n", "#     'University of East Anglia': 'University of East Anglia (UEA)',\n", "#     'University of Dundee': ' University of Dundee', qs下载下来的表前面有空格...本身名字没问题, 去掉...\n", "#     'University of Buckingham': 'University of Birmingham', 无qs排名，去掉...\n", "#     'Australian National University': 'The Australian National University',\n", "#     'London Business School': 'The London School of Economics and Political Science (LSE)', 无qs排名，去掉...\n", "#     'Macao Polytechnic University': 'Harvard University',无qs排名，去掉...\n", "#     'City University of Macau': 'University of Macau',无qs排名，去掉...\n", "#     'University of Essex': 'Essex, University of', 难蚌qs的名字...\n", "#     'Cranfield University': 'National University of Singapore (NUS)',无qs排名，去掉...\n", "#     'Massachusetts Institute of Technology': 'Massachusetts Institute of Technology (MIT) ',\n", "#     '<PERSON>': 'Queen <PERSON> University of London', 匹配错误...\n", "#     'Santa Clara University': 'National University of Singapore (NUS)', 无qs排名，去掉...\n", "#     'Fordham University': 'Fordham University ', qs下载下来的表后面有空格...本身名字没问题, 去掉...\n", "#     'Bentley University': 'National University of Singapore (NUS)', 无qs排名，去掉...\n", "#     'New York University': 'New York University (NYU)',\n", "#     'University of North Carolina at Chapel Hill': 'University of North Carolina, Chapel Hill',\n", "#     'Bernard <PERSON>, CUNY': 'Trinity College Dublin, The University of Dublin',无qs排名，去掉..\n", "#     'Rutgers University-New Brunswick': 'Rutgers University–New Brunswick', 这个-的细微区别...\n", "#     'HongKong Metropolitan University': 'London Metropolitan University', 无qs排名，去掉...\n", "#     'HongKong S hueY an University': 'Harvard University', 无qs排名，去掉... 但本身名字有问题？...\n", "#     'The Hang Seng University of Hong Kong': 'The University of Hong Kong', 无qs排名，去掉... \n", "#     'University of California, Santa Barbara': 'University of California, Santa Barbara (UCSB)',\n", "#     'University of California—Riverside': 'University of California, Riverside',\n", "#     'Ohio State University': 'The Ohio State University',\n", "#     'Rutgers University-Newark': 'Rutgers University–Newark',\n", "#     'California Institute of Technology': 'California Institute of Technology (Caltech)',\n", "#     'University of Georgia': 'The University of Georgia',\n", "#     'Pepperdine University': 'IE University', 无qs排名，去掉... \n", "#     'Brigham Young University-Provo': 'Brigham Young University',\n", "#     'University of California Santa Cruz': 'University of California, Santa Cruz',\n", "#     'Royal Melbourne Institute of Technology': 'KTH Royal Institute of Technology ' 匹配错误 不过kth这个也是excel表中多出来了空格...\n", "# }\n"]}, {"cell_type": "code", "execution_count": 84, "metadata": {}, "outputs": [], "source": ["all_normalized_names_revised = {\n", "    'Nanyang Technological University': 'Nanyang Technological University, Singapore (NTU)',\n", "    'National University of Singapore': 'National University of Singapore (NUS)',\n", "    'Lingnan University': 'Lingnan University, Hong Kong',\n", "    'The Chinese University of Hong Kong': 'The Chinese University of Hong Kong (CUHK)',\n", "    'HongKong Baptist University': 'Hong Kong Baptist University',\n", "    'University of Exeter': 'The University of Exeter',\n", "    'The London School of Economics and Political Science': 'The London School of Economics and Political Science (LSE)',\n", "    'University College London': 'UCL',\n", "    'University of East Anglia': 'University of East Anglia (UEA)',\n", "    'Australian National University': 'The Australian National University',\n", "    'University of Essex': 'Essex, University of',\n", "    'Massachusetts Institute of Technology': 'Massachusetts Institute of Technology (MIT) ',\n", "    'William Mary': 'College of William and Mary',\n", "    'New York University': 'New York University (NYU)',\n", "    'University of North Carolina at Chapel Hill': 'University of North Carolina, Chapel Hill',\n", "    'Rutgers University-New Brunswick': 'Rutgers University–New Brunswick',\n", "    'HongKong Metropolitan University': 'Hong Kong Metropolitan University',\n", "    'HongKong S hueY an University': 'Hong Kong Shue Yan University',\n", "    'University of California, Santa Barbara': 'University of California, Santa Barbara (UCSB)',\n", "    'University of California—Riverside': 'University of California, Riverside',\n", "    'Ohio State University': 'The Ohio State University',\n", "    'Rutgers University-Newark': 'Rutgers University–Newark',\n", "    'California Institute of Technology': 'California Institute of Technology (Caltech)',\n", "    'University of Georgia': 'The University of Georgia',\n", "    'Brigham Young University-Provo': 'Brigham Young University',\n", "    'University of California Santa Cruz': 'University of California, Santa Cruz',\n", "    'Royal Melbourne Institute of Technology': 'RMIT University'\n", "}\n", "\n", "df['学校英文名(QS25)'] = df['学校英文名'].apply(lambda en_name: all_normalized_names_revised.get(en_name, en_name))"]}, {"cell_type": "code", "execution_count": 85, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["无法在 qsdf 中找到对应学校名的学校英文名:\n"]}, {"data": {"text/plain": ["['The Education University of Hong Kong',\n", " 'University of Buckingham',\n", " 'London Business School',\n", " 'Macao Polytechnic University',\n", " 'City University of Macau',\n", " 'Cranfield University',\n", " 'Santa Clara University',\n", " 'Bentley University',\n", " 'Bernard <PERSON> College, CUNY',\n", " 'Hong Kong Metropolitan University',\n", " 'Hong Kong Shue Yan University',\n", " 'The Hang Seng University of Hong Kong',\n", " 'Pepperdine University']"]}, "execution_count": 85, "metadata": {}, "output_type": "execute_result"}], "source": ["# 进行左连接\n", "merged_df = pd.merge(df, qsdf, left_on='学校英文名(QS25)', right_on='Name', how='left')\n", "\n", "# 检查哪些 '学校英文名(QS25)' 无法在 qsdf 中找到对应的学校名\n", "unmatched_rows = merged_df[merged_df['Name'].isna()]\n", "\n", "# 获取无法匹配的学校英文名\n", "unmatched_school_english_names = unmatched_rows['学校英文名(QS25)'].unique().tolist()\n", "\n", "print(\"无法在 qsdf 中找到对应学校名的学校英文名:\")\n", "unmatched_school_english_names"]}, {"cell_type": "code", "execution_count": 89, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>学校中文名</th>\n", "      <th>学校英文名</th>\n", "      <th>专业中文名</th>\n", "      <th>专业英文名</th>\n", "      <th>专业大类</th>\n", "      <th>专业方向</th>\n", "      <th>所在学院</th>\n", "      <th>入学时间</th>\n", "      <th>项目时长</th>\n", "      <th>项目官网</th>\n", "      <th>培养目标</th>\n", "      <th>申请要求</th>\n", "      <th>语言要求</th>\n", "      <th>申请时间</th>\n", "      <th>课程设置</th>\n", "      <th>专业代码</th>\n", "      <th>项目学费</th>\n", "      <th>学校英文名(QS25)</th>\n", "      <th>学校排名(QS25)</th>\n", "      <th>学校所在地区</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>香港科技大学</td>\n", "      <td>The Hong Kong University of Science and Techno...</td>\n", "      <td>化学与能源工程理学硕士</td>\n", "      <td>MSc Chemical and Energy Engineering</td>\n", "      <td>工科</td>\n", "      <td>化工</td>\n", "      <td>工程学院</td>\n", "      <td>2/9月</td>\n", "      <td>1年</td>\n", "      <td>https://prog-crs.hkust.edu.hk/pgprog/2025-26/m...</td>\n", "      <td>香港科技大学化学与能源工程理学硕士项目的主要课程由能源、环境和纳米技术三大块组成，这是化学工...</td>\n", "      <td>具有学士学位或同等学历并且达到二等荣誉学位，需要化学工程、能源工程或相关领域背景不具备化学工...</td>\n", "      <td>雅思总分要求6.5听力5.5阅读5.5写作5.5口语5.5托福总分要求80听力/阅读/写作/口语/</td>\n", "      <td>25年春季入学开放申请2024-09-02截止申请2024-11-01；25年秋季入学开放申...</td>\n", "      <td>该项目分为一年全日制和两年非全日制,最低需修满30学分方可毕业，基础课程最低需修满12学分，...</td>\n", "      <td>1526</td>\n", "      <td>184800港币/年</td>\n", "      <td>The Hong Kong University of Science and Techno...</td>\n", "      <td>47</td>\n", "      <td>Hong Kong SAR</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>香港科技大学</td>\n", "      <td>The Hong Kong University of Science and Techno...</td>\n", "      <td>土木基建工程与管理理学硕士</td>\n", "      <td>MSc Civil Infrastructural Engineering and Mana...</td>\n", "      <td>工科</td>\n", "      <td>土木工程</td>\n", "      <td>工程学院</td>\n", "      <td>2/9月</td>\n", "      <td>1年</td>\n", "      <td>https://prog-crs.hkust.edu.hk/pgprog/2025-26/p...</td>\n", "      <td>香港科技大学土木基建工程及管理理学硕士课程为学员提供理论与实践并重的土木工程及管理课程，专为...</td>\n", "      <td>具有学士学位或同等学历并且达到二等荣誉学位，需要土木工程或相关工程领域背景。拥有非土木工程学...</td>\n", "      <td>雅思总分要求6.5听力5.5阅读5.5写作5.5口语5.5托福总分要求80听力/阅读/写作/口语/</td>\n", "      <td>25年春季入学开放申请2024-09-02截止申请2024-11-01;25年秋季入学开放申...</td>\n", "      <td>该项目提供七个细分方向：环境工程、岩土工程、基建系统工程与管理、材料工程、结构工程、运输工程...</td>\n", "      <td>1527</td>\n", "      <td>186900港币/年</td>\n", "      <td>The Hong Kong University of Science and Techno...</td>\n", "      <td>47</td>\n", "      <td>Hong Kong SAR</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    学校中文名                                              学校英文名          专业中文名  \\\n", "0  香港科技大学  The Hong Kong University of Science and Techno...    化学与能源工程理学硕士   \n", "1  香港科技大学  The Hong Kong University of Science and Techno...  土木基建工程与管理理学硕士   \n", "\n", "                                               专业英文名 专业大类  专业方向  所在学院  入学时间  \\\n", "0                MSc Chemical and Energy Engineering   工科    化工  工程学院  2/9月   \n", "1  MSc Civil Infrastructural Engineering and Mana...   工科  土木工程  工程学院  2/9月   \n", "\n", "  项目时长                                               项目官网  \\\n", "0   1年  https://prog-crs.hkust.edu.hk/pgprog/2025-26/m...   \n", "1   1年  https://prog-crs.hkust.edu.hk/pgprog/2025-26/p...   \n", "\n", "                                                培养目标  \\\n", "0  香港科技大学化学与能源工程理学硕士项目的主要课程由能源、环境和纳米技术三大块组成，这是化学工...   \n", "1  香港科技大学土木基建工程及管理理学硕士课程为学员提供理论与实践并重的土木工程及管理课程，专为...   \n", "\n", "                                                申请要求  \\\n", "0  具有学士学位或同等学历并且达到二等荣誉学位，需要化学工程、能源工程或相关领域背景不具备化学工...   \n", "1  具有学士学位或同等学历并且达到二等荣誉学位，需要土木工程或相关工程领域背景。拥有非土木工程学...   \n", "\n", "                                                语言要求  \\\n", "0  雅思总分要求6.5听力5.5阅读5.5写作5.5口语5.5托福总分要求80听力/阅读/写作/口语/   \n", "1  雅思总分要求6.5听力5.5阅读5.5写作5.5口语5.5托福总分要求80听力/阅读/写作/口语/   \n", "\n", "                                                申请时间  \\\n", "0  25年春季入学开放申请2024-09-02截止申请2024-11-01；25年秋季入学开放申...   \n", "1  25年春季入学开放申请2024-09-02截止申请2024-11-01;25年秋季入学开放申...   \n", "\n", "                                                课程设置  专业代码        项目学费  \\\n", "0  该项目分为一年全日制和两年非全日制,最低需修满30学分方可毕业，基础课程最低需修满12学分，...  1526  184800港币/年   \n", "1  该项目提供七个细分方向：环境工程、岩土工程、基建系统工程与管理、材料工程、结构工程、运输工程...  1527  186900港币/年   \n", "\n", "                                         学校英文名(QS25) 学校排名(QS25)         学校所在地区  \n", "0  The Hong Kong University of Science and Techno...         47  Hong Kong SAR  \n", "1  The Hong Kong University of Science and Techno...         47  Hong Kong SAR  "]}, "execution_count": 89, "metadata": {}, "output_type": "execute_result"}], "source": ["merged_df.drop(columns=['2024RANK', 'Name', 'Region'], inplace=True)\n", "merged_df.rename(columns={'2025RANK': '学校排名(QS25)', 'Location': '学校所在地区'}, inplace=True)\n", "merged_df.head(2)"]}, {"cell_type": "code", "execution_count": 90, "metadata": {}, "outputs": [], "source": ["# merged_df.to_csv(\"专业数据库（含学校信息）.csv\", index=False, encoding='utf-8-sig')\n", "# merged_df = pd.read_csv(\"专业数据库（含学校信息）.csv\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 其他处理及信息列"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["学校所在地区\n", "United Kingdom    6521\n", "United States     1994\n", "Australia         1291\n", "Hong Kong SAR      596\n", "Singapore          244\n", "Macau SAR          111\n", "Name: count, dtype: int64"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["merged_df['学校所在地区'].value_counts() # 11012->10757，少了一些是None值，有些学校因为没有QS排名，所以没有地区信息，需要后续手动补充"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["新增列的统计信息：\n", "申请学位类型唯一值: ['硕士']\n", "绩点要求分布: 绩点要求\n", "70.0    7636\n", "60.0    3376\n", "Name: count, dtype: int64\n", "年开销预估值分布: 年开销预估值\n", "20-30万人民币    6521\n", "25-35万人民币    1994\n", "15-25万人民币    1790\n", "15-20万人民币     707\n", "Name: count, dtype: int64\n", "数据框形状: (11012, 24)\n"]}], "source": ["# 1. 将\"学校所在地区\"列的英文转换为中文\n", "region_en_to_cn = {\n", "    'United Kingdom': '英国',\n", "    'United States': '美国',\n", "    'Australia': '澳大利亚',\n", "    'Hong Kong SAR': '香港',\n", "    'Singapore': '新加坡',\n", "    'Macau SAR': '澳门'\n", "}\n", "\n", "# 转换地区名称\n", "merged_df['学校所在地区'] = merged_df['学校所在地区'].map(region_en_to_cn).fillna(merged_df['学校所在地区'])\n", "\n", "# 2. 添加\"申请学位类型\"列，全部设为\"硕士\"\n", "merged_df['申请学位类型'] = '硕士'\n", "\n", "# 3. 添加\"绩点要求\"列\n", "# 首先将学校排名转换为数值类型，处理非数值情况\n", "def convert_rank_to_numeric(rank_str):\n", "    \"\"\"将排名字符串转换为数值，处理非数值情况\"\"\"\n", "    if pd.isna(rank_str):\n", "        return None\n", "    try:\n", "        # 移除可能的非数字字符，只保留数字\n", "        rank_num = ''.join(filter(str.isdigit, str(rank_str)))\n", "        return int(rank_num) if rank_num else None\n", "    except:\n", "        return None\n", "\n", "# 转换排名并设置绩点要求\n", "merged_df['学校排名_数值'] = merged_df['学校排名(QS25)'].apply(convert_rank_to_numeric)\n", "merged_df['绩点要求'] = merged_df['学校排名_数值'].apply(\n", "    lambda x: 70.0 if x is not None and x <= 200 else 60.0\n", ")\n", "\n", "# 删除临时列\n", "merged_df.drop('学校排名_数值', axis=1, inplace=True)\n", "\n", "# 4. 添加\"年开销预估值\"列，根据地区设置不同值\n", "region_cost_mapping = {\n", "    '英国': '20-30万人民币',\n", "    '美国': '25-35万人民币', \n", "    '澳大利亚': '15-25万人民币',\n", "    '香港': '15-20万人民币',\n", "    '新加坡': '15-25万人民币',\n", "    '澳门': '15-20万人民币'\n", "}\n", "\n", "# 设置年开销预估值\n", "merged_df['年开销预估值'] = merged_df['学校所在地区'].map(region_cost_mapping).fillna('15-25万人民币')\n", "\n", "# 5. 添加\"留服认证\"列，暂时保持空值\n", "merged_df['留服认证'] = None\n", "\n", "# 查看新增列的情况\n", "print(\"新增列的统计信息：\")\n", "print(f\"申请学位类型唯一值: {merged_df['申请学位类型'].unique()}\")\n", "print(f\"绩点要求分布: {merged_df['绩点要求'].value_counts()}\")\n", "print(f\"年开销预估值分布: {merged_df['年开销预估值'].value_counts()}\")\n", "print(f\"数据框形状: {merged_df.shape}\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>学校中文名</th>\n", "      <th>学校英文名</th>\n", "      <th>专业中文名</th>\n", "      <th>专业英文名</th>\n", "      <th>专业大类</th>\n", "      <th>专业方向</th>\n", "      <th>所在学院</th>\n", "      <th>入学时间</th>\n", "      <th>项目时长</th>\n", "      <th>项目官网</th>\n", "      <th>...</th>\n", "      <th>课程设置</th>\n", "      <th>专业代码</th>\n", "      <th>项目学费</th>\n", "      <th>学校英文名(QS25)</th>\n", "      <th>学校排名(QS25)</th>\n", "      <th>学校所在地区</th>\n", "      <th>申请学位类型</th>\n", "      <th>绩点要求</th>\n", "      <th>年开销预估值</th>\n", "      <th>留服认证</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>香港科技大学</td>\n", "      <td>The Hong Kong University of Science and Techno...</td>\n", "      <td>化学与能源工程理学硕士</td>\n", "      <td>MSc Chemical and Energy Engineering</td>\n", "      <td>工科</td>\n", "      <td>化工</td>\n", "      <td>工程学院</td>\n", "      <td>2/9月</td>\n", "      <td>1年</td>\n", "      <td>https://prog-crs.hkust.edu.hk/pgprog/2025-26/m...</td>\n", "      <td>...</td>\n", "      <td>该项目分为一年全日制和两年非全日制,最低需修满30学分方可毕业，基础课程最低需修满12学分，...</td>\n", "      <td>1526</td>\n", "      <td>184800港币/年</td>\n", "      <td>The Hong Kong University of Science and Techno...</td>\n", "      <td>47</td>\n", "      <td>香港</td>\n", "      <td>硕士</td>\n", "      <td>70.0</td>\n", "      <td>15-20万人民币</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>香港科技大学</td>\n", "      <td>The Hong Kong University of Science and Techno...</td>\n", "      <td>土木基建工程与管理理学硕士</td>\n", "      <td>MSc Civil Infrastructural Engineering and Mana...</td>\n", "      <td>工科</td>\n", "      <td>土木工程</td>\n", "      <td>工程学院</td>\n", "      <td>2/9月</td>\n", "      <td>1年</td>\n", "      <td>https://prog-crs.hkust.edu.hk/pgprog/2025-26/p...</td>\n", "      <td>...</td>\n", "      <td>该项目提供七个细分方向：环境工程、岩土工程、基建系统工程与管理、材料工程、结构工程、运输工程...</td>\n", "      <td>1527</td>\n", "      <td>186900港币/年</td>\n", "      <td>The Hong Kong University of Science and Techno...</td>\n", "      <td>47</td>\n", "      <td>香港</td>\n", "      <td>硕士</td>\n", "      <td>70.0</td>\n", "      <td>15-20万人民币</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>香港科技大学</td>\n", "      <td>The Hong Kong University of Science and Techno...</td>\n", "      <td>电子工程理学硕士</td>\n", "      <td>MSc Electronic Engineering</td>\n", "      <td>工科</td>\n", "      <td>电气电子</td>\n", "      <td>工程学院</td>\n", "      <td>9月</td>\n", "      <td>1年</td>\n", "      <td>https://prog-crs.hkust.edu.hk/pgprog/2025-26/m...</td>\n", "      <td>...</td>\n", "      <td>全日制学习年限为1年，电子工程理学硕士（MSc）课程为学生教授最新的技术，这些技术正在改变我...</td>\n", "      <td>1528</td>\n", "      <td>208000港币/年</td>\n", "      <td>The Hong Kong University of Science and Techno...</td>\n", "      <td>47</td>\n", "      <td>香港</td>\n", "      <td>硕士</td>\n", "      <td>70.0</td>\n", "      <td>15-20万人民币</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>香港科技大学</td>\n", "      <td>The Hong Kong University of Science and Techno...</td>\n", "      <td>工程企业管理理学硕士</td>\n", "      <td>MSc Engineering Enterprise Management</td>\n", "      <td>工科</td>\n", "      <td>工程管理</td>\n", "      <td>工程学院</td>\n", "      <td>9月</td>\n", "      <td>1年</td>\n", "      <td>https://prog-crs.hkust.edu.hk/pgprog/2025-26/m...</td>\n", "      <td>...</td>\n", "      <td>HKUST的工程企业管理专业总共包括30个学分的课程，其中包括1个工程经理问题解决学分和29...</td>\n", "      <td>1529</td>\n", "      <td>241500港币/年</td>\n", "      <td>The Hong Kong University of Science and Techno...</td>\n", "      <td>47</td>\n", "      <td>香港</td>\n", "      <td>硕士</td>\n", "      <td>70.0</td>\n", "      <td>15-20万人民币</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>香港科技大学</td>\n", "      <td>The Hong Kong University of Science and Techno...</td>\n", "      <td>环境工程与管理理学硕士</td>\n", "      <td>MSc Environmental Engineering and Management</td>\n", "      <td>工科</td>\n", "      <td>环境工程</td>\n", "      <td>工程学院</td>\n", "      <td>2/9月</td>\n", "      <td>1年</td>\n", "      <td>https://prog-crs.hkust.edu.hk/pgprog/2025-26/p...</td>\n", "      <td>...</td>\n", "      <td>课程包括环境科学、环境工程、环境管理、环境政策、环境法律等领域的核心知识和技能培养。具体的课...</td>\n", "      <td>1530</td>\n", "      <td>186900港币/年</td>\n", "      <td>The Hong Kong University of Science and Techno...</td>\n", "      <td>47</td>\n", "      <td>香港</td>\n", "      <td>硕士</td>\n", "      <td>70.0</td>\n", "      <td>15-20万人民币</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11007</th>\n", "      <td>香港浸会大学</td>\n", "      <td>HongKong Baptist University</td>\n", "      <td>智能、弹性与可持续城市社会科学硕士</td>\n", "      <td>MSocSc Smart, Resilient and Sustainable Cities</td>\n", "      <td>社科</td>\n", "      <td>建筑</td>\n", "      <td>人文与社会科学学院</td>\n", "      <td>9月</td>\n", "      <td>1年</td>\n", "      <td>https://ar.hkbu.edu.hk/tpg-admissions/programm...</td>\n", "      <td>...</td>\n", "      <td>课程结构如下：必修课程（12学分），选修课程（15学分），总计：27学分。全部(12)必修课...</td>\n", "      <td>76361</td>\n", "      <td>210000港币/年</td>\n", "      <td>Hong Kong Baptist University</td>\n", "      <td>252</td>\n", "      <td>香港</td>\n", "      <td>硕士</td>\n", "      <td>60.0</td>\n", "      <td>15-20万人民币</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11008</th>\n", "      <td>新加坡科技设计大学</td>\n", "      <td>Singapore University of Technology and Design</td>\n", "      <td>科技与设计（教育中的人工智能与科技）理学硕士</td>\n", "      <td>MSc Technology and Design (AI and Technology i...</td>\n", "      <td>社科</td>\n", "      <td>教育</td>\n", "      <td>NaN</td>\n", "      <td>9月</td>\n", "      <td>1年</td>\n", "      <td>https://www.sutd.edu.sg/programme-listing/mtd-...</td>\n", "      <td>...</td>\n", "      <td>领导教育和组织发展LeadershipinEducationandOrganisationa...</td>\n", "      <td>76362</td>\n", "      <td>54500新币/年</td>\n", "      <td>Singapore University of Technology and Design</td>\n", "      <td>440</td>\n", "      <td>新加坡</td>\n", "      <td>硕士</td>\n", "      <td>60.0</td>\n", "      <td>15-25万人民币</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11009</th>\n", "      <td>新加坡科技设计大学</td>\n", "      <td>Singapore University of Technology and Design</td>\n", "      <td>科技与设计（建筑设计运算）理学硕士</td>\n", "      <td>MSc Technology and Design (Architectural Desig...</td>\n", "      <td>社科</td>\n", "      <td>建筑</td>\n", "      <td>NaN</td>\n", "      <td>9月</td>\n", "      <td>1年</td>\n", "      <td>https://www.sutd.edu.sg/programme-listing/mtd-...</td>\n", "      <td>...</td>\n", "      <td>设计创新InnovationbyDesign运算设计ComputationalDesign数...</td>\n", "      <td>76363</td>\n", "      <td>54500新币/年</td>\n", "      <td>Singapore University of Technology and Design</td>\n", "      <td>440</td>\n", "      <td>新加坡</td>\n", "      <td>硕士</td>\n", "      <td>60.0</td>\n", "      <td>15-25万人民币</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11010</th>\n", "      <td>新加坡科技设计大学</td>\n", "      <td>Singapore University of Technology and Design</td>\n", "      <td>科技与设计（可持续城市设计）理学硕士</td>\n", "      <td>MSc Technology and Design (Sustainable Urban D...</td>\n", "      <td>工科</td>\n", "      <td>其他工科</td>\n", "      <td>NaN</td>\n", "      <td>9月</td>\n", "      <td>1年</td>\n", "      <td>https://www.sutd.edu.sg/programme-listing/mtd-...</td>\n", "      <td>...</td>\n", "      <td>设计创新InnovationbyDesign智能可持续发展工作室SmartSustainab...</td>\n", "      <td>76364</td>\n", "      <td>54500新币/年</td>\n", "      <td>Singapore University of Technology and Design</td>\n", "      <td>440</td>\n", "      <td>新加坡</td>\n", "      <td>硕士</td>\n", "      <td>60.0</td>\n", "      <td>15-25万人民币</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11011</th>\n", "      <td>利物浦大学</td>\n", "      <td>University of Liverpool</td>\n", "      <td>公共管理与政策文学硕士</td>\n", "      <td>MA Public Administration and Policy</td>\n", "      <td>社科</td>\n", "      <td>公共政策与事务</td>\n", "      <td>历史、语言与文化学院</td>\n", "      <td>9月</td>\n", "      <td>1年</td>\n", "      <td>https://www.liverpool.ac.uk/courses/public-adm...</td>\n", "      <td>...</td>\n", "      <td>全部(19)必修课程(6)选修课程(12)公共管理与政策研究技能和方法PublicAdmin...</td>\n", "      <td>76365</td>\n", "      <td>24100英镑/年</td>\n", "      <td>University of Liverpool</td>\n", "      <td>165</td>\n", "      <td>英国</td>\n", "      <td>硕士</td>\n", "      <td>70.0</td>\n", "      <td>20-30万人民币</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>11012 rows × 24 columns</p>\n", "</div>"], "text/plain": ["           学校中文名                                              学校英文名  \\\n", "0         香港科技大学  The Hong Kong University of Science and Techno...   \n", "1         香港科技大学  The Hong Kong University of Science and Techno...   \n", "2         香港科技大学  The Hong Kong University of Science and Techno...   \n", "3         香港科技大学  The Hong Kong University of Science and Techno...   \n", "4         香港科技大学  The Hong Kong University of Science and Techno...   \n", "...          ...                                                ...   \n", "11007     香港浸会大学                        HongKong Baptist University   \n", "11008  新加坡科技设计大学      Singapore University of Technology and Design   \n", "11009  新加坡科技设计大学      Singapore University of Technology and Design   \n", "11010  新加坡科技设计大学      Singapore University of Technology and Design   \n", "11011      利物浦大学                            University of Liverpool   \n", "\n", "                        专业中文名  \\\n", "0                 化学与能源工程理学硕士   \n", "1               土木基建工程与管理理学硕士   \n", "2                    电子工程理学硕士   \n", "3                  工程企业管理理学硕士   \n", "4                 环境工程与管理理学硕士   \n", "...                       ...   \n", "11007       智能、弹性与可持续城市社会科学硕士   \n", "11008  科技与设计（教育中的人工智能与科技）理学硕士   \n", "11009       科技与设计（建筑设计运算）理学硕士   \n", "11010      科技与设计（可持续城市设计）理学硕士   \n", "11011             公共管理与政策文学硕士   \n", "\n", "                                                   专业英文名 专业大类     专业方向  \\\n", "0                    MSc Chemical and Energy Engineering   工科       化工   \n", "1      MSc Civil Infrastructural Engineering and Mana...   工科     土木工程   \n", "2                             MSc Electronic Engineering   工科     电气电子   \n", "3                  MSc Engineering Enterprise Management   工科     工程管理   \n", "4           MSc Environmental Engineering and Management   工科     环境工程   \n", "...                                                  ...  ...      ...   \n", "11007     MSocSc Smart, Resilient and Sustainable Cities   社科       建筑   \n", "11008  MSc Technology and Design (AI and Technology i...   社科       教育   \n", "11009  MSc Technology and Design (Architectural Desig...   社科       建筑   \n", "11010  MSc Technology and Design (Sustainable Urban D...   工科     其他工科   \n", "11011                MA Public Administration and Policy   社科  公共政策与事务   \n", "\n", "             所在学院  入学时间 项目时长  \\\n", "0            工程学院  2/9月   1年   \n", "1            工程学院  2/9月   1年   \n", "2            工程学院    9月   1年   \n", "3            工程学院    9月   1年   \n", "4            工程学院  2/9月   1年   \n", "...           ...   ...  ...   \n", "11007   人文与社会科学学院    9月   1年   \n", "11008         NaN    9月   1年   \n", "11009         NaN    9月   1年   \n", "11010         NaN    9月   1年   \n", "11011  历史、语言与文化学院    9月   1年   \n", "\n", "                                                    项目官网  ...  \\\n", "0      https://prog-crs.hkust.edu.hk/pgprog/2025-26/m...  ...   \n", "1      https://prog-crs.hkust.edu.hk/pgprog/2025-26/p...  ...   \n", "2      https://prog-crs.hkust.edu.hk/pgprog/2025-26/m...  ...   \n", "3      https://prog-crs.hkust.edu.hk/pgprog/2025-26/m...  ...   \n", "4      https://prog-crs.hkust.edu.hk/pgprog/2025-26/p...  ...   \n", "...                                                  ...  ...   \n", "11007  https://ar.hkbu.edu.hk/tpg-admissions/programm...  ...   \n", "11008  https://www.sutd.edu.sg/programme-listing/mtd-...  ...   \n", "11009  https://www.sutd.edu.sg/programme-listing/mtd-...  ...   \n", "11010  https://www.sutd.edu.sg/programme-listing/mtd-...  ...   \n", "11011  https://www.liverpool.ac.uk/courses/public-adm...  ...   \n", "\n", "                                                    课程设置   专业代码        项目学费  \\\n", "0      该项目分为一年全日制和两年非全日制,最低需修满30学分方可毕业，基础课程最低需修满12学分，...   1526  184800港币/年   \n", "1      该项目提供七个细分方向：环境工程、岩土工程、基建系统工程与管理、材料工程、结构工程、运输工程...   1527  186900港币/年   \n", "2      全日制学习年限为1年，电子工程理学硕士（MSc）课程为学生教授最新的技术，这些技术正在改变我...   1528  208000港币/年   \n", "3      HKUST的工程企业管理专业总共包括30个学分的课程，其中包括1个工程经理问题解决学分和29...   1529  241500港币/年   \n", "4      课程包括环境科学、环境工程、环境管理、环境政策、环境法律等领域的核心知识和技能培养。具体的课...   1530  186900港币/年   \n", "...                                                  ...    ...         ...   \n", "11007  课程结构如下：必修课程（12学分），选修课程（15学分），总计：27学分。全部(12)必修课...  76361  210000港币/年   \n", "11008  领导教育和组织发展LeadershipinEducationandOrganisationa...  76362   54500新币/年   \n", "11009  设计创新InnovationbyDesign运算设计ComputationalDesign数...  76363   54500新币/年   \n", "11010  设计创新InnovationbyDesign智能可持续发展工作室SmartSustainab...  76364   54500新币/年   \n", "11011  全部(19)必修课程(6)选修课程(12)公共管理与政策研究技能和方法PublicAdmin...  76365   24100英镑/年   \n", "\n", "                                             学校英文名(QS25) 学校排名(QS25)  学校所在地区  \\\n", "0      The Hong Kong University of Science and Techno...         47      香港   \n", "1      The Hong Kong University of Science and Techno...         47      香港   \n", "2      The Hong Kong University of Science and Techno...         47      香港   \n", "3      The Hong Kong University of Science and Techno...         47      香港   \n", "4      The Hong Kong University of Science and Techno...         47      香港   \n", "...                                                  ...        ...     ...   \n", "11007                       Hong Kong Baptist University        252      香港   \n", "11008      Singapore University of Technology and Design        440     新加坡   \n", "11009      Singapore University of Technology and Design        440     新加坡   \n", "11010      Singapore University of Technology and Design        440     新加坡   \n", "11011                            University of Liverpool        165      英国   \n", "\n", "      申请学位类型  绩点要求     年开销预估值  留服认证  \n", "0         硕士  70.0  15-20万人民币  None  \n", "1         硕士  70.0  15-20万人民币  None  \n", "2         硕士  70.0  15-20万人民币  None  \n", "3         硕士  70.0  15-20万人民币  None  \n", "4         硕士  70.0  15-20万人民币  None  \n", "...      ...   ...        ...   ...  \n", "11007     硕士  60.0  15-20万人民币  None  \n", "11008     硕士  60.0  15-25万人民币  None  \n", "11009     硕士  60.0  15-25万人民币  None  \n", "11010     硕士  60.0  15-25万人民币  None  \n", "11011     硕士  70.0  20-30万人民币  None  \n", "\n", "[11012 rows x 24 columns]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["merged_df"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["# merged_df.to_csv(\"专业数据库.csv\", index=False, encoding='utf-8-sig')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 数据入库"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# df = pd.read_csv(\"专业数据库.csv\")"]}], "metadata": {"kernelspec": {"display_name": "tunshu_data", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}