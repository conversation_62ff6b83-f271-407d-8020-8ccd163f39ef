# TunshuEdu - 留学行业的AI工具箱

TunshuEdu (囤鼠科技教育平台) 是一个专为留学教育行业设计的综合AI工具平台，帮助留学顾问管理客户、撰写文书、规划申请策略等。

## 项目概述

本项目采用前后端分离架构：
- 前端：Vue 3 + Vite + TailwindCSS + Element Plus
- 后端：Python FastAPI + SQLAlchemy（异步）
- 数据库：PostgreSQL

## 系统要求

- Node.js 16+ (前端)
- Python 3.9+ (后端)
- PostgreSQL 12+ (数据库)

## 目录结构

```
tunshuedu/
├── frontend/                # 前端代码 (Vue 3 + Vite)
│   ├── public/              # 静态资源目录
│   │   └── logo.png         # 网站logo
│   ├── src/                 # 源代码目录
│   │   ├── api/             # 后端 API 请求封装
│   │   ├── assets/          # 静态资源
│   │   ├── components/      # 可复用的 Vue 组件
│   │   │   ├── common/      # 通用基础组件
│   │   │   └── layout/      # 布局相关组件
│   │   ├── router/          # 路由配置
│   │   │   └── modules/     # 路由模块
│   │   ├── stores/          # 状态管理 (Pinia)
│   │   ├── styles/          # 全局样式
│   │   ├── utils/           # 工具函数
│   │   ├── views/           # 页面级组件
│   │   │   ├── account/     # 账户管理页面
│   │   │   ├── ai-tools/    # AI工具页面
│   │   │   ├── auth/        # 认证相关页面
│   │   │   ├── clients/     # 客户管理页面
│   │   │   ├── dashboard/   # 仪表盘页面
│   │   │   ├── error/       # 错误页面
│   │   │   ├── planning/    # 申请规划页面
│   │   │   ├── school/      # 选校助手页面
│   │   │   └── writing/     # 文书写作页面
│   │   ├── App.vue          # Vue 应用根组件
│   │   ├── main.js          # 应用入口文件
│   │   └── style.css        # 全局样式文件
│   ├── auto-imports.d.ts    # 自动导入声明文件
│   ├── components.d.ts      # 组件类型声明文件
│   ├── index.html           # HTML 入口文件
│   ├── package.json         # Node.js 项目配置和依赖
│   ├── package-lock.json    # 锁定依赖版本
│   ├── postcss.config.cjs   # PostCSS 配置文件
│   ├── tailwind.config.js   # TailwindCSS 配置文件
│   └── vite.config.js       # Vite 配置文件
├── backend/                 # 后端代码 (Python FastAPI)
│   ├── app/                 # FastAPI 应用核心目录
│   │   ├── api/             # API 路由
│   │   │   ├── auth.py      # 认证相关 API 路由
│   │   │   ├── clients.py   # 客户相关 API 路由
│   │   │   ├── dashboard.py # 仪表盘相关 API 路由
│   │   │   └── __init__.py  # 初始化 API 路由
│   │   ├── core/            # 核心功能模块
│   │   │   ├── config.py    # 配置
│   │   │   ├── dependencies.py # 依赖注入
│   │   │   ├── security.py  # 安全相关功能
│   │   │   └── __init__.py  # 初始化核心模块
│   │   ├── db/              # 数据库相关
│   │   │   ├── database.py  # 数据库连接配置
│   │   │   └── __init__.py  # 初始化数据库模块
│   │   ├── models/          # SQLAlchemy 数据模型
│   │   │   ├── user.py      # 用户模型
│   │   │   └── __init__.py  # 初始化模型
│   │   ├── schemas/         # Pydantic 模型/schemas
│   │   │   ├── user.py      # 用户相关 schema
│   │   │   └── __init__.py  # 初始化 schemas
│   │   ├── utils/           # 后端工具函数
│   │   │   └── __init__.py  # 初始化工具模块
│   │   ├── main.py          # FastAPI 应用实例化
│   │   └── __init__.py      # 包初始化
│   ├── create_tables.sql    # 数据库表创建 SQL
│   ├── init_db.py           # 数据库初始化脚本
│   ├── main.py              # 应用启动入口脚本
│   └── requirements.txt     # Python 项目依赖
├── .cursor/                 # Cursor 编辑器配置
│   └── rules/               # Cursor 规则
├── .gitignore               # Git 忽略文件配置
├── .prettierrc              # Prettier 代码格式化配置
├── README.md                # 项目说明文档
├── start.sh                 # Linux/macOS 启动脚本 (启动前后端)
├── structure.md             # 项目结构详细说明
├── tsconfig.json            # TypeScript 配置文件
└── tsconfig.node.json       # Node.js TypeScript 配置
```

## 安装步骤

### 前置条件

1. 安装 Node.js: https://nodejs.org/ (16.x或更高版本)
2. 安装 Python: https://www.python.org/ (3.9或更高版本)
3. 安装 PostgreSQL: https://www.postgresql.org/ (12或更高版本)

### PostgreSQL 设置

1. 安装完 PostgreSQL 后，需要创建 postgres 超级用户（如果不存在）：

```bash
# macOS
createuser -s postgres

# Linux (需要 sudo 权限)
sudo -u postgres createuser -s postgres

# Windows (使用 psql 工具)
psql -U postgres
```

2. 创建数据库并初始化：

```bash
# 创建数据库
psql -U postgres -c "CREATE DATABASE tunshuedu_db;"

# 确保 plpgsql 扩展已安装
psql -U postgres -d tunshuedu_db -c "CREATE EXTENSION IF NOT EXISTS plpgsql;"

# 创建数据表
psql -U postgres -d tunshuedu_db -f create_tables.sql
```

如果执行 create_tables.sql 时遇到表已存在的错误，可以先删除已存在的表：

```bash
psql -U postgres -d tunshuedu_db -c "DROP TABLE IF EXISTS users CASCADE;"
```

### 后端设置

1. 创建Python虚拟环境:

```bash
# Linux/macOS
cd backend
python -m venv .venv
source .venv/bin/activate

# Windows
cd backend
python -m venv .venv
.venv\Scripts\activate
```

2. 安装Python依赖:

```bash
pip install -r requirements.txt
```

3. 配置后端环境变量:

直接在backend/app/config.py中修改，主要是username和password

```env
# 或创建 `.env` 文件在 `backend` 目录，添加以下内容:

# 数据库连接
DATABASE_URL=postgresql+asyncpg://username:password@localhost/tunshuedu

# 安全设置
SECRET_KEY=your_very_long_secret_key_here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# 其他设置
CORS_ORIGINS=http://localhost:3000
```

### 前端设置

1. 安装Node.js依赖:

```bash
cd frontend
npm install
```

2. 配置前端环境变量:

创建 `.env.development` 文件在 `frontend` 目录: （没有该文件？）

```env
VITE_API_URL=http://localhost:8000
```

## 启动项目

### 使用自动化脚本启动 (推荐)

#### Linux/macOS

```bash
# 给脚本添加执行权限
chmod +x start.sh start_frontend.sh start_backend.sh

# 启动前后端
./start.sh

# 或分别启动
./start_frontend.sh
./start_backend.sh
```

#### Windows

```bash
# 启动前后端
start.bat
```

### 手动启动

#### 启动后端

```bash
# 激活虚拟环境
cd backend
source .venv/bin/activate  # Linux/macOS
.venv\Scripts\activate     # Windows

# 启动FastAPI服务
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### 启动前端

```bash
cd frontend
npm run dev
```

## 访问应用

- 前端: http://localhost:3000
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs

## 默认账户

初始管理员账户:
- 用户名: admin
- 密码: admin123

## 主要功能

- **用户认证**
  - 登录、注册、刷新令牌
  - 权限管理和角色控制
  - 安全认证和会话管理

- **个人信息管理**
  - 查看和更新个人资料
  - 修改密码和安全设置
  - 个性化设置和偏好

- **客户管理**
  - 客户列表浏览和搜索
  - 添加和编辑客户信息
  - 客户标签和分类管理
  - 客户跟进记录

- **文书写作**
  - 推荐信生成和编辑
  - 个人陈述辅助写作
  - 简历模板和优化
  - 文书智能评分和建议

- **申请规划**
  - 选校助手和院校匹配
  - 申请时间线规划
  - 申请材料清单管理
  - 录取概率评估

- **AI工具**
  - AI率检测（识别AI生成内容）
  - AI率降低（优化文本自然度）
  - 智能润色和语法检查
  - 多语言翻译和本地化

- **学生背景提取**
  - 自动分析和提取学生背景信息
  - 关键经历和成就识别
  - 背景信息分类和标记
  - 生成结构化背景摘要
  - 提供背景优化建议

- **数据分析**
  - 客户数据统计和可视化
  - 申请成功率分析
  - 业务趋势和预测
  - 自定义报表生成

## 技术堆栈

### 前端
- Vue 3 (组合式API)
- TypeScript
- Vue Router (路由管理)
- Pinia (状态管理)
- Axios (HTTP客户端)
- TailwindCSS (样式框架)
- Element Plus (UI组件库)
- Vite (构建工具)
- SortableJS (拖拽排序)
- VueUse (组合式API工具集)
- Heroicons (图标库)

### 后端
- FastAPI (API框架)
- SQLAlchemy (ORM，使用异步模式)
- asyncpg (PostgreSQL异步驱动)
- Pydantic (数据验证)
- PyJWT (JWT认证)
- Uvicorn (ASGI服务器)
- Python 3.9+
- PostgreSQL 12+

## 贡献指南

1. Fork 仓库
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request

## 许可证

MIT License

## 联系方式

- 项目维护: 囤鼠科技团队
- 邮箱: <EMAIL>