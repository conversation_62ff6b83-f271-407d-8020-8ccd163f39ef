# 项目结构说明 (Project Structure Documentation)

本文件详细说明了囤鼠教育平台 (TunShu Education Platform) 的项目结构，包括前端和后端两部分。

## 根目录结构 (Root Directory Structure)

```
TunshuEdu/
├── backend/                 # 后端代码 (Python FastAPI)
│   ├── alembic.ini          # Alembic 配置文件
│   ├── app/                 # FastAPI 应用核心目录
│   │   ├── ai_selection/      # AI选校模块
│   │   │   ├── api/
│   │   │   │   ├── endpoints/
│   │   │   │   ├── __init__.py
│   │   │   │   └── router.py
│   │   │   ├── core/
│   │   │   │   ├── __init__.py
│   │   │   │   ├── candidate_pool.py
│   │   │   │   ├── program_matching.py
│   │   │   │   ├── ranking.py
│   │   │   │   ├── school_matching.py
│   │   │   │   └── user_profile.py
│   │   │   ├── db/
│   │   │   │   ├── __init__.py
│   │   │   │   ├── init_db.py
│   │   │   │   ├── models.py
│   │   │   │   └── seed.py
│   │   │   ├── schemas/
│   │   │   │   ├── __init__.py
│   │   │   │   ├── case.py
│   │   │   │   ├── program.py
│   │   │   │   ├── recommendation.py
│   │   │   │   └── user.py
│   │   │   ├── utils/
│   │   │   │   ├── __init__.py
│   │   │   │   ├── llm.py
│   │   │   │   └── rag.py
│   │   │   ├── __init__.py
│   │   │   └── README.md
│   │   ├── api/             # API 路由 (按功能模块划分)
│   │   │   ├── __init__.py
│   │   │   ├── auth.py
│   │   │   ├── clients.py
│   │   │   └── dashboard.py
│   │   ├── core/            # 核心功能模块
│   │   │   ├── __init__.py
│   │   │   ├── config.py
│   │   │   ├── dependencies.py
│   │   │   └── security.py
│   │   ├── db/              # 数据库相关
│   │   │   ├── __init__.py
│   │   │   └── database.py
│   │   ├── models/          # SQLAlchemy 数据模型
│   │   │   ├── __init__.py
│   │   │   ├── client.py
│   │   │   └── user.py
│   │   ├── schemas/         # Pydantic 模型/schemas
│   │   │   ├── __init__.py
│   │   │   ├── client.py
│   │   │   └── user.py
│   │   ├── utils/           # 后端工具函数
│   │   │   └── __init__.py
│   │   ├── __init__.py
│   │   └── main.py
│   ├── migrations/          # 数据库迁移脚本
│   │   ├── versions/
│   │   ├── README
│   │   ├── env.py
│   │   └── script.py.mako
│   ├── __init__.py
│   ├── create_tables.sql
│   ├── drop_tables.sql
│   ├── init_db.py
│   ├── main.py              # 应用启动入口脚本
│   └── requirements.txt     # Python 项目依赖
├── frontend/                # 前端代码 (Vue 3 + Vite)
│   ├── public/              # 静态资源目录
│   │   └── logo.png
│   ├── src/                 # 源代码目录
│   │   ├── api/
│   │   │   ├── account.js
│   │   │   ├── auth.js
│   │   │   ├── client.js
│   │   │   └── dashboard.js
│   │   ├── assets/
│   │   │   ├── logo.svg
│   │   │   └── vue.svg
│   │   ├── components/      # 可复用的 Vue 组件
│   │   │   ├── common/
│   │   │   │   ├── AnimatedInput.vue
│   │   │   │   └── Breadcrumb.vue
│   │   │   └── layout/
│   │   │       ├── Header.vue
│   │   │       ├── MainLayout.vue
│   │   │       └── Sidebar.vue
│   │   ├── router/          # 路由配置
│   │   │   ├── index.js
│   │   │   └── modules/
│   │   │       ├── account.js
│   │   │       ├── ai-tools.js
│   │   │       ├── auth.js
│   │   │       ├── clients.js
│   │   │       ├── dashboard.js
│   │   │       ├── error.js
│   │   │       ├── school.js
│   │   │       └── writing.js
│   │   ├── stores/          # 状态管理 (Pinia)
│   │   │   ├── auth.js
│   │   │   ├── clients.js
│   │   │   └── user.js
│   │   ├── styles/
│   │   │   └── index.css
│   │   ├── utils/           # 工具函数
│   │   │   ├── adapter.js
│   │   │   ├── auth.js
│   │   │   ├── format.js
│   │   │   └── request.js
│   │   ├── views/           # 页面级组件
│   │   │   ├── account/
│   │   │   │   ├── AccountSettings.vue
│   │   │   │   ├── Settings.vue
│   │   │   │   └── SubAccounts.vue
│   │   │   ├── ai-tools/
│   │   │   │   ├── AIDetector.vue
│   │   │   │   └── AIReducer.vue
│   │   │   ├── auth/
│   │   │   │   ├── ForgotPassword.vue
│   │   │   │   ├── Login.vue
│   │   │   │   └── Register.vue
│   │   │   ├── clients/
│   │   │   │   ├── ClientList.vue
│   │   │   │   └── ClientProfile.vue
│   │   │   ├── dashboard/
│   │   │   │   └── Dashboard.vue
│   │   │   ├── error/
│   │   │   │   └── NotFound.vue
│   │   │   ├── planning/
│   │   │   │   └── SchoolPlanning.vue
│   │   │   ├── school/
│   │   │   │   └── SchoolAssistant.vue
│   │   │   └── writing/
│   │   │       ├── CV.vue
│   │   │       ├── PS.vue
│   │   │       └── Recommendation.vue
│   │   ├── App.vue
│   │   ├── main.js
│   │   └── style.css
│   ├── auto-imports.d.ts
│   ├── components.d.ts
│   ├── index.html
│   ├── package.json
│   ├── postcss.config.cjs
│   ├── tailwind.config.js
│   └── vite.config.js
├── .cursor/
├── .gitignore
├── .prettierrc
├── README.md
├── start.sh
├── structure.md
├── tsconfig.json
└── tsconfig.node.json
```

## 前端结构 (Frontend Structure - `frontend/`)

前端使用 Vue 3 和 Vite 构建，采用模块化的方式组织代码。

```
frontend/
├── public/              # 静态资源目录 (会被直接复制到构建输出目录)
│   └── logo.png
├── src/                 # 主要源代码目录
│   ├── api/             # 后端 API 请求封装
│   │   ├── account.js
│   │   ├── auth.js
│   │   ├── client.js
│   │   └── dashboard.js
│   ├── assets/          # 静态资源 (会被 Vite 处理)
│   │   ├── logo.svg
│   │   └── vue.svg
│   ├── components/      # 可复用的 Vue 组件
│   │   ├── common/      # 通用基础组件
│   │   │   ├── AnimatedInput.vue
│   │   │   └── Breadcrumb.vue
│   │   └── layout/      # 布局相关组件 (Header, Sidebar等)
│   │       ├── Header.vue
│   │       ├── MainLayout.vue
│   │       └── Sidebar.vue
│   ├── router/          # 路由配置
│   │   ├── index.js
│   │   └── modules/     # 路由模块化配置
│   │       ├── account.js
│   │       ├── ai-tools.js
│   │       ├── auth.js
│   │       ├── clients.js
│   │       ├── dashboard.js
│   │       ├── error.js
│   │       ├── school.js
│   │       └── writing.js
│   ├── stores/          # 状态管理 (Pinia)
│   │   ├── auth.js
│   │   ├── clients.js
│   │   └── user.js
│   ├── styles/          # 全局样式
│   │   └── index.css
│   ├── utils/           # 工具函数
│   │   ├── adapter.js
│   │   ├── auth.js
│   │   ├── format.js
│   │   └── request.js
│   ├── views/           # 页面级组件 (路由对应的视图)
│   │   ├── account/     # 账户管理页面
│   │   │   ├── AccountSettings.vue
│   │   │   ├── Settings.vue
│   │   │   └── SubAccounts.vue
│   │   ├── ai-tools/    # AI工具页面
│   │   │   ├── AIDetector.vue
│   │   │   └── AIReducer.vue
│   │   ├── auth/        # 认证相关页面 (Login, Register)
│   │   │   ├── ForgotPassword.vue
│   │   │   ├── Login.vue
│   │   │   └── Register.vue
│   │   ├── clients/     # 客户管理页面
│   │   │   ├── ClientList.vue
│   │   │   └── ClientProfile.vue
│   │   ├── dashboard/   # 仪表盘页面
│   │   │   └── Dashboard.vue
│   │   ├── error/       # 错误页面
│   │   │   └── NotFound.vue
│   │   ├── planning/    # 申请规划页面
│   │   │   └── SchoolPlanning.vue
│   │   ├── school/      # 选校助手页面
│   │   │   └── SchoolAssistant.vue
│   │   └── writing/     # 文书写作页面
│   │       ├── CV.vue
│   │       ├── PS.vue
│   │       └── Recommendation.vue
│   ├── App.vue          # Vue 应用根组件
│   ├── main.js          # 应用入口文件 (初始化 Vue, Pinia, Router 等)
│   └── style.css        # 全局样式文件
├── auto-imports.d.ts    # 自动导入声明文件
├── components.d.ts      # 组件类型声明文件
├── index.html           # HTML 入口文件
├── package.json         # Node.js 项目配置和依赖
├── postcss.config.cjs   # PostCSS 配置文件
├── tailwind.config.js   # TailwindCSS 配置文件
└── vite.config.js       # Vite 配置文件 (构建、代理等)
```

**关键目录/文件说明:**

- `src/`: 存放所有前端源代码。
- `src/components/`: 存放可复用的 UI 组件。
- `src/views/`: 存放页面级组件，通常与路由对应。
- `src/router/`: 定义前端路由规则。
- `src/stores/`: 使用 Pinia 管理全局状态。
- `src/api/`: 封装与后端交互的 API 请求。
- `src/utils/`: 存放通用工具函数。
- `src/layouts/`: 定义不同页面的布局结构。
- `vite.config.js`: Vite 的核心配置文件，包括开发服务器、构建选项、插件和代理设置。
- `package.json`: 管理项目依赖和脚本命令。

## 后端结构 (Backend Structure - `backend/`)

后端使用 Python FastAPI 框架构建，采用模块化的方式组织API路由。

```
backend/
├── alembic.ini          # Alembic 配置文件
├── app/                 # FastAPI 应用核心目录
│   ├── ai_selection/      # AI选校模块
│   │   ├── api/
│   │   │   ├── endpoints/
│   │   │   ├── __init__.py
│   │   │   └── router.py
│   │   ├── core/
│   │   │   ├── __init__.py
│   │   │   ├── candidate_pool.py
│   │   │   ├── program_matching.py
│   │   │   ├── ranking.py
│   │   │   ├── school_matching.py
│   │   │   └── user_profile.py
│   │   ├── db/
│   │   │   ├── __init__.py
│   │   │   ├── init_db.py
│   │   │   ├── models.py
│   │   │   └── seed.py
│   │   ├── schemas/
│   │   │   ├── __init__.py
│   │   │   ├── case.py
│   │   │   ├── program.py
│   │   │   ├── recommendation.py
│   │   │   └── user.py
│   │   ├── utils/
│   │   │   ├── __init__.py
│   │   │   ├── llm.py
│   │   │   └── rag.py
│   │   ├── __init__.py
│   │   └── README.md
│   ├── api/             # API 路由 (按功能模块划分)
│   │   ├── __init__.py
│   │   ├── auth.py
│   │   ├── clients.py
│   │   └── dashboard.py
│   ├── core/            # 核心功能模块
│   │   ├── __init__.py
│   │   ├── config.py
│   │   ├── dependencies.py
│   │   └── security.py
│   ├── db/              # 数据库相关
│   │   ├── __init__.py
│   │   └── database.py
│   ├── models/          # SQLAlchemy 数据模型
│   │   ├── __init__.py
│   │   ├── client.py
│   │   └── user.py
│   ├── schemas/         # Pydantic 模型/schemas
│   │   ├── __init__.py
│   │   ├── client.py
│   │   └── user.py
│   │   ├── utils/           # 后端工具函数
│   │   │   └── __init__.py
│   │   ├── __init__.py
│   │   └── main.py
├── migrations/          # 数据库迁移脚本
│   ├── versions/
│   ├── README
│   ├── env.py
│   └── script.py.mako
├── __init__.py
├── create_tables.sql
├── drop_tables.sql
├── init_db.py
├── main.py              # 应用启动入口脚本
└── requirements.txt     # Python 项目依赖
```

**关键目录/文件说明:**

- `app/`: FastAPI 应用的核心代码。
- `app/api/`: 按功能模块组织的 API 路由。
- `app/models/`: 定义数据库模型 (使用 SQLAlchemy)。
- `app/schemas/`: 定义数据验证和序列化模式 (使用 Pydantic)。
- `app/core/`: 存放核心功能，如配置、安全等。
- `app/db/`: 数据库连接和会话管理。
- `app/utils/`: 工具函数和辅助方法。
- `app/dependencies.py`: 依赖注入函数，用于路由。
- `app/main.py`: 初始化和配置 FastAPI 应用实例。
- `main.py`: 应用启动入口点。
- `requirements.txt`: 列出项目所需的 Python 包。

## API 文档 (API Documentation)

所有 API 请求都需要在 Header 中包含有效的 JWT `Authorization: Bearer <token>` (除了 `/api/auth/login`, `/api/auth/register`, `/api/auth/refresh`)。

### 认证模块 (`/api/auth`)

- **POST /api/auth/register**
  - **描述**: 注册新用户。
  - **请求体**: `{"username": "string", "email": "string", "password": "string"}`
  - **成功响应 (201)**: `{"message": "注册成功", "access_token": "string", "refresh_token": "string", "user": {"id": integer, "username": "string", "email": "string", "nickname": "string|null", "role": "string", "created_at": "datetime", "last_login": "datetime|null", "is_active": boolean}}`
  - **错误响应 (400)**: `{"error": "缺少必要字段"}` 或 `{"error": "用户名已存在"}` 或 `{"error": "邮箱已存在"}`
  - **错误响应 (500)**: `{"error": "注册失败"}`

- **POST /api/auth/login**
  - **描述**: 用户登录。
  - **请求体**: `{"username": "string", "password": "string"}`
  - **成功响应 (200)**: `{"access_token": "string", "refresh_token": "string", "user": {"id": integer, "username": "string", "email": "string", "nickname": "string|null", "role": "string", "created_at": "datetime", "last_login": "datetime", "is_active": boolean}}`
  - **错误响应 (400)**: `{"error": "缺少必要字段"}`
  - **错误响应 (401)**: `{"error": "用户名或密码错误"}`
  - **错误响应 (403)**: `{"error": "账户已被禁用"}`

- **POST /api/auth/refresh**
  - **描述**: 使用 Refresh Token 刷新 Access Token。
  - **请求头**: `Authorization: Bearer <refresh_token>`
  - **成功响应 (200)**: `{"access_token": "string"}`

- **GET /api/auth/me**
  - **描述**: 获取当前登录用户信息。
  - **请求头**: `Authorization: Bearer <access_token>`
  - **成功响应 (200)**: `{"id": integer, "username": "string", "email": "string", "nickname": "string|null", "role": "string", "created_at": "datetime", "last_login": "datetime", "is_active": boolean}`
  - **错误响应 (404)**: `{"error": "用户不存在"}`

- **PUT /api/auth/update-profile**
  - **描述**: 更新当前登录用户的个人信息 (目前仅支持昵称)。
  - **请求头**: `Authorization: Bearer <access_token>`
  - **请求体**: `{"nickname": "string"}`
  - **成功响应 (200)**: `{"message": "个人信息更新成功", "user": { ... }}`
  - **错误响应 (404)**: `{"error": "用户不存在"}`
  - **错误响应 (500)**: `{"error": "更新失败"}`

- **POST /api/auth/change-password**
  - **描述**: 修改当前登录用户的密码。
  - **请求头**: `Authorization: Bearer <access_token>`
  - **请求体**: `{"old_password": "string", "new_password": "string"}`
  - **成功响应 (200)**: `{"message": "密码修改成功"}`
  - **错误响应 (400)**: `{"error": "缺少必要字段"}` 或 `{"error": "原密码错误"}`
  - **错误响应 (404)**: `{"error": "用户不存在"}`

### 仪表盘模块 (`/api/dashboard`)

- **GET /api/dashboard/**
  - **描述**: 获取仪表盘数据。
  - **请求头**: `Authorization: Bearer <access_token>`
  - **成功响应 (200)**: `{"message": "获取仪表盘数据成功", "data": {"total_clients": integer, "active_clients": integer, "recent_activities": []}}` (具体数据待实现)

### 客户模块 (`/api/clients`)

- **GET /api/clients/**
  - **描述**: 获取客户列表。
  - **请求头**: `Authorization: Bearer <access_token>`
  - **成功响应 (200)**: `{"message": "获取客户列表成功", "clients": []}` (具体数据待实现)