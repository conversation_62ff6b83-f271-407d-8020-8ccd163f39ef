# 项目结构说明 (Project Structure Documentation)

本文件详细说明了囤鼠教育平台 (TunShu Education Platform) 的项目结构，包括前端和后端两部分。

## 根目录结构 (Root Directory Structure)

```
tunshuedu/
├── frontend/                # 前端代码 (Vue 3 + Vite)
├── backend/                 # 后端代码 (Python FastAPI)
├── .cursor/                 # Cursor 编辑器配置
│   └── rules/               # Cursor 规则
├── .gitignore               # Git 忽略文件配置
├── .prettierrc              # Prettier 代码格式化配置
├── README.md                # 项目说明文档
├── start.sh                 # Linux/macOS 启动脚本 (启动前后端)
├── structure.md             # 本文件，项目结构说明
├── tsconfig.json            # TypeScript 配置文件
└── tsconfig.node.json       # Node.js TypeScript 配置
```

## 前端结构 (Frontend Structure - `frontend/`)

前端使用 Vue 3 和 Vite 构建，采用模块化的方式组织代码。

```
frontend/
├── public/              # 静态资源目录 (会被直接复制到构建输出目录)
│   └── logo.png         # 网站logo
├── src/                 # 主要源代码目录
│   ├── api/             # 后端 API 请求封装
│   ├── assets/          # 静态资源 (会被 Vite 处理)
│   ├── components/      # 可复用的 Vue 组件
│   │   ├── common/      # 通用基础组件
│   │   └── layout/      # 布局相关组件 (Header, Sidebar等)
│   ├── router/          # 路由配置
│   │   └── modules/     # 路由模块化配置
│   ├── stores/          # 状态管理 (Pinia)
│   ├── styles/          # 全局样式
│   ├── utils/           # 工具函数
│   ├── views/           # 页面级组件 (路由对应的视图)
│   │   ├── account/     # 账户管理页面
│   │   ├── ai-tools/    # AI工具页面
│   │   ├── auth/        # 认证相关页面 (Login, Register)
│   │   ├── clients/     # 客户管理页面
│   │   ├── dashboard/   # 仪表盘页面
│   │   ├── error/       # 错误页面
│   │   ├── planning/    # 申请规划页面
│   │   ├── school/      # 选校助手页面
│   │   └── writing/     # 文书写作页面
│   ├── App.vue          # Vue 应用根组件
│   ├── main.js          # 应用入口文件 (初始化 Vue, Pinia, Router 等)
│   └── style.css        # 全局样式文件
├── auto-imports.d.ts    # 自动导入声明文件
├── components.d.ts      # 组件类型声明文件
├── index.html           # HTML 入口文件
├── package.json         # Node.js 项目配置和依赖
├── package-lock.json    # 锁定依赖版本
├── postcss.config.cjs   # PostCSS 配置文件
├── tailwind.config.js   # TailwindCSS 配置文件
└── vite.config.js       # Vite 配置文件 (构建、代理等)
```

**关键目录/文件说明:**

- `src/`: 存放所有前端源代码。
- `src/components/`: 存放可复用的 UI 组件。
- `src/views/`: 存放页面级组件，通常与路由对应。
- `src/router/`: 定义前端路由规则。
- `src/stores/`: 使用 Pinia 管理全局状态。
- `src/api/`: 封装与后端交互的 API 请求。
- `src/utils/`: 存放通用工具函数。
- `src/layouts/`: 定义不同页面的布局结构。
- `vite.config.js`: Vite 的核心配置文件，包括开发服务器、构建选项、插件和代理设置。
- `package.json`: 管理项目依赖和脚本命令。

## 后端结构 (Backend Structure - `backend/`)

后端使用 Python FastAPI 框架构建，采用模块化的方式组织API路由。

```
backend/
├── app/                 # FastAPI 应用核心目录
│   ├── api/             # API 路由 (按功能模块划分)
│   │   ├── auth.py      # 认证相关 API 路由
│   │   ├── clients.py   # 客户相关 API 路由
│   │   ├── dashboard.py # 仪表盘相关 API 路由
│   │   └── __init__.py  # 初始化 API 路由
│   ├── core/            # 核心功能模块
│   │   ├── config.py    # 配置
│   │   ├── dependencies.py # 依赖注入
│   │   ├── security.py  # 安全相关功能
│   │   └── __init__.py  # 初始化核心模块
│   ├── db/              # 数据库相关
│   │   ├── database.py  # 数据库连接配置
│   │   └── __init__.py  # 初始化数据库模块
│   ├── models/          # SQLAlchemy 数据模型
│   │   ├── user.py      # User 模型
│   │   └── __init__.py  # 初始化模型
│   ├── schemas/         # Pydantic 模型/schemas
│   │   ├── user.py      # User 相关 schema
│   │   └── __init__.py  # 初始化 schemas
│   ├── utils/           # 后端工具函数
│   │   └── __init__.py  # 初始化工具模块
│   ├── main.py          # FastAPI 应用实例化
│   └── __init__.py      # 包初始化
├── create_tables.sql    # 数据库表创建 SQL
├── init_db.py           # 数据库初始化脚本
├── main.py              # 应用启动入口脚本
└── requirements.txt     # Python 项目依赖
```

**关键目录/文件说明:**

- `app/`: FastAPI 应用的核心代码。
- `app/api/`: 按功能模块组织的 API 路由。
- `app/models/`: 定义数据库模型 (使用 SQLAlchemy)。
- `app/schemas/`: 定义数据验证和序列化模式 (使用 Pydantic)。
- `app/core/`: 存放核心功能，如配置、安全等。
- `app/db/`: 数据库连接和会话管理。
- `app/utils/`: 工具函数和辅助方法。
- `app/dependencies.py`: 依赖注入函数，用于路由。
- `app/main.py`: 初始化和配置 FastAPI 应用实例。
- `main.py`: 应用启动入口点。
- `requirements.txt`: 列出项目所需的 Python 包。

## API 文档 (API Documentation)

所有 API 请求都需要在 Header 中包含有效的 JWT `Authorization: Bearer <token>` (除了 `/api/auth/login`, `/api/auth/register`, `/api/auth/refresh`)。

### 认证模块 (`/api/auth`)

- **POST /api/auth/register**
  - **描述**: 注册新用户。
  - **请求体**: `{"username": "string", "email": "string", "password": "string"}`
  - **成功响应 (201)**: `{"message": "注册成功", "access_token": "string", "refresh_token": "string", "user": {"id": integer, "username": "string", "email": "string", "nickname": "string|null", "role": "string", "created_at": "datetime", "last_login": "datetime|null", "is_active": boolean}}`
  - **错误响应 (400)**: `{"error": "缺少必要字段"}` 或 `{"error": "用户名已存在"}` 或 `{"error": "邮箱已存在"}`
  - **错误响应 (500)**: `{"error": "注册失败"}`

- **POST /api/auth/login**
  - **描述**: 用户登录。
  - **请求体**: `{"username": "string", "password": "string"}`
  - **成功响应 (200)**: `{"access_token": "string", "refresh_token": "string", "user": {"id": integer, "username": "string", "email": "string", "nickname": "string|null", "role": "string", "created_at": "datetime", "last_login": "datetime", "is_active": boolean}}`
  - **错误响应 (400)**: `{"error": "缺少必要字段"}`
  - **错误响应 (401)**: `{"error": "用户名或密码错误"}`
  - **错误响应 (403)**: `{"error": "账户已被禁用"}`

- **POST /api/auth/refresh**
  - **描述**: 使用 Refresh Token 刷新 Access Token。
  - **请求头**: `Authorization: Bearer <refresh_token>`
  - **成功响应 (200)**: `{"access_token": "string"}`

- **GET /api/auth/me**
  - **描述**: 获取当前登录用户信息。
  - **请求头**: `Authorization: Bearer <access_token>`
  - **成功响应 (200)**: `{"id": integer, "username": "string", "email": "string", "nickname": "string|null", "role": "string", "created_at": "datetime", "last_login": "datetime", "is_active": boolean}`
  - **错误响应 (404)**: `{"error": "用户不存在"}`

- **PUT /api/auth/update-profile**
  - **描述**: 更新当前登录用户的个人信息 (目前仅支持昵称)。
  - **请求头**: `Authorization: Bearer <access_token>`
  - **请求体**: `{"nickname": "string"}`
  - **成功响应 (200)**: `{"message": "个人信息更新成功", "user": { ... }}`
  - **错误响应 (404)**: `{"error": "用户不存在"}`
  - **错误响应 (500)**: `{"error": "更新失败"}`

- **POST /api/auth/change-password**
  - **描述**: 修改当前登录用户的密码。
  - **请求头**: `Authorization: Bearer <access_token>`
  - **请求体**: `{"old_password": "string", "new_password": "string"}`
  - **成功响应 (200)**: `{"message": "密码修改成功"}`
  - **错误响应 (400)**: `{"error": "缺少必要字段"}` 或 `{"error": "原密码错误"}`
  - **错误响应 (404)**: `{"error": "用户不存在"}`

### 仪表盘模块 (`/api/dashboard`)

- **GET /api/dashboard/**
  - **描述**: 获取仪表盘数据。
  - **请求头**: `Authorization: Bearer <access_token>`
  - **成功响应 (200)**: `{"message": "获取仪表盘数据成功", "data": {"total_clients": integer, "active_clients": integer, "recent_activities": []}}` (具体数据待实现)

### 客户模块 (`/api/clients`)

- **GET /api/clients/**
  - **描述**: 获取客户列表。
  - **请求头**: `Authorization: Bearer <access_token>`
  - **成功响应 (200)**: `{"message": "获取客户列表成功", "clients": []}` (具体数据待实现)